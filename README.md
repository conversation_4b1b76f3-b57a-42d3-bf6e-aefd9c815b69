# Yubi - An Emotionally Intelligent AI Companion

![<PERSON><PERSON>](/public/placeholder-logo.svg)

**Yubi** is an emotionally intelligent AI companion designed to help young people explore their identity through guided reflection and meaningful conversation.

## Project Overview

Our MVP offers:
- **Cinematic Experience**: A carefully crafted introduction to establish connection
- **Voice Interaction**: Natural voice conversations powered by ElevenLabs
- **Conversational Memory**: Remembers user responses through Supabase integration
- **Reflection Journey**: Guided questioning system using Google Gemini AI
- **Emotionally Safe Space**: No profiles, likes, or social pressure

> "<PERSON><PERSON> isn't a chatbot. It's a mirror that speaks."

## Features

### Core Interactions
- 🎙️ Voice-powered conversations
- 🤔 Identity exploration prompts
- 🔄 Response memory tracking
- 🎨 Themed visual experiences
- 🔒 Secure Google authentication

### Technical Highlights
- Real-time audio processing
- GSAP animations for smooth transitions
- Dynamic particle backgrounds
- Emotionally-aware response generation
- Progressively enhanced UI components

## Technology Stack

**Frontend**
- Next.js 14 (App Router)
- TypeScript
- Framer Motion
- GSAP
- shadcn/ui

**AI Services**
- Google Gemini (Text Generation)
- ElevenLabs (Voice Synthesis)

**Backend**
- Supabase (Auth & Database)
- Next.js API Routes

**Styling**
- Tailwind CSS
- CSS Modules
- PostCSS

## Getting Started

1. **Install dependencies**
```bash
npm install