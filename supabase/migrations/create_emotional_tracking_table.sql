-- Create emotional_tracking table
CREATE TABLE IF NOT EXISTS emotional_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  emotion VARCHAR(50) NOT NULL,
  intensity INTEGER NOT NULL CHECK (intensity BETWEEN 1 AND 10),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT
);

-- Create index for faster queries
CREATE INDEX emotional_tracking_user_id_idx ON emotional_tracking(user_id);
CREATE INDEX emotional_tracking_conversation_id_idx ON emotional_tracking(conversation_id);

-- Enable Row Level Security
ALTER TABLE emotional_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own emotional data"
  ON emotional_tracking FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own emotional data"
  ON emotional_tracking FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own emotional data"
  ON emotional_tracking FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own emotional data"
  ON emotional_tracking FOR DELETE
  USING (auth.uid() = user_id);
