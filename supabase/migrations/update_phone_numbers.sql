-- Add phone_number column to profiles table
ALTER TABLE profiles 
  ADD COLUMN phone_number TEXT,
  ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;

-- Add validation for proper phone number format
ALTER TABLE profiles 
  ADD CONSTRAINT phone_number_format CHECK (
    phone_number IS NULL OR 
    phone_number ~ '^\+[1-9]\d{1,14}$'
  );

-- Add comment explaining the format
COMMENT ON COLUMN profiles.phone_number IS 'Phone number in E.164 format (e.g., +12125551234)';
COMMENT ON COLUMN profiles.phone_verified IS 'Whether the phone number has been verified';

-- Create index for faster queries
CREATE INDEX profiles_phone_number_idx ON profiles(phone_number) WHERE phone_number IS NOT NULL;