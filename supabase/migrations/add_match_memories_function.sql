-- Create a function for matching memories by embedding similarity
CREATE OR <PERSON><PERSON>LACE FUNCTION match_memories(
  query_embedding vector(1536),
  match_user_id uuid,
  match_count int
)
RETURNS TABLE (
  id uuid,
  user_id uuid,
  content text,
  memory_type text,
  importance int,
  created_at timestamptz,
  embedding vector(1536),
  emotional_memory_context jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    me.id,
    me.user_id,
    me.content,
    me.memory_type,
    me.importance,
    me.created_at,
    me.embedding,
    me.emotional_memory_context,
    1 - (me.embedding <=> query_embedding) AS similarity
  FROM memory_embeddings me
  WHERE me.user_id = match_user_id
  ORDER BY me.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
