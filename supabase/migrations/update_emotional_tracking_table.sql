-- Add new columns to emotional_tracking table for real-time analysis
ALTER TABLE emotional_tracking 
  ADD COLUMN IF NOT EXISTS speech_pace FLOAT,
  ADD COLUMN IF NOT EXISTS speech_volume FLOAT,
  ADD COLUMN IF NOT EXISTS speech_tone FLOAT,
  ADD COLUMN IF NOT EXISTS emotional_keywords TEXT[],
  ADD COLUMN IF NOT EXISTS in_conversation_timestamp INTEGER;

-- Create index for faster queries on the new timestamp column
CREATE INDEX IF NOT EXISTS emotional_tracking_conversation_timestamp_idx 
  ON emotional_tracking(conversation_id, in_conversation_timestamp);

-- Comment on new columns
COMMENT ON COLUMN emotional_tracking.speech_pace IS 'Rate of speech (words per minute)';
COMMENT ON COLUMN emotional_tracking.speech_volume IS 'Volume level of speech (0-100)';
COMMENT ON COLUMN emotional_tracking.speech_tone IS 'Tone measurement (negative to positive scale)';
COMMENT ON COLUMN emotional_tracking.emotional_keywords IS 'Array of emotional keywords detected';
COMMENT ON COLUMN emotional_tracking.in_conversation_timestamp IS 'Timestamp in milliseconds from start of conversation';