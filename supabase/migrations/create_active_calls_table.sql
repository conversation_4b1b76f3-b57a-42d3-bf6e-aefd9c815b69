-- Create active_calls table
CREATE TABLE IF NOT EXISTS active_calls (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  phone_number TEXT NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL, -- 'active', 'completed', 'failed', etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for faster queries
CREATE INDEX IF NOT EXISTS active_calls_user_id_idx ON active_calls(user_id);
CREATE INDEX IF NOT EXISTS active_calls_conversation_id_idx ON active_calls(conversation_id);
CREATE INDEX IF NOT EXISTS active_calls_status_idx ON active_calls(status);