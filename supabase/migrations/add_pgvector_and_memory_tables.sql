-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create memory_embeddings table
CREATE TABLE IF NOT EXISTS memory_embeddings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536), -- Dimension depends on your embedding model
  metadata JSONB NOT NULL DEFAULT '{}',
  memory_type TEXT NOT NULL, -- 'explicit', 'conversation', 'emotional', etc.
  importance INTEGER NOT NULL DEFAULT 1 CHECK (importance BETWEEN 1 AND 10),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed_at TIMESTAMP WITH TIME ZONE
);

-- Create emotional_memory_context table to store additional emotional metadata
CREATE TABLE IF NOT EXISTS emotional_memory_context (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  memory_id UUID REFERENCES memory_embeddings(id) ON DELETE CASCADE NOT NULL,
  emotion TEXT,
  intensity INTEGER CHECK (intensity BETWEEN 1 AND 10),
  speech_pace FLOAT,
  speech_volume FLOAT,
  speech_tone FLOAT,
  emotional_keywords TEXT[]
);

-- Create indexes for faster queries
CREATE INDEX memory_embeddings_user_id_idx ON memory_embeddings(user_id);
CREATE INDEX memory_embeddings_memory_type_idx ON memory_embeddings(memory_type);
CREATE INDEX memory_embeddings_importance_idx ON memory_embeddings(importance);

-- Create vector index for similarity search
CREATE INDEX memory_embeddings_embedding_idx ON memory_embeddings USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Enable RLS
ALTER TABLE memory_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE emotional_memory_context ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own memories"
  ON memory_embeddings FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memories"
  ON memory_embeddings FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own emotional memory context"
  ON emotional_memory_context FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM memory_embeddings 
    WHERE memory_embeddings.id = emotional_memory_context.memory_id 
    AND memory_embeddings.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert their own emotional memory context"
  ON emotional_memory_context FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM memory_embeddings 
    WHERE memory_embeddings.id = emotional_memory_context.memory_id 
    AND memory_embeddings.user_id = auth.uid()
  ));