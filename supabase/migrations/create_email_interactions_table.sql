-- Create email_interactions table to track email activities separately from conversations
CREATE TABLE IF NOT EXISTS email_interactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  interaction_type TEXT NOT NULL, -- 'email_summary', 'email_send', 'email_reply'
  summary TEXT NOT NULL, -- Brief description of the action
  details JSONB, -- Detailed information about the email interaction
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE email_interactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own email interactions"
  ON email_interactions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own email interactions"
  ON email_interactions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own email interactions"
  ON email_interactions FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own email interactions"
  ON email_interactions FOR DELETE
  USING (auth.uid() = user_id);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_email_interactions_user_id ON email_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_email_interactions_type ON email_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_email_interactions_created_at ON email_interactions(created_at);
