-- Remove email interactions from conversations table
-- These should not be mixed with actual conversations

DELETE FROM conversations 
WHERE conversation_type IN ('email_summary', 'email_send', 'email_reply', 'email_interaction');

-- Also remove any conversations that contain email-related summaries
DELETE FROM conversations 
WHERE summary ILIKE '%email summary%' 
   OR summary ILIKE '%sent email%' 
   OR summary ILIKE '%replied to%'
   OR summary ILIKE '%email reply%';

-- Clean up any conversations with email topics that aren't real conversations
DELETE FROM conversations 
WHERE topics @> ARRAY['email'] 
  AND conversation_type != 'conversation'
  AND conversation_type != 'reflection'
  AND conversation_type != 'casual'
  AND conversation_type != 'deep_dive';
