-- Enable uuid-ossp extension for uuid_generate_v4()
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create multimodal memories table
CREATE TABLE IF NOT EXISTS multimodal_memories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  embedding vector(1000),
  memory_type VARCHAR(50) NOT NULL,
  importance INTEGER DEFAULT 5 CHECK (importance BETWEEN 1 AND 10),
  emotional_memory_context JSONB,
  image_url TEXT,
  audio_url TEXT,
  location TEXT,
  related_memory_ids UUID[],
  decay_factor DECIMAL(4,3) DEFAULT 1.0,
  activation_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create neural connections table
CREATE TABLE IF NOT EXISTS neural_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  source_memory_id UUID NOT NULL REFERENCES multimodal_memories(id) ON DELETE CASCADE,
  target_memory_id UUID NOT NULL REFERENCES multimodal_memories(id) ON DELETE CASCADE,
  weight DECIMAL(4,3) NOT NULL CHECK (weight BETWEEN 0 AND 1),
  emotional_valence DECIMAL(4,3),
  recency DECIMAL(4,3),
  interaction_count INTEGER DEFAULT 0,
  last_activated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(source_memory_id, target_memory_id)
);

-- Create memory activation history table
CREATE TABLE IF NOT EXISTS memory_activations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  memory_id UUID NOT NULL REFERENCES multimodal_memories(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  activation_context TEXT,
  activation_strength DECIMAL(4,3) CHECK (activation_strength BETWEEN 0 AND 1),
  emotional_state VARCHAR(50),
  context_embedding vector(1536),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX multimodal_memories_user_id_idx ON multimodal_memories(user_id);
CREATE INDEX multimodal_memories_memory_type_idx ON multimodal_memories(memory_type);
CREATE INDEX multimodal_memories_importance_idx ON multimodal_memories(importance);
CREATE INDEX neural_connections_source_idx ON neural_connections(source_memory_id);
CREATE INDEX neural_connections_target_idx ON neural_connections(target_memory_id);
CREATE INDEX memory_activations_memory_id_idx ON memory_activations(memory_id);

-- Create vector index for similarity search
CREATE INDEX multimodal_memories_embedding_idx ON multimodal_memories 
  USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Enable Row Level Security
ALTER TABLE multimodal_memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE neural_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE memory_activations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own memories"
  ON multimodal_memories FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memories"
  ON multimodal_memories FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own memories"
  ON multimodal_memories FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own neural connections"
  ON neural_connections FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM multimodal_memories 
    WHERE multimodal_memories.id = neural_connections.source_memory_id 
    AND multimodal_memories.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert their own neural connections"
  ON neural_connections FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM multimodal_memories 
    WHERE multimodal_memories.id = neural_connections.source_memory_id 
    AND multimodal_memories.user_id = auth.uid()
  ));

CREATE POLICY "Users can view their own memory activations"
  ON memory_activations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memory activations"
  ON memory_activations FOR INSERT
  WITH CHECK (auth.uid() = user_id);
