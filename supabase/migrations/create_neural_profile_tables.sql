-- Neural profile master table
CREATE TABLE IF NOT EXISTS neural_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Brain region activity tracking
CREATE TABLE IF NOT EXISTS brain_region_activity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES neural_profiles(id) ON DELETE CASCADE NOT NULL,
  region_name VARCHAR(100) NOT NULL,
  activity_level DECIMAL(4,2) NOT NULL CHECK (activity_level BETWEEN 0 AND 10),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id, region_name)
);

-- Cognitive function assessments
CREATE TABLE IF NOT EXISTS cognitive_functions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES neural_profiles(id) ON DELETE CASCADE NOT NULL,
  function_name VARCHAR(100) NOT NULL,
  strength_score DECIMAL(4,2) NOT NULL CHECK (strength_score BETWEEN 0 AND 10),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id, function_name)
);

-- Neural network connections
CREATE TABLE IF NOT EXISTS neural_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID REFERENCES neural_profiles(id) ON DELETE CASCADE NOT NULL,
  network_name VARCHAR(100) NOT NULL,
  connection_strength DECIMAL(4,2) NOT NULL CHECK (connection_strength BETWEEN 0 AND 10),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(profile_id, network_name)
);

-- Enable Row Level Security on all tables
ALTER TABLE neural_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE brain_region_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE cognitive_functions ENABLE ROW LEVEL SECURITY;
ALTER TABLE neural_connections ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for neural_profiles
CREATE POLICY "Users can view their own neural profile"
  ON neural_profiles FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own neural profile"
  ON neural_profiles FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for brain_region_activity
CREATE POLICY "Users can view their own brain region activity"
  ON brain_region_activity FOR SELECT
  USING ((SELECT user_id FROM neural_profiles WHERE id = brain_region_activity.profile_id) = auth.uid());

-- Create RLS policies for cognitive_functions
CREATE POLICY "Users can view their own cognitive functions"
  ON cognitive_functions FOR SELECT
  USING ((SELECT user_id FROM neural_profiles WHERE id = cognitive_functions.profile_id) = auth.uid());

-- Create RLS policies for neural_connections
CREATE POLICY "Users can view their own neural connections"
  ON neural_connections FOR SELECT
  USING ((SELECT user_id FROM neural_profiles WHERE id = neural_connections.profile_id) = auth.uid());
