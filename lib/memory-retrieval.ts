import { GoogleGenAI } from "@google/genai";
import { createClient } from '@supabase/supabase-js';
import { generateEmbedding } from "./embedding-service";

// Initialize Gemini client
const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY,
});

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL! as string;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

// Embedding model name
const EMBEDDING_MODEL = 'gemini-embedding-exp-03-07';

interface MemorySearchOptions {
  userId: string;
  query: string;
  memoryTypes?: string[];
  minImportance?: number;
  limit?: number;
  includeEmotionalContext?: boolean;
}

interface Memory {
  id: string;
  content: string;
  memoryType: string;
  importance: number;
  createdAt: string;
  emotionalContext?: {
    emotion?: string;
    intensity?: number;
    speechPace?: number;
    speechVolume?: number;
    speechTone?: number;
    emotionalKeywords?: string[];
  };
}

export async function searchMemories({
  userId,
  query,
  memoryTypes = ['explicit', 'conversation', 'emotional'],
  minImportance = 1,
  limit = 5,
  includeEmotionalContext = true
}: MemorySearchOptions): Promise<Memory[]> {
  try {
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);
    
    console.log("Generated query embedding of length:", queryEmbedding.length);
    
    // Search for similar memories
    let selectQuery = includeEmotionalContext 
      ? '*, emotional_memory_context(*)'
      : '*';
    
    let queryBuilder = supabase
      .from('memory_embeddings')
      .select(selectQuery)
      .eq('user_id', userId)
      .gte('importance', minImportance);
    
    // Add memory type filter if specified
    if (memoryTypes.length > 0) {
      queryBuilder = queryBuilder.in('memory_type', memoryTypes);
    }
    
    // Execute the query with vector similarity search
    console.log("Executing vector similarity search");
    const { data, error } = await queryBuilder
      .order(`embedding <-> '${JSON.stringify(queryEmbedding)}'`, { ascending: true })
      .limit(limit);
    
    if (error) {
      console.error("Error in vector similarity search:", error);
      throw error;
    }
    
    console.log(`Found ${data?.length || 0} similar memories`);
    
    // Update last_accessed_at for retrieved memories
    if (data && data.length > 0) {
      const memoryIds = data.map((memory: any) => memory.id);
      await supabase
        .from('memory_embeddings')
        .update({ last_accessed_at: new Date().toISOString() })
        .in('id', memoryIds);
    }
    
    // Format the results
    return (data || []).map((memory: any) => ({
      id: memory.id,
      content: memory.content,
      memoryType: memory.memory_type,
      importance: memory.importance,
      createdAt: memory.created_at,
      emotionalContext: includeEmotionalContext && memory.emotional_memory_context && memory.emotional_memory_context.length > 0
        ? {
            emotion: memory.emotional_memory_context[0].emotion,
            intensity: memory.emotional_memory_context[0].intensity,
            speechPace: memory.emotional_memory_context[0].speech_pace,
            speechVolume: memory.emotional_memory_context[0].speech_volume,
            speechTone: memory.emotional_memory_context[0].speech_tone,
            emotionalKeywords: memory.emotional_memory_context[0].emotional_keywords
          }
        : undefined
    }));
  } catch (error) {
    console.error("Error searching memories:", error);
    throw error;
  }
}
