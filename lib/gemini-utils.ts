/**
 * Utility functions for handling Gemini AI responses consistently
 */

export function extractTextFromGeminiResponse(response: any): string {
  // Try different possible response structures
  if (response.response && typeof response.response.text === 'function') {
    return response.response.text();
  }
  
  if (response.response && response.response.text) {
    return response.response.text;
  }
  
  if (response.text) {
    return response.text;
  }
  
  // Try to get text from candidates structure
  if (response.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
    return response.response.candidates[0].content.parts[0].text;
  }
  
  // Try alternative candidates structure
  if (response.candidates?.[0]?.content?.parts?.[0]?.text) {
    return response.candidates[0].content.parts[0].text;
  }
  
  // Last resort - try to find any text property
  if (response.response?.text) {
    return response.response.text;
  }
  
  return '';
}

export function safeParseJSON(text: string, fallback: any = null): any {
  try {
    return JSON.parse(text);
  } catch (error) {
    console.error('Failed to parse JSON:', error);
    console.error('Text that failed to parse:', text);
    return fallback;
  }
}
