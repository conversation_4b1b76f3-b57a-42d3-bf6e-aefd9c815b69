import { GoogleGenAI } from "@google/genai";
import { createClient } from '@supabase/supabase-js';

// Initialize Gemini client
const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY,
});

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL! as string;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

// Embedding model name
const EMBEDDING_MODEL = 'gemini-embedding-exp-03-07';

export interface EmotionalContext {
  emotion?: string;
  intensity?: number;
  speechPace?: number;
  speechVolume?: number;
  speechTone?: number;
  emotionalKeywords?: string[];
}

export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const response = await ai.models.embedContent({
      model: EMBEDDING_MODEL,
      contents: text,
    });
    
    if (!response.embeddings || !response.embeddings[0]) {
      throw new Error("Failed to generate embedding: No embedding returned");
    }
    
    return response.embeddings[0].values ? Array.from(response.embeddings[0].values) : [];
  } catch (error) {
    console.error("Error generating embedding:", error);
    throw error;
  }
}

export async function storeMemory(
  userId: string,
  content: string,
  memoryType: string,
  emotionalContext?: EmotionalContext,
  conversationId?: string,
  importance: number = 5
): Promise<string> {
  try {
    // Generate embedding for the content
    const embedding = await generateEmbedding(content);
    
    // Create metadata object with emotional context if provided
    const metadata: Record<string, any> = {};
    if (emotionalContext) {
      metadata.emotionalContext = emotionalContext;
    }
    
    console.log("Storing memory with embedding of length:", embedding.length);
    
    // Store the memory embedding
    const { data, error } = await supabase
      .from("memory_embeddings")
      .insert({
        user_id: userId,
        conversation_id: conversationId,
        content,
        embedding,
        metadata,
        memory_type: memoryType,
        importance: importance,
        created_at: new Date().toISOString(),
        last_accessed_at: new Date().toISOString()
      })
      .select('id')
      .single();
      
    if (error) {
      console.error("Error inserting into memory_embeddings:", error);
      throw error;
    }
    
    // If we have emotional context, store it in the emotional_memory_context table
    if (emotionalContext && data?.id) {
      console.log("Storing emotional context for memory:", data.id);
      const { error: emotionalError } = await supabase
        .from("emotional_memory_context")
        .insert({
          memory_id: data.id,
          emotion: emotionalContext.emotion,
          intensity: emotionalContext.intensity,
          speech_pace: emotionalContext.speechPace,
          speech_volume: emotionalContext.speechVolume,
          speech_tone: emotionalContext.speechTone,
          emotional_keywords: emotionalContext.emotionalKeywords
        });
        
      if (emotionalError) {
        console.error("Error inserting into emotional_memory_context:", emotionalError);
        throw emotionalError;
      }
    }
    
    return data.id;
  } catch (error) {
    console.error("Error storing memory:", error);
    throw error;
  }
}

// Function to handle explicit "remember this" requests
export async function storeExplicitMemory(
  userId: string,
  content: string,
  emotionalContext?: EmotionalContext,
  conversationId?: string
): Promise<string> {
  // For explicit memories, we set a higher importance (8)
  return storeMemory(
    userId,
    content,
    'explicit',
    emotionalContext,
    conversationId,
    8
  );
}

interface MultimodalMemory {
  textContent: string;
  imageUrl?: string;
  audioUrl?: string;
  emotionalContext?: EmotionalContext;
  timestamp: string;
  location?: string;
  relatedMemoryIds?: string[];
}

export async function storeMultimodalMemory(
  userId: string,
  memory: MultimodalMemory,
  memoryType: string,
  importance: number = 5
): Promise<string> {
  try {
    // Generate embedding for text content
    const textEmbedding = await generateEmbedding(memory.textContent);
    
    // If image is present, generate image embedding
    let combinedEmbedding = textEmbedding;
    if (memory.imageUrl) {
      // This would require an image embedding service
      // const imageEmbedding = await generateImageEmbedding(memory.imageUrl);
      // combinedEmbedding = combineEmbeddings(textEmbedding, imageEmbedding);
    }
    
    // Store the multimodal memory
    const { data, error } = await supabase
      .from('multimodal_memories')
      .insert({
        user_id: userId,
        content: memory.textContent,
        embedding: combinedEmbedding,
        memory_type: memoryType,
        importance: importance,
        emotional_memory_context: memory.emotionalContext,
        image_url: memory.imageUrl,
        audio_url: memory.audioUrl,
        location: memory.location,
        related_memory_ids: memory.relatedMemoryIds,
        created_at: memory.timestamp || new Date().toISOString()
      })
      .select('id');
      
    if (error) throw error;
    return data[0].id;
  } catch (error) {
    console.error("Error storing multimodal memory:", error);
    throw error;
  }
}
