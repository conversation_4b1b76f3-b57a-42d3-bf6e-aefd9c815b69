// Audio analyzer for real-time speech processing
import { emotionalKeywords } from './emotional-keywords';

// Types for audio analysis
export interface AudioAnalysisResult {
  pace: number;       // Words per minute
  volume: number;     // Volume level (0-100)
  tone: number;       // Tone (-1 to 1, negative to positive)
  keywords: string[]; // Detected emotional keywords
  wordFrequency: Record<string, number>; // Count of emotional words
}

export class AudioAnalyzer {
  private audioContext: AudioContext | null = null;
  private analyzer: AnalyserNode | null = null;
  private microphone: MediaStreamAudioSourceNode | null = null;
  private stream: MediaStream | null = null;
  private dataArray: Uint8Array | null = null;
  private isAnalyzing: boolean = false;
  private wordBuffer: string[] = [];
  private lastProcessedTime: number = 0;
  private volumeReadings: number[] = [];
  
  // Initialize the audio analyzer
  async initialize(): Promise<boolean> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.microphone = this.audioContext.createMediaStreamSource(this.stream);
      this.analyzer = this.audioContext.createAnalyser();
      
      // Configure analyzer
      this.analyzer.fftSize = 2048;
      this.microphone.connect(this.analyzer);
      
      // Create data array for analysis
      const bufferLength = this.analyzer.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);
      
      return true;
    } catch (error) {
      console.error('Failed to initialize audio analyzer:', error);
      return false;
    }
  }
  
  // Start continuous analysis
  startAnalysis(): void {
    if (!this.analyzer || this.isAnalyzing) return;
    
    this.isAnalyzing = true;
    this.analyzeAudio();
  }
  
  // Stop analysis and release resources
  stopAnalysis(): void {
    this.isAnalyzing = false;
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.analyzer = null;
    this.microphone = null;
    this.dataArray = null;
    this.wordBuffer = [];
    this.volumeReadings = [];
  }
  
  // Add transcribed words to the buffer for analysis
  addWords(words: string): void {
    const newWords = words.toLowerCase().split(/\s+/);
    this.wordBuffer = [...this.wordBuffer, ...newWords];
    
    // Keep buffer at reasonable size
    if (this.wordBuffer.length > 200) {
      this.wordBuffer = this.wordBuffer.slice(-200);
    }
  }
  
  // Analyze audio and return metrics
  analyzeAudio(): AudioAnalysisResult | null {
    if (!this.analyzer || !this.dataArray || !this.isAnalyzing) return null;
    
    // Get current audio data
    this.analyzer.getByteFrequencyData(this.dataArray);
    
    // Calculate volume (average amplitude)
    const volume = this.calculateVolume();
    
    // Calculate speech pace
    const pace = this.calculatePace();
    
    // Estimate emotional tone
    const tone = this.estimateTone();
    
    // Extract emotional keywords
    const { keywords, wordFrequency } = this.extractEmotionalKeywords();
    
    // Schedule next analysis
    requestAnimationFrame(() => this.analyzeAudio());
    
    return { pace, volume, tone, keywords, wordFrequency };
  }
  
  // Calculate current volume level (0-100)
  private calculateVolume(): number {
    if (!this.dataArray) return 0;
    
    // Calculate average amplitude
    const average = Array.from(this.dataArray).reduce((sum, value) => sum + value, 0) / this.dataArray.length;
    
    // Normalize to 0-100 scale
    const normalizedVolume = Math.min(100, Math.max(0, average / 2.56));
    
    // Add to readings for smoothing
    this.volumeReadings.push(normalizedVolume);
    if (this.volumeReadings.length > 10) {
      this.volumeReadings.shift();
    }
    
    // Return smoothed volume
    return this.volumeReadings.reduce((sum, vol) => sum + vol, 0) / this.volumeReadings.length;
  }
  
  // Calculate speech pace (words per minute)
  private calculatePace(): number {
    const now = Date.now();
    const elapsed = (now - this.lastProcessedTime) / 1000; // seconds
    
    if (elapsed < 5 || this.wordBuffer.length < 3) {
      return 0; // Not enough data yet
    }
    
    // Calculate words per minute
    const wordsPerSecond = this.wordBuffer.length / elapsed;
    const wordsPerMinute = wordsPerSecond * 60;
    
    // Reset for next calculation
    this.lastProcessedTime = now;
    this.wordBuffer = [];
    
    return Math.round(wordsPerMinute);
  }
  
  // Estimate emotional tone based on frequency patterns
  private estimateTone(): number {
    if (!this.dataArray) return 0;
    
    // Simple heuristic: higher frequencies often correlate with positive emotions
    // Lower frequencies with negative emotions
    const lowerFreqs = this.dataArray.slice(0, this.dataArray.length / 3);
    const higherFreqs = this.dataArray.slice(this.dataArray.length * 2 / 3);
    
    const lowerAvg = lowerFreqs.reduce((sum, val) => sum + val, 0) / lowerFreqs.length;
    const higherAvg = higherFreqs.reduce((sum, val) => sum + val, 0) / higherFreqs.length;
    
    // Calculate tone on scale from -1 (negative) to 1 (positive)
    return (higherAvg - lowerAvg) / 128;
  }
  
  // Extract emotional keywords from word buffer
  private extractEmotionalKeywords(): { keywords: string[], wordFrequency: Record<string, number> } {
    const wordFrequency: Record<string, number> = {};
    const keywords: string[] = [];
    
    // Count occurrences of emotional keywords
    this.wordBuffer.forEach(word => {
      if (emotionalKeywords.includes(word)) {
        wordFrequency[word] = (wordFrequency[word] || 0) + 1;
        if (!keywords.includes(word)) {
          keywords.push(word);
        }
      }
    });
    
    return { keywords, wordFrequency };
  }
}

// Create singleton instance
export const audioAnalyzer = new AudioAnalyzer();