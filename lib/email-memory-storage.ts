import { createClient } from '@supabase/supabase-js';
import { EmailSummary } from './gmail-service';

interface EmailInteractionMemory {
  userId: string;
  action: 'summarize' | 'send' | 'reply';
  summary: string;
  details: {
    emailCount?: number;
    recipient?: string;
    subject?: string;
    message?: string;
    priorityEmails?: EmailSummary[];
  };
  emotionalContext?: string[];
}

export async function storeEmailSummaryMemory(
  userId: string,
  emailSummaryData: any
): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Store email interaction in a separate table/system, not in conversations
    // For now, we'll just log it and store it in a way that doesn't interfere with conversations
    const emailInteractionData = {
      user_id: userId,
      interaction_type: 'email_summary',
      summary: `Checked ${emailSummaryData.totalEmails} emails, ${emailSummaryData.unreadCount} unread`,
      details: JSON.stringify({
        totalEmails: emailSummaryData.totalEmails,
        unreadCount: emailSummaryData.unreadCount,
        summary: emailSummaryData.summary,
        priorityEmails: emailSummaryData.priorityEmails?.slice(0, 3).map((email: any) => ({
          from: email.from,
          subject: email.subject,
          snippet: email.snippet.substring(0, 100)
        }))
      }),
      created_at: new Date().toISOString()
    };

    // Store in the email_interactions table (separate from conversations)
    const { error } = await supabase
      .from('email_interactions')
      .insert(emailInteractionData);

    if (error) {
      console.error('Error storing email interaction:', error);
    } else {
      console.log('Email summary interaction stored successfully (separate from conversations)');
    }
  } catch (error) {
    console.error('Failed to process email summary memory:', error);
  }
}

export async function storeEmailSendMemory(
  userId: string,
  emailSendData: {
    recipient: string;
    subject: string;
    message: string;
    isReply?: boolean;
  }
): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const action = emailSendData.isReply ? 'replied to' : 'sent email to';
    const emailInteractionData = {
      user_id: userId,
      interaction_type: emailSendData.isReply ? 'email_reply' : 'email_send',
      summary: `${action} ${emailSendData.recipient}: ${emailSendData.subject}`,
      details: JSON.stringify({
        recipient: emailSendData.recipient,
        subject: emailSendData.subject,
        messagePreview: emailSendData.message.substring(0, 100),
        isReply: emailSendData.isReply || false,
        fullMessage: emailSendData.message
      }),
      created_at: new Date().toISOString()
    };

    // Store in the email_interactions table (separate from conversations)
    const { error } = await supabase
      .from('email_interactions')
      .insert(emailInteractionData);

    if (error) {
      console.error('Error storing email interaction:', error);
    } else {
      console.log('Email send interaction stored successfully (separate from conversations)');
    }
  } catch (error) {
    console.error('Failed to process email send memory:', error);
  }
}

export async function getRecentEmailInteractions(
  userId: string,
  limit: number = 5
): Promise<any[]> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data, error } = await supabase
      .from('email_interactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent email interactions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch recent email interactions:', error);
    return [];
  }
}

export async function getEmailContactHistory(
  userId: string,
  contactEmail: string
): Promise<any[]> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Search for email interactions involving this contact
    const { data, error } = await supabase
      .from('email_interactions')
      .select('*')
      .eq('user_id', userId)
      .in('interaction_type', ['email_send', 'email_reply'])
      .ilike('summary', `%${contactEmail}%`)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching email contact history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch email contact history:', error);
    return [];
  }
}

function determineEmailEmotionalMarkers(emailSummaryData: any): string[] {
  const markers = ['productive'];

  // Add emotional markers based on email content
  if (emailSummaryData.unreadCount > 10) {
    markers.push('overwhelmed');
  } else if (emailSummaryData.unreadCount === 0) {
    markers.push('organized', 'satisfied');
  }

  if (emailSummaryData.categories?.urgent?.length > 0) {
    markers.push('focused', 'alert');
  }

  if (emailSummaryData.categories?.personal?.length > 0) {
    markers.push('connected');
  }

  return markers;
}

// Enhanced function to store email interactions with emotional context
export async function storeEmailInteractionWithContext(
  userId: string,
  interaction: EmailInteractionMemory
): Promise<void> {
  try {
    switch (interaction.action) {
      case 'summarize':
        await storeEmailSummaryMemory(userId, {
          totalEmails: interaction.details.emailCount || 0,
          unreadCount: 0, // This would come from the actual summary data
          summary: interaction.summary,
          priorityEmails: interaction.details.priorityEmails || []
        });
        break;

      case 'send':
      case 'reply':
        await storeEmailSendMemory(userId, {
          recipient: interaction.details.recipient || 'Unknown',
          subject: interaction.details.subject || 'No Subject',
          message: interaction.details.message || '',
          isReply: interaction.action === 'reply'
        });
        break;
    }

    console.log(`Email ${interaction.action} interaction stored successfully`);
  } catch (error) {
    console.error('Failed to store email interaction with context:', error);
  }
}
