import { createClient } from '@supabase/supabase-js';
import { EmailSummary } from './gmail-service';

interface EmailInteractionMemory {
  userId: string;
  action: 'summarize' | 'send' | 'reply';
  summary: string;
  details: {
    emailCount?: number;
    recipient?: string;
    subject?: string;
    message?: string;
    priorityEmails?: EmailSummary[];
  };
  emotionalContext?: string[];
}

export async function storeEmailSummaryMemory(
  userId: string,
  emailSummaryData: any
): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Create a conversation entry for the email summary
    const conversationData = {
      user_id: userId,
      summary: `Email summary: ${emailSummaryData.totalEmails} emails, ${emailSummaryData.unreadCount} unread`,
      full_conversation: JSON.stringify([
        {
          role: 'user',
          content: 'Summarize my emails',
          timestamp: new Date().toISOString()
        },
        {
          role: 'assistant',
          content: emailSummaryData.summary,
          timestamp: new Date().toISOString(),
          metadata: {
            totalEmails: emailSummaryData.totalEmails,
            unreadCount: emailSummaryData.unreadCount,
            priorityEmails: emailSummaryData.priorityEmails?.slice(0, 3).map((email: any) => ({
              from: email.from,
              subject: email.subject,
              snippet: email.snippet.substring(0, 100)
            }))
          }
        }
      ]),
      emotional_markers: determineEmailEmotionalMarkers(emailSummaryData),
      topics: ['email', 'productivity', 'communication'],
      conversation_type: 'email_summary'
    };

    const { error } = await supabase
      .from('conversations')
      .insert(conversationData);

    if (error) {
      console.error('Error storing email summary memory:', error);
    } else {
      console.log('Email summary memory stored successfully');
    }
  } catch (error) {
    console.error('Failed to store email summary memory:', error);
  }
}

export async function storeEmailSendMemory(
  userId: string,
  emailSendData: {
    recipient: string;
    subject: string;
    message: string;
    isReply?: boolean;
  }
): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const action = emailSendData.isReply ? 'replied to' : 'sent email to';
    const conversationData = {
      user_id: userId,
      summary: `${action} ${emailSendData.recipient}: ${emailSendData.subject}`,
      full_conversation: JSON.stringify([
        {
          role: 'user',
          content: `${emailSendData.isReply ? 'Reply to' : 'Send email to'} ${emailSendData.recipient}: ${emailSendData.message}`,
          timestamp: new Date().toISOString()
        },
        {
          role: 'assistant',
          content: `Email ${emailSendData.isReply ? 'reply' : ''} sent successfully to ${emailSendData.recipient}`,
          timestamp: new Date().toISOString(),
          metadata: {
            recipient: emailSendData.recipient,
            subject: emailSendData.subject,
            messagePreview: emailSendData.message.substring(0, 100),
            isReply: emailSendData.isReply || false
          }
        }
      ]),
      emotional_markers: ['productive', 'communicative', 'helpful'],
      topics: ['email', 'communication', 'productivity'],
      conversation_type: emailSendData.isReply ? 'email_reply' : 'email_send'
    };

    const { error } = await supabase
      .from('conversations')
      .insert(conversationData);

    if (error) {
      console.error('Error storing email send memory:', error);
    } else {
      console.log('Email send memory stored successfully');
    }
  } catch (error) {
    console.error('Failed to store email send memory:', error);
  }
}

export async function getRecentEmailInteractions(
  userId: string,
  limit: number = 5
): Promise<any[]> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('user_id', userId)
      .in('conversation_type', ['email_summary', 'email_send', 'email_reply'])
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent email interactions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch recent email interactions:', error);
    return [];
  }
}

export async function getEmailContactHistory(
  userId: string,
  contactEmail: string
): Promise<any[]> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Search for conversations involving this contact
    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('user_id', userId)
      .in('conversation_type', ['email_send', 'email_reply'])
      .ilike('summary', `%${contactEmail}%`)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('Error fetching email contact history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Failed to fetch email contact history:', error);
    return [];
  }
}

function determineEmailEmotionalMarkers(emailSummaryData: any): string[] {
  const markers = ['productive'];

  // Add emotional markers based on email content
  if (emailSummaryData.unreadCount > 10) {
    markers.push('overwhelmed');
  } else if (emailSummaryData.unreadCount === 0) {
    markers.push('organized', 'satisfied');
  }

  if (emailSummaryData.categories?.urgent?.length > 0) {
    markers.push('focused', 'alert');
  }

  if (emailSummaryData.categories?.personal?.length > 0) {
    markers.push('connected');
  }

  return markers;
}

// Enhanced function to store email interactions with emotional context
export async function storeEmailInteractionWithContext(
  userId: string,
  interaction: EmailInteractionMemory
): Promise<void> {
  try {
    switch (interaction.action) {
      case 'summarize':
        await storeEmailSummaryMemory(userId, {
          totalEmails: interaction.details.emailCount || 0,
          unreadCount: 0, // This would come from the actual summary data
          summary: interaction.summary,
          priorityEmails: interaction.details.priorityEmails || []
        });
        break;

      case 'send':
      case 'reply':
        await storeEmailSendMemory(userId, {
          recipient: interaction.details.recipient || 'Unknown',
          subject: interaction.details.subject || 'No Subject',
          message: interaction.details.message || '',
          isReply: interaction.action === 'reply'
        });
        break;
    }

    console.log(`Email ${interaction.action} interaction stored successfully`);
  } catch (error) {
    console.error('Failed to store email interaction with context:', error);
  }
}
