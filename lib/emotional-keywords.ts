// Dictionary of emotional keywords for analysis

// Emotional keywords categorized by emotion type
export const emotionalKeywordsByCategory: Record<string, string[]> = {
  joy: [
    'happy', 'excited', 'delighted', 'thrilled', 'ecstatic',
    'glad', 'pleased', 'joyful', 'cheerful', 'content'
  ],
  sadness: [
    'sad', 'unhappy', 'depressed', 'gloomy', 'miserable',
    'heartbroken', 'disappointed', 'upset', 'down', 'blue'
  ],
  anger: [
    'angry', 'furious', 'enraged', 'irritated', 'annoyed',
    'frustrated', 'mad', 'outraged', 'hostile', 'bitter'
  ],
  fear: [
    'afraid', 'scared', 'frightened', 'terrified', 'anxious',
    'nervous', 'worried', 'panicked', 'alarmed', 'uneasy'
  ],
  surprise: [
    'surprised', 'shocked', 'amazed', 'astonished', 'stunned',
    'startled', 'dumbfounded', 'bewildered', 'awestruck', 'speechless'
  ],
  disgust: [
    'disgusted', 'revolted', 'repulsed', 'appalled', 'sickened',
    'nauseated', 'offended', 'horrified', 'averse', 'loathing'
  ],
  trust: [
    'trusting', 'confident', 'secure', 'assured', 'faithful',
    'reliable', 'dependable', 'believing', 'certain', 'safe'
  ],
  anticipation: [
    'anticipating', 'expecting', 'hopeful', 'eager', 'looking forward',
    'excited', 'waiting', 'ready', 'prepared', 'enthusiastic'
  ],
  fatigue: [
    'tired', 'exhausted', 'weary', 'drained', 'sleepy',
    'fatigued', 'worn out', 'spent', 'burned out', 'lethargic'
  ],
  confusion: [
    'confused', 'puzzled', 'perplexed', 'baffled', 'disoriented',
    'uncertain', 'unsure', 'lost', 'bewildered', 'muddled'
  ]
};

// Flattened list of all emotional keywords
export const emotionalKeywords: string[] = Object.values(emotionalKeywordsByCategory)
  .flat()
  .sort();

// Get emotion category from keyword
export function getEmotionCategory(keyword: string): string | null {
  for (const [category, words] of Object.entries(emotionalKeywordsByCategory)) {
    if (words.includes(keyword.toLowerCase())) {
      return category;
    }
  }
  return null;
}