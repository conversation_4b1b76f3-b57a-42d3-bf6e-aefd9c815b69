import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';

// Gmail API scopes
const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.compose'
];

export interface EmailSummary {
  id: string;
  threadId: string;
  from: string;
  subject: string;
  snippet: string;
  date: Date;
  isUnread: boolean;
  labels: string[];
}

export interface EmailThread {
  id: string;
  messages: EmailMessage[];
}

export interface EmailMessage {
  id: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  date: Date;
  isUnread: boolean;
}

export class GmailService {
  private oauth2Client: OAuth2Client;
  private gmail: any;

  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gmail/callback`
    );
  }

  // Generate OAuth URL for user authentication
  getAuthUrl(): string {
    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
      prompt: 'consent'
    });
  }

  // Set credentials from stored tokens
  setCredentials(tokens: any) {
    this.oauth2Client.setCredentials(tokens);
    this.gmail = google.gmail({ version: 'v1', auth: this.oauth2Client });
  }

  // Exchange authorization code for tokens
  async getTokens(code: string) {
    const { tokens } = await this.oauth2Client.getToken(code);
    return tokens;
  }

  // Refresh access token if needed
  async refreshTokens() {
    const { credentials } = await this.oauth2Client.refreshAccessToken();
    return credentials;
  }

  // Fetch today's emails
  async getTodaysEmails(): Promise<EmailSummary[]> {
    if (!this.gmail) {
      throw new Error('Gmail client not initialized. Please authenticate first.');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = Math.floor(today.getTime() / 1000);

    try {
      // Search for emails from today
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        q: `after:${todayTimestamp}`,
        maxResults: 50
      });

      if (!response.data.messages) {
        return [];
      }

      // Get detailed information for each message
      const emailPromises = response.data.messages.map(async (message: any) => {
        const details = await this.gmail.users.messages.get({
          userId: 'me',
          id: message.id,
          format: 'metadata',
          metadataHeaders: ['From', 'Subject', 'Date']
        });

        const headers = details.data.payload.headers;
        const fromHeader = headers.find((h: any) => h.name === 'From');
        const subjectHeader = headers.find((h: any) => h.name === 'Subject');
        const dateHeader = headers.find((h: any) => h.name === 'Date');

        return {
          id: message.id,
          threadId: details.data.threadId,
          from: fromHeader?.value || 'Unknown',
          subject: subjectHeader?.value || 'No Subject',
          snippet: details.data.snippet || '',
          date: new Date(dateHeader?.value || Date.now()),
          isUnread: details.data.labelIds?.includes('UNREAD') || false,
          labels: details.data.labelIds || []
        };
      });

      const emails = await Promise.all(emailPromises);
      
      // Sort by date (newest first)
      return emails.sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error('Error fetching emails:', error);
      throw new Error('Failed to fetch emails from Gmail');
    }
  }

  // Get email thread for context when responding
  async getEmailThread(threadId: string): Promise<EmailThread> {
    if (!this.gmail) {
      throw new Error('Gmail client not initialized. Please authenticate first.');
    }

    try {
      const response = await this.gmail.users.threads.get({
        userId: 'me',
        id: threadId,
        format: 'full'
      });

      const messages: EmailMessage[] = response.data.messages.map((msg: any) => {
        const headers = msg.payload.headers;
        const fromHeader = headers.find((h: any) => h.name === 'From');
        const toHeader = headers.find((h: any) => h.name === 'To');
        const subjectHeader = headers.find((h: any) => h.name === 'Subject');
        const dateHeader = headers.find((h: any) => h.name === 'Date');

        // Extract body text
        let body = '';
        if (msg.payload.body?.data) {
          body = Buffer.from(msg.payload.body.data, 'base64').toString();
        } else if (msg.payload.parts) {
          // Handle multipart messages
          const textPart = msg.payload.parts.find((part: any) => 
            part.mimeType === 'text/plain' && part.body?.data
          );
          if (textPart) {
            body = Buffer.from(textPart.body.data, 'base64').toString();
          }
        }

        return {
          id: msg.id,
          from: fromHeader?.value || 'Unknown',
          to: toHeader?.value || 'Unknown',
          subject: subjectHeader?.value || 'No Subject',
          body: body,
          date: new Date(dateHeader?.value || Date.now()),
          isUnread: msg.labelIds?.includes('UNREAD') || false
        };
      });

      return {
        id: threadId,
        messages: messages
      };
    } catch (error) {
      console.error('Error fetching email thread:', error);
      throw new Error('Failed to fetch email thread');
    }
  }

  // Send email response
  async sendEmail(to: string, subject: string, body: string, threadId?: string): Promise<void> {
    if (!this.gmail) {
      throw new Error('Gmail client not initialized. Please authenticate first.');
    }

    try {
      // Create email message
      const email = [
        `To: ${to}`,
        `Subject: ${subject}`,
        '',
        body
      ].join('\n');

      const encodedEmail = Buffer.from(email).toString('base64url');

      const requestBody: any = {
        raw: encodedEmail
      };

      // If replying to a thread, include threadId
      if (threadId) {
        requestBody.threadId = threadId;
      }

      await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: requestBody
      });

      console.log('Email sent successfully');
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  // Find email by sender name (for voice commands like "reply to John")
  async findEmailBySender(senderName: string, maxResults: number = 10): Promise<EmailSummary[]> {
    if (!this.gmail) {
      throw new Error('Gmail client not initialized. Please authenticate first.');
    }

    try {
      // Search for emails from the sender
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        q: `from:${senderName}`,
        maxResults
      });

      if (!response.data.messages) {
        return [];
      }

      // Get detailed information for each message
      const emailPromises = response.data.messages.map(async (message: any) => {
        const details = await this.gmail.users.messages.get({
          userId: 'me',
          id: message.id,
          format: 'metadata',
          metadataHeaders: ['From', 'Subject', 'Date']
        });

        const headers = details.data.payload.headers;
        const fromHeader = headers.find((h: any) => h.name === 'From');
        const subjectHeader = headers.find((h: any) => h.name === 'Subject');
        const dateHeader = headers.find((h: any) => h.name === 'Date');

        return {
          id: message.id,
          threadId: details.data.threadId,
          from: fromHeader?.value || 'Unknown',
          subject: subjectHeader?.value || 'No Subject',
          snippet: details.data.snippet || '',
          date: new Date(dateHeader?.value || Date.now()),
          isUnread: details.data.labelIds?.includes('UNREAD') || false,
          labels: details.data.labelIds || []
        };
      });

      const emails = await Promise.all(emailPromises);

      // Sort by date (newest first)
      return emails.sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error('Error finding emails by sender:', error);
      throw new Error('Failed to find emails by sender');
    }
  }
}
