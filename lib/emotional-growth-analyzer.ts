/**
 * Emotional Growth Analyzer
 * 
 * This service analyzes emotional data to track growth, stability,
 * diversity, and resilience metrics over time.
 */

// Types for emotional data analysis
export interface EmotionalDataPoint {
  emotion: string;
  intensity: number;
  speech_pace?: number;
  speech_volume?: number;
  speech_tone?: number;
  timestamp: string;
  emotional_keywords?: string[];
}

export interface EmotionalGrowthMetrics {
  stability: number;       // 0-100 scale, higher means more stable
  diversity: number;       // 0-100 scale, higher means more diverse emotional range
  resilience: number;      // 0-100 scale, higher means better recovery from negative emotions
  awareness: number;       // 0-100 scale, higher means more emotional self-awareness
  expressiveness: number;  // 0-100 scale, higher means more expressive communication
  growth: number;          // Overall growth score (average of all metrics)
  insights: string[];      // Key insights about emotional growth
  dominantEmotions: Record<string, number>; // Count of dominant emotions
  emotionalTrends: Record<string, number>;  // Trends for each emotion (positive or negative slope)
}

export interface EmotionalTimelineSegment {
  startDate: string;
  endDate: string;
  dominantEmotion: string;
  averageIntensity: number;
  emotionalDiversity: number;
  keyInsight: string;
}

// Main analyzer class
export class EmotionalGrowthAnalyzer {
  // Categorize emotions into positive, negative, and neutral
  private static positiveEmotions = ['joy', 'trust', 'anticipation', 'surprise'];
  private static negativeEmotions = ['sadness', 'fear', 'anger', 'disgust'];
  private static neutralEmotions = ['neutral', 'confusion'];
  
  /**
   * Calculate emotional growth metrics from a set of emotional data points
   */
  static calculateGrowthMetrics(data: EmotionalDataPoint[]): EmotionalGrowthMetrics {
    if (!data || data.length < 5) {
      return this.getDefaultMetrics();
    }
    
    // Sort data by timestamp
    const sortedData = [...data].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    // Calculate stability metric
    const stability = this.calculateStability(sortedData);
    
    // Calculate emotional diversity
    const diversity = this.calculateDiversity(sortedData);
    
    // Calculate emotional resilience
    const resilience = this.calculateResilience(sortedData);
    
    // Calculate emotional awareness
    const awareness = this.calculateAwareness(sortedData);
    
    // Calculate expressiveness
    const expressiveness = this.calculateExpressiveness(sortedData);
    
    // Calculate overall growth score
    const growth = (stability + diversity + resilience + awareness + expressiveness) / 5;
    
    // Generate insights
    const insights = this.generateInsights(sortedData, {
      stability,
      diversity,
      resilience,
      awareness,
      expressiveness,
      growth,
      insights: [],
      dominantEmotions: {},
      emotionalTrends: {}
    });
    
    // Get dominant emotions
    const dominantEmotions = this.getDominantEmotions(sortedData);
    
    // Calculate emotional trends
    const emotionalTrends = this.calculateEmotionalTrends(sortedData);
    
    return {
      stability,
      diversity,
      resilience,
      awareness,
      expressiveness,
      growth,
      insights,
      dominantEmotions,
      emotionalTrends
    };
  }
  
  /**
   * Generate a timeline of emotional growth segments
   */
  static generateEmotionalTimeline(data: EmotionalDataPoint[]): EmotionalTimelineSegment[] {
    if (!data || data.length < 10) {
      return [];
    }
    
    // Sort data by timestamp
    const sortedData = [...data].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    // Divide data into segments (e.g., weeks)
    const segments: EmotionalTimelineSegment[] = [];
    const segmentSize = Math.max(5, Math.floor(sortedData.length / 4)); // At least 5 data points per segment
    
    for (let i = 0; i < sortedData.length; i += segmentSize) {
      const segmentData = sortedData.slice(i, i + segmentSize);
      if (segmentData.length < 3) continue; // Skip segments with too few data points
      
      // Calculate segment metrics
      const dominantEmotion = this.findDominantEmotion(segmentData);
      const averageIntensity = this.calculateAverageIntensity(segmentData);
      const emotionalDiversity = this.calculateDiversity(segmentData);
      
      // Generate insight for this segment
      const keyInsight = this.generateSegmentInsight(segmentData, dominantEmotion, averageIntensity, emotionalDiversity);
      
      segments.push({
        startDate: segmentData[0].timestamp,
        endDate: segmentData[segmentData.length - 1].timestamp,
        dominantEmotion,
        averageIntensity,
        emotionalDiversity,
        keyInsight
      });
    }
    
    return segments;
  }
  
  /**
   * Calculate emotional stability (consistency over time)
   * Higher values mean more stable emotional patterns
   */
  private static calculateStability(data: EmotionalDataPoint[]): number {
    // Calculate intensity variance
    const intensities = data.map(d => d.intensity);
    const avgIntensity = intensities.reduce((sum, val) => sum + val, 0) / intensities.length;
    const variance = intensities.reduce((sum, val) => sum + Math.pow(val - avgIntensity, 2), 0) / intensities.length;
    
    // Calculate emotion switching frequency
    let emotionSwitches = 0;
    for (let i = 1; i < data.length; i++) {
      if (data[i].emotion !== data[i-1].emotion) {
        emotionSwitches++;
      }
    }
    const switchRate = emotionSwitches / (data.length - 1);
    
    // Combine metrics (lower variance and switch rate = higher stability)
    const varianceScore = Math.max(0, 100 - (variance * 10));
    const switchScore = Math.max(0, 100 - (switchRate * 100));
    
    return Math.round((varianceScore * 0.6) + (switchScore * 0.4));
  }
  
  /**
   * Calculate emotional diversity (range of emotions experienced)
   * Higher values mean more diverse emotional experiences
   */
  private static calculateDiversity(data: EmotionalDataPoint[]): number {
    // Count unique emotions
    const uniqueEmotions = new Set(data.map(d => d.emotion).filter(Boolean));
    
    // Calculate Shannon entropy for emotion distribution
    const emotionCounts: Record<string, number> = {};
    data.forEach(d => {
      if (d.emotion) {
        emotionCounts[d.emotion] = (emotionCounts[d.emotion] || 0) + 1;
      }
    });
    
    const totalEmotions = data.length;
    let entropy = 0;
    
    Object.values(emotionCounts).forEach(count => {
      const probability = count / totalEmotions;
      entropy -= probability * Math.log2(probability);
    });
    
    // Normalize entropy to 0-100 scale (max entropy would be log2 of total possible emotions)
    const maxPossibleEmotions = 10; // Assuming 10 possible emotion categories
    const normalizedEntropy = (entropy / Math.log2(maxPossibleEmotions)) * 100;
    
    // Combine with unique emotion count
    const uniqueEmotionScore = (uniqueEmotions.size / maxPossibleEmotions) * 100;
    
    return Math.round((normalizedEntropy * 0.7) + (uniqueEmotionScore * 0.3));
  }
  
  /**
   * Calculate emotional resilience (recovery from negative emotions)
   * Higher values mean better recovery from negative states
   */
  private static calculateResilience(data: EmotionalDataPoint[]): number {
    let negativeSequences = 0;
    let recoveries = 0;
    let inNegativeSequence = false;
    
    // Identify sequences of negative emotions and count recoveries
    for (let i = 0; i < data.length; i++) {
      const isNegative = this.negativeEmotions.includes(data[i].emotion);
      
      if (isNegative && !inNegativeSequence) {
        // Start of a negative sequence
        inNegativeSequence = true;
        negativeSequences++;
      } else if (!isNegative && inNegativeSequence) {
        // End of a negative sequence (recovery)
        inNegativeSequence = false;
        recoveries++;
      }
    }
    
    // Calculate recovery rate
    const recoveryRate = negativeSequences > 0 ? (recoveries / negativeSequences) * 100 : 100;
    
    // Calculate average duration of negative sequences
    let totalNegativeSequenceLength = 0;
    let currentSequenceLength = 0;
    inNegativeSequence = false;
    
    for (let i = 0; i < data.length; i++) {
      const isNegative = this.negativeEmotions.includes(data[i].emotion);
      
      if (isNegative) {
        currentSequenceLength++;
        inNegativeSequence = true;
      } else if (inNegativeSequence) {
        totalNegativeSequenceLength += currentSequenceLength;
        currentSequenceLength = 0;
        inNegativeSequence = false;
      }
    }
    
    // Add the last sequence if we're still in one
    if (inNegativeSequence) {
      totalNegativeSequenceLength += currentSequenceLength;
    }
    
    const avgNegativeSequenceLength = negativeSequences > 0 ? totalNegativeSequenceLength / negativeSequences : 0;
    const durationScore = Math.max(0, 100 - (avgNegativeSequenceLength * 20));
    
    // Combine metrics
    return Math.round((recoveryRate * 0.7) + (durationScore * 0.3));
  }
  
  /**
   * Calculate emotional awareness (ability to identify and express emotions)
   * Higher values mean more emotional self-awareness
   */
  private static calculateAwareness(data: EmotionalDataPoint[]): number {
    // Check for presence of emotional keywords
    const keywordRichness = data.reduce((sum, d) => {
      return sum + (d.emotional_keywords && d.emotional_keywords.length > 0 ? 1 : 0);
    }, 0) / data.length;
    
    // Check for non-neutral emotion identification
    const nonNeutralRate = data.reduce((sum, d) => {
      return sum + (!this.neutralEmotions.includes(d.emotion) ? 1 : 0);
    }, 0) / data.length;
    
    // Check for intensity variation (more aware people tend to use the full range)
    const intensities = data.map(d => d.intensity);
    const minIntensity = Math.min(...intensities);
    const maxIntensity = Math.max(...intensities);
    const intensityRange = maxIntensity - minIntensity;
    const rangeScore = intensityRange / 10; // Normalize to 0-1 scale
    
    // Combine metrics
    return Math.round((keywordRichness * 40) + (nonNeutralRate * 40) + (rangeScore * 20));
  }
  
  /**
   * Calculate emotional expressiveness (vocal and speech patterns)
   * Higher values mean more expressive communication
   */
  private static calculateExpressiveness(data: EmotionalDataPoint[]): number {
    // Count data points with speech metrics
    const withSpeechMetrics = data.filter(d => 
      d.speech_pace !== undefined || 
      d.speech_volume !== undefined || 
      d.speech_tone !== undefined
    ).length;
    
    const speechCoverage = withSpeechMetrics / data.length;
    
    // Calculate volume variation
    const volumes = data.map(d => d.speech_volume).filter(v => v !== undefined) as number[];
    let volumeVariation = 0;
    
    if (volumes.length > 1) {
      const avgVolume = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
      const volumeVariance = volumes.reduce((sum, v) => sum + Math.pow((v - avgVolume), 2), 0) / volumes.length;
      volumeVariation = Math.min(1, Math.sqrt(volumeVariance) / 30); // Normalize to 0-1
    }
    
    // Calculate pace variation
    const paces = data.map(d => d.speech_pace).filter(p => p !== undefined) as number[];
    let paceVariation = 0;
    
    if (paces.length > 1) {
      const avgPace = paces.reduce((sum, p) => sum + p, 0) / paces.length;
      const paceVariance = paces.reduce((sum, p) => sum + Math.pow((p - avgPace), 2), 0) / paces.length;
      paceVariation = Math.min(1, Math.sqrt(paceVariance) / 50); // Normalize to 0-1
    }
    
    // Calculate tone variation
    const tones = data.map(d => d.speech_tone).filter(t => t !== undefined) as number[];
    let toneVariation = 0;
    
    if (tones.length > 1) {
      const avgTone = tones.reduce((sum, t) => sum + t, 0) / tones.length;
      const toneVariance = tones.reduce((sum, t) => sum + Math.pow((t - avgTone), 2), 0) / tones.length;
      toneVariation = Math.min(1, Math.sqrt(toneVariance) / 0.5); // Normalize to 0-1
    }
    
    // Combine metrics
    return Math.round(
      (speechCoverage * 30) + 
      (volumeVariation * 25) + 
      (paceVariation * 25) + 
      (toneVariation * 20)
    );
  }
  
  /**
   * Generate insights based on emotional data and calculated metrics
   */
  private static generateInsights(
    data: EmotionalDataPoint[], 
    metrics: EmotionalGrowthMetrics
  ): string[] {
    const insights: string[] = [];
    
    // Insight about dominant emotions
    const dominantEmotions = this.getDominantEmotions(data);
    const topEmotions = Object.entries(dominantEmotions)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 2)
      .map(([emotion]) => emotion);
    
    if (topEmotions.length > 0) {
      insights.push(`Your conversations are predominantly characterized by ${topEmotions.join(' and ')}, which suggests a ${this.getEmotionalTendency(topEmotions)} communication style.`);
    }
    
    // Insight about stability
    if (metrics.stability > 80) {
      insights.push(`You show remarkable emotional stability, maintaining consistent emotional patterns across conversations.`);
    } else if (metrics.stability < 40) {
      insights.push(`Your emotional patterns show significant variability, which can indicate a period of personal growth or change.`);
    }
    
    // Insight about diversity
    if (metrics.diversity > 75) {
      insights.push(`You express a rich range of emotions, demonstrating high emotional intelligence and self-awareness.`);
    } else if (metrics.diversity < 30) {
      insights.push(`Your emotional expression tends to focus on a limited range. Exploring a wider emotional vocabulary could enhance your self-understanding.`);
    }
    
    // Insight about resilience
    if (metrics.resilience > 70) {
      insights.push(`You demonstrate strong emotional resilience, effectively recovering from challenging emotional states.`);
    } else if (metrics.resilience < 40) {
      insights.push(`Building strategies to transition from difficult emotions could enhance your emotional well-being.`);
    }
    
    // Insight about growth trends
    const trends = this.calculateEmotionalTrends(data);
    const positiveGrowth = Object.entries(trends)
      .filter(([emotion, trend]) => this.positiveEmotions.includes(emotion) && trend > 0)
      .map(([emotion]) => emotion);
    
    if (positiveGrowth.length > 0) {
      insights.push(`You're showing positive growth in expressing ${positiveGrowth.join(' and ')}, indicating emotional development.`);
    }
    
    // Insight about expressiveness
    if (metrics.expressiveness > 75) {
      insights.push(`Your communication style is highly expressive, with rich variations in tone, pace, and volume.`);
    } else if (metrics.expressiveness < 40) {
      insights.push(`Your vocal expression tends to be consistent. Exploring more vocal variety might enhance your emotional communication.`);
    }
    
    return insights;
  }
  
  /**
   * Get the emotional tendency based on dominant emotions
   */
  private static getEmotionalTendency(emotions: string[]): string {
    const positiveCount = emotions.filter(e => this.positiveEmotions.includes(e)).length;
    const negativeCount = emotions.filter(e => this.negativeEmotions.includes(e)).length;
    const neutralCount = emotions.filter(e => this.neutralEmotions.includes(e)).length;
    
    if (positiveCount > negativeCount && positiveCount > neutralCount) {
      return "generally positive";
    } else if (negativeCount > positiveCount && negativeCount > neutralCount) {
      return "reflective and processing-oriented";
    } else if (neutralCount > positiveCount && neutralCount > negativeCount) {
      return "measured and analytical";
    } else {
      return "balanced";
    }
  }
  
  /**
   * Find the dominant emotion in a set of data points
   */
  private static findDominantEmotion(data: EmotionalDataPoint[]): string {
    const emotionCounts: Record<string, number> = {};
    
    data.forEach(d => {
      if (d.emotion) {
        emotionCounts[d.emotion] = (emotionCounts[d.emotion] || 0) + 1;
      }
    });
    
    return Object.entries(emotionCounts)
      .sort((a, b) => b[1] - a[1])[0][0];
  }
  
  /**
   * Calculate average intensity for a set of data points
   */
  private static calculateAverageIntensity(data: EmotionalDataPoint[]): number {
    const intensities = data.map(d => d.intensity);
    return intensities.reduce((sum, val) => sum + val, 0) / intensities.length;
  }
  
  /**
   * Generate an insight for a timeline segment
   */
  private static generateSegmentInsight(
    data: EmotionalDataPoint[],
    dominantEmotion: string,
    averageIntensity: number,
    emotionalDiversity: number
  ): string {
    // Check if this is a positive, negative, or neutral dominant emotion
    const isPositive = this.positiveEmotions.includes(dominantEmotion);
    const isNegative = this.negativeEmotions.includes(dominantEmotion);
    
    if (isPositive) {
      if (averageIntensity > 7) {
        return `Period of high ${dominantEmotion} with strong emotional expression`;
      } else if (emotionalDiversity > 70) {
        return `Positive period with diverse emotional experiences`;
      } else {
        return `Steady ${dominantEmotion} with moderate intensity`;
      }
    } else if (isNegative) {
      if (averageIntensity > 7) {
        return `Period of processing intense ${dominantEmotion}`;
      } else if (emotionalDiversity > 70) {
        return `Period of emotional processing with ${dominantEmotion} as a theme`;
      } else {
        return `Reflective period centered on ${dominantEmotion}`;
      }
    } else {
      if (emotionalDiversity > 70) {
        return `Period of emotional exploration with balanced expression`;
      } else {
        return `Steady, measured emotional expression`;
      }
    }
  }
  
  /**
   * Get counts of dominant emotions from data
   */
  private static getDominantEmotions(data: EmotionalDataPoint[]): Record<string, number> {
    const emotionCounts: Record<string, number> = {};
    
    data.forEach(d => {
      if (d.emotion) {
        emotionCounts[d.emotion] = (emotionCounts[d.emotion] || 0) + 1;
      }
    });
    
    return emotionCounts;
  }
  
  /**
   * Calculate trends for each emotion (positive or negative slope)
   */
  private static calculateEmotionalTrends(data: EmotionalDataPoint[]): Record<string, number> {
    const trends: Record<string, number> = {};
    const emotions = new Set(data.map(d => d.emotion).filter(Boolean));
    
    emotions.forEach(emotion => {
      // Get data points for this emotion
      const points = data
        .filter(d => d.emotion === emotion)
        .map((d, i) => ({ 
          x: i, 
          y: d.intensity,
          timestamp: new Date(d.timestamp).getTime()
        }));
      
      if (points.length < 3) {
        trends[emotion] = 0;
        return;
      }
      
      // Calculate linear regression
      const n = points.length;
      const sumX = points.reduce((sum, p) => sum + p.x, 0);
      const sumY = points.reduce((sum, p) => sum + p.y, 0);
      const sumXY = points.reduce((sum, p) => sum + (p.x * p.y), 0);
      const sumXX = points.reduce((sum, p) => sum + (p.x * p.x), 0);
      
      // Calculate slope
      const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
      trends[emotion] = slope;
    });
    
    return trends;
  }
  
  /**
   * Get default metrics when not enough data is available
   */
  private static getDefaultMetrics(): EmotionalGrowthMetrics {
    return {
      stability: 50,
      diversity: 50,
      resilience: 50,
      awareness: 50,
      expressiveness: 50,
      growth: 50,
      insights: [
        "Continue conversations with Yubi to generate personalized emotional insights.",
        "Your emotional growth metrics will appear as you share more with Yubi."
      ],
      dominantEmotions: {},
      emotionalTrends: {}
    };
  }
  
  /**
   * Generate a summary of emotional growth over time
   */
  static generateGrowthSummary(data: EmotionalDataPoint[]): string {
    if (!data || data.length < 10) {
      return "Continue your conversations with Yubi to receive a personalized emotional growth summary.";
    }
    
    const metrics = this.calculateGrowthMetrics(data);
    const timeline = this.generateEmotionalTimeline(data);
    
    // Identify growth areas
    const growthAreas: string[] = [];
    
    if (metrics.diversity > 70) growthAreas.push("emotional range");
    if (metrics.resilience > 70) growthAreas.push("emotional resilience");
    if (metrics.awareness > 70) growthAreas.push("self-awareness");
    if (metrics.expressiveness > 70) growthAreas.push("emotional expression");
    
    // Create summary
    let summary = "";
    
    if (growthAreas.length > 0) {
      summary = `Your conversations with Yubi show particular strength in ${growthAreas.join(' and ')}. `;
    } else {
      summary = `Your emotional journey with Yubi is developing across multiple dimensions. `;
    }
    
    // Add trend information
    const positiveEmotionTrends = Object.entries(metrics.emotionalTrends)
      .filter(([emotion, trend]) => this.positiveEmotions.includes(emotion) && trend > 0)
      .map(([emotion]) => emotion);
    
    if (positiveEmotionTrends.length > 0) {
      summary += `You're showing growth in expressing ${positiveEmotionTrends.join(' and ')}, `;
    }
    
    // Add timeline insight
    if (timeline.length >= 2) {
      const firstSegment = timeline[0];
      const lastSegment = timeline[timeline.length - 1];
      
      if (firstSegment.dominantEmotion !== lastSegment.dominantEmotion) {
        summary += `with a shift from ${firstSegment.dominantEmotion} to ${lastSegment.dominantEmotion} over time.`;
      } else {
        summary += `with consistent ${lastSegment.dominantEmotion} as a theme in your conversations.`;
      }
    } else {
      summary += `and your emotional patterns will become clearer with more conversations.`;
    }
    
    return summary;
  }

  /**
   * Get emotional data formatted for visualization
   */
  static getEmotionalDataForVisualization(data: EmotionalDataPoint[]): any[] {
    if (!data || data.length < 3) {
      return [];
    }
    
    // Sort data by timestamp
    const sortedData = [...data].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    // Group by day
    const groupedByDay: Record<string, EmotionalDataPoint[]> = {};
    
    sortedData.forEach(dataPoint => {
      const date = new Date(dataPoint.timestamp);
      const dateKey = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
      if (!groupedByDay[dateKey]) {
        groupedByDay[dateKey] = [];
      }
      
      groupedByDay[dateKey].push(dataPoint);
    });
    
    // Format for visualization
    return Object.entries(groupedByDay).map(([date, points]) => {
      // Create base data point with date
      const visualPoint: Record<string, any> = { date };
      
      // Calculate average intensity for each emotion on this date
      const emotionIntensities: Record<string, number[]> = {};
      
      points.forEach(point => {
        if (point.emotion) {
          if (!emotionIntensities[point.emotion]) {
            emotionIntensities[point.emotion] = [];
          }
          emotionIntensities[point.emotion].push(point.intensity || 0);
        }
      });
      
      // Add average intensity for each emotion to data point
      Object.entries(emotionIntensities).forEach(([emotion, intensities]) => {
        const avgIntensity = intensities.reduce((sum, val) => sum + val, 0) / intensities.length;
        visualPoint[`${emotion}_intensity`] = avgIntensity;
      });
      
      return visualPoint;
    }).sort((a, b) => {
      // Ensure data is sorted by date
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return dateA.getTime() - dateB.getTime();
    });
  }
}

/**
 * Helper functions for using the emotional growth analyzer
 */

/**
 * Get emotional growth metrics for a user
 */
export async function getUserEmotionalGrowthMetrics(userId: string, supabase: any): Promise<EmotionalGrowthMetrics> {
  try {
    // Fetch emotional data for the user
    const { data, error } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, speech_pace, speech_volume, speech_tone, timestamp, emotional_keywords")
      .eq("user_id", userId)
      .order("timestamp", { ascending: true });
    
    if (error) {
      console.error("Error fetching emotional data:", error);
      return EmotionalGrowthAnalyzer.calculateGrowthMetrics([]);
    }
    
    return EmotionalGrowthAnalyzer.calculateGrowthMetrics(data);
  } catch (error) {
    console.error("Error in getUserEmotionalGrowthMetrics:", error);
    return EmotionalGrowthAnalyzer.calculateGrowthMetrics([]);
  }
}

/**
 * Get emotional timeline for a user
 */
export async function getUserEmotionalTimeline(userId: string, supabase: any): Promise<EmotionalTimelineSegment[]> {
  try {
    // Fetch emotional data for the user
    const { data, error } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, speech_pace, speech_volume, speech_tone, timestamp, emotional_keywords")
      .eq("user_id", userId)
      .order("timestamp", { ascending: true });
    
    if (error) {
      console.error("Error fetching emotional data:", error);
      return [];
    }
    
    return EmotionalGrowthAnalyzer.generateEmotionalTimeline(data);
  } catch (error) {
    console.error("Error in getUserEmotionalTimeline:", error);
    return [];
  }
}

/**
 * Get emotional growth summary for a user
 */
export async function getUserGrowthSummary(userId: string, supabase: any): Promise<string> {
  try {
    // Fetch emotional data for the user
    const { data, error } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, speech_pace, speech_volume, speech_tone, timestamp, emotional_keywords")
      .eq("user_id", userId)
      .order("timestamp", { ascending: true });
    
    if (error) {
      console.error("Error fetching emotional data:", error);
      return "Unable to generate growth summary at this time.";
    }
    
    return EmotionalGrowthAnalyzer.generateGrowthSummary(data);
  } catch (error) {
    console.error("Error in getUserGrowthSummary:", error);
    return "Unable to generate growth summary at this time.";
  }
}
