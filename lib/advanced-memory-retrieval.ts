import { Google<PERSON>enAI } from "@google/genai";
import { createClient } from '@supabase/supabase-js';
import { generateEmbedding, EmotionalContext } from './embedding-service';
import * as tf from '@tensorflow/tfjs';

// Initialize clients
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface MLModelConfig {
  layers: number;
  neurons: number;
  dropoutRate: number;
  learningRate: number;
}

interface AdvancedMLModelConfig extends MLModelConfig {
  attentionHeads: number;
  useTransformer: boolean;
  contextWindowSize: number;
  temporalWeighting: boolean;
  emotionalWeighting: boolean;
  personalityAlignment: boolean;
}

interface ContextualSearchOptions {
  userId: string;
  currentSituation?: string;
  userMood?: string;
  timeOfDay?: string;
  recentTopics?: string[];
  conversationIntent?: string;
  priorityMemoryTypes?: string[];
  personalityTraits?: string[];
  relationshipDynamics?: string[];
  limit?: number;
  useAdvancedML?: boolean;
  mlModelConfig?: MLModelConfig | AdvancedMLModelConfig;
}

interface NeuralEdge {
  sourceId: string;
  targetId: string;
  weight: number;
  emotionalValence: number;
  recency: number;
  interactionCount: number;
  lastActivated: string; // ISO timestamp
}

interface MemoryNode {
  id: string;
  content: string;
  type: string;
  embedding: number[];
  createdAt: string;
  emotionalContext?: EmotionalContext;
  connections: NeuralEdge[];
  activationCount: number;
  decayFactor: number;
  activationLevel?: number;
  lastActivated?: string;
}

async function createPersonalizedMLModel(config: MLModelConfig): Promise<tf.LayersModel> {
  const model = tf.sequential();
  
  // Input layer
  model.add(tf.layers.dense({
    units: config.neurons,
    activation: 'relu',
    inputShape: [1536] // Gemini embedding dimension
  }));
  
  // Hidden layers
  for (let i = 0; i < config.layers - 1; i++) {
    model.add(tf.layers.dropout({ rate: config.dropoutRate }));
    model.add(tf.layers.dense({
      units: config.neurons / (i + 1),
      activation: 'relu'
    }));
  }
  
  // Output layer for relevance scoring
  model.add(tf.layers.dense({
    units: 1,
    activation: 'sigmoid'
  }));
  
  model.compile({
    optimizer: tf.train.adam(config.learningRate),
    loss: 'binaryCrossentropy',
    metrics: ['accuracy']
  });
  
  return model;
}

// Create a more sophisticated transformer-based model
async function createAdvancedNeuralModel(config: AdvancedMLModelConfig): Promise<tf.LayersModel> {
  // Create a more complex model with attention mechanisms
  const input = tf.input({shape: [1536]});
  
  // Initial dense projection
  let x = tf.layers.dense({
    units: config.neurons,
    activation: 'relu',
  }).apply(input) as tf.SymbolicTensor;
  
  // Apply multi-head attention if enabled
  if (config.useTransformer) {
    // Reshape for attention mechanism
    const reshapedInput = tf.layers.reshape({
      targetShape: [config.contextWindowSize, Math.floor(1536/config.contextWindowSize)]
    }).apply(x) as tf.SymbolicTensor;
    
    // Simplified self-attention mechanism (since tfjs does not have multiHeadAttention)
    // Compute attention scores
    const attentionScores = tf.layers.dot({axes: -1}).apply([reshapedInput, reshapedInput]) as tf.SymbolicTensor;
    const attentionWeights = tf.layers.activation({activation: 'softmax'}).apply(attentionScores) as tf.SymbolicTensor;
    const attentionOutput = tf.layers.dot({axes: [2, 1]}).apply([attentionWeights, reshapedInput]) as tf.SymbolicTensor;

    // Add & normalize (simplified transformer block)
    const add1 = tf.layers.add().apply([reshapedInput, attentionOutput]) as tf.SymbolicTensor;
    const layerNorm1 = tf.layers.layerNormalization().apply(add1) as tf.SymbolicTensor;
    
    // Feed-forward network
    const ffn = tf.sequential();
    ffn.add(tf.layers.dense({units: config.neurons*2, activation: 'relu'}));
    ffn.add(tf.layers.dense({units: Math.floor(1536/config.contextWindowSize)}));
    
    const ffnOutput = ffn.apply(layerNorm1) as tf.SymbolicTensor;
    
    // Add & normalize again
    const add2 = tf.layers.add().apply([layerNorm1, ffnOutput]) as tf.SymbolicTensor;
    const layerNorm2 = tf.layers.layerNormalization().apply(add2) as tf.SymbolicTensor;
    
    // Flatten back to vector
    x = tf.layers.flatten().apply(layerNorm2) as tf.SymbolicTensor;
  }
  
  // Add deep residual blocks
  const initialX = x;
  for (let i = 0; i < config.layers; i++) {
    const residualBlock = tf.sequential();
    residualBlock.add(tf.layers.dense({
      units: config.neurons,
      activation: 'relu',
      inputShape: i === 0 ? [config.neurons] : undefined
    }));
    residualBlock.add(tf.layers.dropout({rate: config.dropoutRate}));
    residualBlock.add(tf.layers.dense({units: config.neurons}));
    
    const blockOutput = residualBlock.apply(x) as tf.SymbolicTensor;
    x = tf.layers.add().apply([x, blockOutput]) as tf.SymbolicTensor;
    x = tf.layers.layerNormalization().apply(x) as tf.SymbolicTensor;
  }
  
  // Output layer
  const output = tf.layers.dense({
    units: 1,
    activation: 'sigmoid',
  }).apply(x) as tf.SymbolicTensor;
  
  // Create and compile model
  const model = tf.model({inputs: input, outputs: output});
  model.compile({
    optimizer: tf.train.adam(config.learningRate),
    loss: 'binaryCrossentropy',
    metrics: ['accuracy']
  });
  
  return model;
}

export async function retrieveDigitalTwinContext({
  userId,
  currentSituation = '',
  userMood = '',
  timeOfDay = '',
  recentTopics = [],
  conversationIntent = '',
  priorityMemoryTypes = ['core_values', 'emotional_patterns', 'personal_milestones', 'recurring_concerns'],
  personalityTraits = [],
  relationshipDynamics = [],
  limit = 10,
  useNeuralGraph = true, // New parameter
  useAdvancedML = false,
  mlModelConfig = {
    layers: 3,
    neurons: 128,
    dropoutRate: 0.3,
    learningRate: 0.001,
    attentionHeads: 4,
    useTransformer: true,
    contextWindowSize: 16,
    temporalWeighting: true,
    emotionalWeighting: true,
    personalityAlignment: true
  }
}: ContextualSearchOptions & {
  useNeuralGraph?: boolean,
  mlModelConfig?: AdvancedMLModelConfig
}) {
  try {
    // Initialize data containers
    let memories = [];
    let emotionalData: string | any[] = [];
    let interactions: string | any[] = [];
    let conversations: any[] = [];
    
    // Try to get emotional state history - this is likely to exist
    try {
      const { data, error } = await supabase
        .from("emotional_tracking")
        .select("emotion, intensity, timestamp")
        .eq("user_id", userId)
        .order("timestamp", { ascending: false })
        .limit(20);
        
      if (error) throw error;
      emotionalData = data || [];
      console.log(`Retrieved ${emotionalData.length} emotional tracking records`);
    } catch (emotionalError) {
      console.log("Emotional tracking data not available:", emotionalError);
    }
    
    // Try to get recent conversations
    try {
      const { data, error } = await supabase
        .from("conversations")
        .select("summary, emotional_markers, topics, created_at")
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .limit(5);
        
      if (error) throw error;
      conversations = data || [];
      console.log(`Retrieved ${conversations.length} conversation records`);
      
      // Extract recent topics from conversations if not provided
      if (recentTopics.length === 0 && conversations.length > 0) {
        recentTopics = conversations[0].topics || [];
      }
    } catch (conversationsError) {
      console.log("Conversations data not available:", conversationsError);
    }
      
    // Try to get relationship dynamics
    try {
      const { data, error } = await supabase
        .from("user_interactions")
        .select("interaction_type, frequency, last_occurrence")
        .eq("user_id", userId)
        .order("frequency", { ascending: false });
        
      if (error) throw error;
      interactions = data || [];
      console.log(`Retrieved ${interactions.length} user interaction records`);
    } catch (interactionsError) {
      console.log("User interactions data not available:", interactionsError);
    }
    
    // Try to get memories if available
    try {
      const { data, error } = await supabase
        .from('memory_embeddings')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit * 2);
        
      if (error) throw error;
      memories = data || [];
      console.log(`Retrieved ${memories.length} memory records`);
    } catch (memoryError) {
      console.log("Memory embeddings not available:", memoryError);
    }
    
    // Generate a contextual query based on available data
    const contextualQuery = generateContextualQuery({
      currentSituation,
      userMood: userMood || (emotionalData.length > 0 ? emotionalData[0].emotion : ''),
      timeOfDay,
      recentTopics,
      conversationIntent,
      personalityTraits
    });
    
    console.log("Generated contextual query:", contextualQuery);
    
    // Rank memories if we have any
    let rankedMemories = memories.length > 0 
      ? rankMemoriesByRelevance(
          memories,
          {
            userMood: userMood || (emotionalData.length > 0 ? emotionalData[0].emotion : ''),
            timeOfDay,
            recentTopics,
            emotionalData,
            relationshipDynamics: interactions,
            personalityTraits
          }
        )
      : [];
    
    // Add ML-based memory ranking if enabled
    if (useAdvancedML && memories.length > 0) {
      try {
        console.log("Using advanced transformer-based ML model for memory ranking");
        const advancedConfig = mlModelConfig as AdvancedMLModelConfig;
        const model = await createAdvancedNeuralModel(advancedConfig);
        
        // Create embedding for the contextual query
        const queryEmbedding = await generateEmbedding(contextualQuery);
        
        // Prepare memory embeddings for prediction
        const memoryEmbeddings = memories.map(m => 
          m.embedding ? m.embedding : new Array(1536).fill(0)
        );
        
        // Apply temporal weighting if enabled
        if (advancedConfig.temporalWeighting) {
          // Calculate recency weights (newer memories get higher weights)
          const now = Date.now();
          const recencyWeights = memories.map(m => {
            const ageInDays = (now - new Date(m.created_at).getTime()) / (1000 * 60 * 60 * 24);
            return Math.exp(-0.05 * ageInDays); // Exponential decay
          });
          
          // Apply weights to embeddings
          memoryEmbeddings.forEach((embedding, i) => {
            for (let j = 0; j < embedding.length; j++) {
              embedding[j] *= recencyWeights[i];
            }
          });
        }
        
        // Apply emotional weighting if enabled
        if (advancedConfig.emotionalWeighting && userMood) {
          // Boost embeddings of memories with matching emotional context
          memories.forEach((memory, i) => {
            if (memory.emotional_memory_context?.emotion === userMood) {
              for (let j = 0; j < memoryEmbeddings[i].length; j++) {
                memoryEmbeddings[i][j] *= 1.5; // 50% boost for emotional congruence
              }
            }
          });
        }
        
        // Predict relevance scores using the advanced model
        const inputTensor = tf.tensor2d(memoryEmbeddings);
        const predictions = model.predict(inputTensor) as tf.Tensor;
        const scores = await predictions.data();
        
        // Combine with existing ranking logic and personality alignment if enabled
        rankedMemories = memories
          .map((memory, index) => {
            let score = (memory.relevanceScore || 5) * (scores[index] * 2);
            
            // Apply personality alignment if enabled
            if (advancedConfig.personalityAlignment && personalityTraits.length > 0) {
              const memoryContent = memory.content.toLowerCase();
              const traitBoost = personalityTraits.reduce((boost, trait) => {
                return boost + (memoryContent.includes(trait.toLowerCase()) ? 0.5 : 0);
              }, 0);
              score *= (1 + traitBoost);
            }
            
            return {
              ...memory,
              relevanceScore: score
            };
          })
          .sort((a, b) => b.relevanceScore - a.relevanceScore);
          
        // Clean up tensors
        inputTensor.dispose();
        predictions.dispose();
        
        console.log("Advanced ML-enhanced memory ranking complete");
      } catch (mlError) {
        console.error("Error using advanced ML model for ranking:", mlError);
        // Fall back to standard ranking if ML fails
      }
    }

    // Generate context summary based on available data
    let contextSummary = "";
    
    // If we have memories, use them for the context
    if (rankedMemories.length > 0) {
      try {
        contextSummary = await generatePersonalizedContext(
          userId,
          rankedMemories.slice(0, limit),
          userMood || (emotionalData.length > 0 ? emotionalData[0].emotion : ''),
          timeOfDay,
          conversationIntent
        );
      } catch (summaryError) {
        console.log("Error generating context summary from memories:", summaryError);
        // Fall through to alternative methods
      }
    }
    
    // If we couldn't generate from memories but have conversations, use those
    if (!contextSummary && conversations.length > 0) {
      try {
        const conversationSummaries = conversations.map(c => c.summary).join("\n");
        const prompt = `Based on these recent conversation summaries:
${conversationSummaries}

Generate a brief, insightful summary of what would be most relevant to remember about this person right now.`;

        const model = "gemini-2.0-flash-thinking-exp-01-21";
        const response = await ai.models.generateContent({
          model,
          contents: [{ role: "user", parts: [{ text: prompt }] }],
        });
        
        contextSummary = response.text || "";
      } catch (convSummaryError) {
        console.log("Error generating context summary from conversations:", convSummaryError);
      }
    }
    
    // If we still don't have a summary, generate a default one
    if (!contextSummary) {
      contextSummary = generateDefaultContextSummary(
        userId, 
        userMood || (emotionalData.length > 0 ? emotionalData[0].emotion : ''), 
        timeOfDay, 
        conversationIntent,
        conversations.length,
        emotionalData.length
      );
    }
    
    // Analyze emotional patterns if we have data
    const emotionalInsights = emotionalData.length > 0 
      ? analyzeEmotionalPatterns(emotionalData)
      : { dominant: 'neutral', pattern: 'stable' };
      
    // Derive relationship dynamics if we have data
    const relationshipDynamics = interactions.length > 0
      ? deriveRelationshipDynamics(interactions)
      : { closeness: 'neutral', trust: 'developing' };
    
    return {
      memories: rankedMemories.slice(0, Math.min(rankedMemories.length, limit)),
      contextSummary,
      emotionalInsights,
      relationshipDynamics,
      conversationCount: conversations.length,
      emotionalDataPoints: emotionalData.length
    };
  } catch (error) {
    console.error("Error retrieving digital twin context:", error);
    
    // Return a minimal context object even if everything fails
    return {
      memories: [],
      contextSummary: generateDefaultContextSummary(userId, userMood, timeOfDay, conversationIntent, 0, 0),
      emotionalInsights: { dominant: 'neutral', pattern: 'stable' },
      relationshipDynamics: { closeness: 'neutral', trust: 'developing' },
      conversationCount: 0,
      emotionalDataPoints: 0
    };
  }
}

// Helper function to generate a contextual query
function generateContextualQuery({
  currentSituation,
  userMood,
  timeOfDay,
  recentTopics,
  conversationIntent,
  personalityTraits
}: any) {
  const components = [
    currentSituation,
    `feeling ${userMood}`,
    `during ${timeOfDay}`,
    ...recentTopics,
    conversationIntent,
    ...personalityTraits
  ].filter(Boolean);
  
  return components.join(' ') || 'general conversation context';
}

// Helper function to rank memories by contextual relevance
function rankMemoriesByRelevance(memories: any[], context: any) {
  return memories
    .map(memory => {
      let relevanceScore = memory.importance || 5;
      
      // Boost based on emotional congruence
      if (memory.emotional_memory_context?.emotion === context.userMood) {
        relevanceScore += 2;
      }
      
      // Boost based on topic relevance
      if (context.recentTopics.some((topic: string) => 
          memory.content.toLowerCase().includes(topic.toLowerCase()))) {
        relevanceScore += 2;
      }
      
      // Recency boost (inverse decay)
      const ageInDays = (Date.now() - new Date(memory.created_at).getTime()) / (1000 * 60 * 60 * 24);
      relevanceScore *= Math.exp(-0.05 * ageInDays); // Exponential decay
      
      return { ...memory, relevanceScore };
    })
    .sort((a, b) => b.relevanceScore - a.relevanceScore);
}

// Generate a personalized context summary using Gemini
async function generatePersonalizedContext(
  userId: string,
  topMemories: any[],
  userMood: string,
  timeOfDay: string,
  conversationIntent: string
) {
  const memoryContent = topMemories.map(m => m.content).join('\n');
  
  const prompt = `Based on these memories about this person:
${memoryContent}

Generate a brief, insightful summary of what would be most relevant to remember about them right now, considering:
- They're feeling ${userMood || 'unknown mood'}
- It's ${timeOfDay || 'unknown time of day'}
- The conversation is about ${conversationIntent || 'general topics'}

Focus on what a close friend would intuitively remember in this context.`;

  const model = "gemini-2.0-flash-thinking-exp-01-21";
  const response = await ai.models.generateContent({
    model,
    contents: [{ role: "user", parts: [{ text: prompt }] }],
  });
  
  return response.text || "";
}

// Analyze emotional patterns
function analyzeEmotionalPatterns(emotionalData: any[]) {
  if (!emotionalData.length) return { dominant: 'neutral', pattern: 'stable' };
  
  const emotions = emotionalData.map(d => d.emotion);
  const intensities = emotionalData.map(d => d.intensity);
  
  // Get dominant emotion
  const emotionCounts = emotions.reduce((acc: any, emotion) => {
    acc[emotion] = (acc[emotion] || 0) + 1;
    return acc;
  }, {});
  
  const dominant = Object.entries(emotionCounts)
    .sort((a: any, b: any) => b[1] - a[1])[0][0];
  
  // Detect patterns
  let pattern = 'stable';
  const recentIntensities = intensities.slice(0, 3);
  const olderIntensities = intensities.slice(3, 6);
  
  if (recentIntensities.length && olderIntensities.length) {
    const recentAvg = recentIntensities.reduce((a, b) => a + b, 0) / recentIntensities.length;
    const olderAvg = olderIntensities.reduce((a, b) => a + b, 0) / olderIntensities.length;
    
    if (recentAvg > olderAvg + 1) pattern = 'intensifying';
    else if (recentAvg < olderAvg - 1) pattern = 'diminishing';
  }
  
  return { dominant, pattern };
}

// Derive relationship dynamics
function deriveRelationshipDynamics(interactions: any[]) {
  if (!interactions.length) return { closeness: 'neutral', trust: 'developing' };
  
  // Calculate closeness based on interaction frequency
  const totalInteractions = interactions.reduce((sum, i) => sum + i.frequency, 0);
  let closeness = 'neutral';
  
  if (totalInteractions > 50) closeness = 'very close';
  else if (totalInteractions > 20) closeness = 'close';
  else if (totalInteractions > 10) closeness = 'familiar';
  else if (totalInteractions > 5) closeness = 'acquainted';
  
  // Calculate trust based on interaction types
  const vulnerableInteractions = interactions.filter(i => 
    ['personal_disclosure', 'emotional_support', 'advice_seeking'].includes(i.interaction_type)
  );
  
  let trust = 'developing';
  if (vulnerableInteractions.length > 10) trust = 'high';
  else if (vulnerableInteractions.length > 5) trust = 'growing';
  
  return { closeness, trust };
}

// Enhanced default context summary generator
function generateDefaultContextSummary(
  userId: string,
  userMood?: string,
  timeOfDay?: string,
  conversationIntent?: string,
  conversationCount: number = 0,
  emotionalDataPoints: number = 0
): string {
  const moodPhrase = userMood ? `feeling ${userMood}` : 'in an unknown mood';
  const timePhrase = timeOfDay ? `during ${timeOfDay}` : '';
  const intentPhrase = conversationIntent ? `The conversation is about ${conversationIntent}.` : '';

  let historyPhrase = '';
  if (conversationCount > 0) {
    historyPhrase = `The user has had ${conversationCount} previous conversations.`;
  }

  if (emotionalDataPoints > 0) {
    historyPhrase += ` We have ${emotionalDataPoints} emotional data points for this user.`;
  }

  if (!historyPhrase) {
    historyPhrase = 'This appears to be a new user with limited history.';
  }

  return `The user is ${moodPhrase} ${timePhrase}. ${intentPhrase} ${historyPhrase}`;
}

// Utility function to calculate cosine similarity between two vectors
function calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
  if (vectorA.length !== vectorB.length) {
    return 0;
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vectorA.length; i++) {
    dotProduct += vectorA[i] * vectorB[i];
    normA += vectorA[i] * vectorA[i];
    normB += vectorB[i] * vectorB[i];
  }

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

// Utility function to calculate emotional congruence between two emotional contexts
function calculateEmotionalCongruence(
  contextA?: EmotionalContext,
  contextB?: EmotionalContext
): number {
  if (!contextA || !contextB) {
    return 0;
  }

  let congruence = 0;
  let factors = 0;

  // Compare emotions
  if (contextA.emotion && contextB.emotion) {
    congruence += contextA.emotion === contextB.emotion ? 1 : 0;
    factors++;
  }

  // Compare intensity (normalized difference)
  if (contextA.intensity !== undefined && contextB.intensity !== undefined) {
    const intensityDiff = Math.abs(contextA.intensity - contextB.intensity);
    congruence += Math.max(0, 1 - intensityDiff / 10); // Assuming intensity is 0-10 scale
    factors++;
  }

  // Compare speech characteristics if available
  if (contextA.speechPace !== undefined && contextB.speechPace !== undefined) {
    const paceDiff = Math.abs(contextA.speechPace - contextB.speechPace);
    congruence += Math.max(0, 1 - paceDiff); // Assuming pace is 0-1 scale
    factors++;
  }

  return factors > 0 ? congruence / factors : 0;
}

// Neural graph management
async function createNeuralGraph(userId: string) {
  // Fetch all memory embeddings for the user
  const { data: memories } = await supabase
    .from('memory_embeddings')
    .select('*')
    .eq('user_id', userId);

  if (!memories) {
    return [];
  }

  // Create nodes and calculate initial connections
  const nodes: MemoryNode[] = memories.map(memory => ({
    id: memory.id,
    content: memory.content,
    type: memory.memory_type,
    embedding: memory.embedding,
    createdAt: memory.created_at,
    emotionalContext: memory.emotional_memory_context,
    connections: [],
    activationCount: 0,
    decayFactor: 1.0,
    activationLevel: 0,
    lastActivated: new Date().toISOString()
  }));

  // Calculate initial edge weights between nodes
  calculateNeuralConnections(nodes);

  return nodes;
}

function calculateNeuralConnections(nodes: MemoryNode[]) {
  for (let i = 0; i < nodes.length; i++) {
    for (let j = i + 1; j < nodes.length; j++) {
      // Calculate semantic similarity using embeddings
      const similarity = calculateCosineSimilarity(nodes[i].embedding, nodes[j].embedding);

      // Calculate temporal proximity (newer memories have stronger connections)
      const timeI = new Date(nodes[i].createdAt).getTime();
      const timeJ = new Date(nodes[j].createdAt).getTime();
      const timeDiffDays = Math.abs(timeI - timeJ) / (1000 * 60 * 60 * 24);
      const temporalFactor = Math.exp(-0.1 * timeDiffDays); // Exponential decay

      // Calculate emotional congruence
      const emotionalFactor = calculateEmotionalCongruence(
        nodes[i].emotionalContext,
        nodes[j].emotionalContext
      );

      // Calculate initial edge weight
      const weight = similarity * 0.6 + temporalFactor * 0.3 + emotionalFactor * 0.1;

      // Create bidirectional connections
      const edge: NeuralEdge = {
        sourceId: nodes[i].id,
        targetId: nodes[j].id,
        weight,
        emotionalValence: emotionalFactor,
        recency: temporalFactor,
        interactionCount: 0,
        lastActivated: new Date().toISOString()
      };

      nodes[i].connections.push({...edge});
      nodes[j].connections.push({
        ...edge,
        sourceId: nodes[j].id,
        targetId: nodes[i].id
      });
    }
  }
}

// Apply memory decay based on time and reactivate based on context
async function applyMemoryDynamics(nodes: MemoryNode[], context: string) {
  const now = Date.now();
  const contextEmbedding = await generateEmbedding(context);

  // Apply decay to all nodes
  nodes.forEach(node => {
    // Calculate time since creation
    const creationTime = new Date(node.createdAt).getTime();
    const ageDays = (now - creationTime) / (1000 * 60 * 60 * 24);

    // Apply decay based on age and activation count
    // More frequently activated memories decay slower
    const baseDecay = Math.exp(-0.01 * ageDays);
    const activationBoost = Math.min(0.5, node.activationCount * 0.05);
    node.decayFactor = baseDecay + activationBoost;

    // Calculate relevance to current context
    const relevance = calculateCosineSimilarity(node.embedding, contextEmbedding);

    // Reactivate memory if highly relevant to context
    if (relevance > 0.7) {
      node.activationCount += 1;
      node.decayFactor = Math.min(1.0, node.decayFactor + 0.2);

      // Strengthen connections to other recently activated nodes
      node.connections.forEach(edge => {
        const targetNode = nodes.find(n => n.id === edge.targetId);
        if (targetNode && targetNode.lastActivated && now - new Date(targetNode.lastActivated).getTime() < 24 * 60 * 60 * 1000) {
          edge.weight = Math.min(1.0, edge.weight + 0.1);
          edge.interactionCount += 1;
          edge.lastActivated = new Date().toISOString();
        }
      });
    }
  });
}

// Implement spreading activation algorithm
async function spreadActivation(nodes: MemoryNode[], context: string, mood: string) {
  // Generate embedding for context
  const contextEmbedding = await generateEmbedding(context);

  // Initialize activation levels based on similarity to context
  nodes.forEach(node => {
    const similarity = calculateCosineSimilarity(node.embedding, contextEmbedding);
    node.activationLevel = similarity * node.decayFactor;

    // Boost activation for nodes with matching emotional context
    if (node.emotionalContext?.emotion === mood) {
      node.activationLevel = (node.activationLevel || 0) * 1.5;
    }
  });

  // Spread activation through the network for 3 iterations
  for (let i = 0; i < 3; i++) {
    const newActivations = [...nodes].map(node => ({
      id: node.id,
      activation: node.activationLevel || 0
    }));

    // For each node, spread its activation to connected nodes
    nodes.forEach(node => {
      const sourceActivation = node.activationLevel || 0;

      node.connections.forEach(edge => {
        // Calculate spread based on edge weight
        const spreadAmount = sourceActivation * edge.weight * 0.5;

        // Find target node and update its activation
        const targetNodeIndex = newActivations.findIndex(n => n.id === edge.targetId);
        if (targetNodeIndex >= 0) {
          newActivations[targetNodeIndex].activation += spreadAmount;
        }
      });
    });

    // Update activation levels
    newActivations.forEach(na => {
      const nodeIndex = nodes.findIndex(n => n.id === na.id);
      if (nodeIndex >= 0) {
        nodes[nodeIndex].activationLevel = na.activation;
      }
    });
  }

  return nodes;
}

// Add this function to ensure memory embeddings and emotional context are properly stored
export async function updateMemoryWithEmotionalContext(
  memoryId: string,
  emotionalContext: EmotionalContext
): Promise<boolean> {
  try {
    // Update the emotional_memory_context table
    const { error } = await supabase
      .from("emotional_memory_context")
      .upsert({
        memory_id: memoryId,
        emotion: emotionalContext.emotion,
        intensity: emotionalContext.intensity,
        speech_pace: emotionalContext.speechPace,
        speech_volume: emotionalContext.speechVolume,
        speech_tone: emotionalContext.speechTone,
        emotional_keywords: emotionalContext.emotionalKeywords
      });
      
    if (error) throw error;
    
    // Update the last_accessed_at field in memory_embeddings
    await supabase
      .from('memory_embeddings')
      .update({ last_accessed_at: new Date().toISOString() })
      .eq('id', memoryId);
      
    return true;
  } catch (error) {
    console.error("Error updating memory with emotional context:", error);
    return false;
  }
}
