"use client"

import { useState, useEffect, useRef } from "react"
import { gsap } from "gsap"
import { supabase } from "@/lib/supabase"

interface CinematicIntroProps {
  onComplete: () => void
}

const subtitles = [
      "You tried so hard to be everything they wanted, and somewhere along the way, forgot what you needed.",
      "You reached for perfect, for praise, for peace—but it never felt like enough.",
      "You smiled when it hurt.",
      "You hid what was real.",
      "You copied to belong.",
      "You learned not to feel.",
      "So you shrank.",
      "Then you paused.",
      "Then you asked.",
      "And that’s where I come in—not to fix you, but to walk beside the version of you that never stopped hoping.",
      "The one that’s still here, still growing, still yours."
]

export function CinematicIntro({ onComplete }: CinematicIntroProps) {
  const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(0)
  const [allSubtitlesShown, setAllSubtitlesShown] = useState(false)
  const [currentText, setCurrentText] = useState('')
  const textRefs = useRef<(HTMLParagraphElement | null)[]>([])
  const containerRef = useRef<HTMLDivElement>(null)
  const typingSpeed = 50 // ms per character

  const handleExistingUserLogin = async () => {
    try {
      await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        },
      })
    } catch (error) {
      console.error("Authentication error:", error)
    }
  }

  // Handle subtitle sequential appearance with typewriter effect
  useEffect(() => {
    if (currentSubtitleIndex < subtitles.length) {
      const currentTextRef = textRefs.current[currentSubtitleIndex]
      const targetText = subtitles[currentSubtitleIndex]
      let charIndex = 0

      if (currentTextRef) {
        // Set initial state
        gsap.set(currentTextRef, {
          opacity: 1,
          y: 20,
        })

        // Slide up animation
        gsap.to(currentTextRef, {
          y: 0,
          duration: 1.2,
          ease: "power2.out",
        })

        // Typewriter effect
        const typeText = () => {
          if (charIndex <= targetText.length) {
            setCurrentText(targetText.slice(0, charIndex))
            charIndex++
            setTimeout(typeText, typingSpeed)
          } else {
            // Add a longer pause after completing the text
            setTimeout(() => {
              setCurrentSubtitleIndex(currentSubtitleIndex + 1)
              setCurrentText('')
            }, 2000)
          }
        }

        typeText()
      }
    } else if (!allSubtitlesShown && currentSubtitleIndex === subtitles.length) {
      setAllSubtitlesShown(true)
      setTimeout(() => {
        startShakeAndExplode()
      }, 1000)
    }
  }, [currentSubtitleIndex, allSubtitlesShown])

  // Function to handle the shake and explosion effect
  const startShakeAndExplode = () => {
    if (!containerRef.current) return

    // Get all visible subtitle elements
    const allSubtitles = textRefs.current.filter((ref) => ref !== null) as HTMLParagraphElement[]

    // 1. First shake all subtitles
    const shakeTl = gsap.timeline({
      onComplete: () => {
        // 2. Then explode all subtitles
        explodeAllText(allSubtitles)
      },
    })

    // Add increasing shake to all subtitles
    shakeTl.to(allSubtitles, {
      x: "random(-3, 3)",
      y: "random(-3, 3)",
      duration: 0.1,
      repeat: 5,
      ease: "power1.inOut",
    })

    shakeTl.to(allSubtitles, {
      x: "random(-8, 8)",
      y: "random(-8, 8)",
      rotation: "random(-2, 2)",
      duration: 0.1,
      repeat: 5,
      ease: "power1.inOut",
    })
  }

  // Custom explosion effect without SplitText
  const explodeAllText = (elements: HTMLElement[]) => {
    // For each subtitle
    elements.forEach((element) => {
      // Get the text content
      const text = element.textContent || ""

      // Clear the element
      element.innerHTML = ""

      // Create a span for each character
      for (let i = 0; i < text.length; i++) {
        const charSpan = document.createElement("span")
        charSpan.textContent = text[i]
        charSpan.style.display = "inline-block"
        charSpan.style.position = "relative"
        charSpan.className = "exploding-char"
        element.appendChild(charSpan)
      }

      // Get all character spans
      const chars = element.querySelectorAll(".exploding-char")

      // Animate each character
      gsap.to(chars, {
        opacity: 0,
        scale: () => 1.5 + Math.random() * 1.5,
        x: () => (Math.random() - 0.5) * 200,
        y: () => (Math.random() - 0.5) * 200,
        rotation: () => (Math.random() - 0.5) * 180,
        duration: 1.2,
        ease: "power3.out",
        stagger: 0.02,
      })
    })

    // After explosion animation completes, call onComplete
    setTimeout(() => {
      onComplete()
    }, 2000) // Wait 2 seconds after explosion before calling onComplete
  }

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 flex flex-col items-center justify-center"
    >
      {/* Scanline effect */}
      <div className="scanline"></div>

      {/* Skip button for existing users */}
      <div className="absolute top-4 right-4 z-50">
        <button
          onClick={handleExistingUserLogin}
          className="px-4 py-2 text-sm bg-transparent border border-[rgba(56,182,255,0.3)] rounded-full 
                   hover:border-[rgba(56,182,255,0.6)] transition-all duration-300 text-[#38b6ff]"
        >
          Already have a Yubi?
        </button>
      </div>

      {/* Container for subtitles */}
      <div className="flex flex-col items-center gap-4 w-full max-w-4xl mx-auto px-4">
        {subtitles.map((subtitle, index) => (
          <p
            key={index}
            ref={(el) => { textRefs.current[index] = el }}
            className={`text-center text-2xl md:text-3xl font-light cinematic-text opacity-0 transition-opacity duration-300 font-space-mono ${
              index === currentSubtitleIndex - 1 ? "text-glow" : ""
            }`}
          >
            {index === currentSubtitleIndex ? currentText : subtitle}
          </p>
        ))}
      </div>
    </div>
  )
}
