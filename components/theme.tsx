'use client'

import { createContext, useContext, ReactNode } from 'react'
import { Montserrat, Inter, Roboto_Mono } from 'next/font/google'

// Font definitions
export const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
})

export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const robotoMono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto-mono',
})

// Theme configuration
export const themeConfig = {
  // Main color schemes
  colors: {
    // Core UI colors
    primary: {
      light: 'hsl(var(--primary))',
      dark: 'hsl(var(--primary))',
      foreground: 'hsl(var(--primary-foreground))',
    },
    secondary: {
      light: 'hsl(var(--secondary))',
      dark: 'hsl(var(--secondary))',
      foreground: 'hsl(var(--secondary-foreground))',
    },
    
    // Accent colors
    accent: {
      blue: 'rgb(var(--accent-blue))',
      purple: 'rgb(var(--accent-purple))',
      cyan: 'rgb(var(--accent-cyan))',
      neonBlue: 'rgb(var(--neon-blue))',
      neonPurple: 'rgb(var(--neon-purple))',
    },
    
    // Background colors
    background: {
      light: 'hsl(var(--background))',
      dark: 'hsl(var(--background))',
      deepBlue: 'rgb(var(--dark-blue))',
      darkerBlue: 'rgb(var(--darker-blue))',
      deepestBlue: 'rgb(var(--deepest-blue))',
      deepPurple: 'rgb(var(--deep-purple))',
    },
    
    // Chart colors
    chart: {
      c1: 'hsl(var(--chart-1))',
      c2: 'hsl(var(--chart-2))',
      c3: 'hsl(var(--chart-3))',
      c4: 'hsl(var(--chart-4))',
      c5: 'hsl(var(--chart-5))',
    },
    
    // Sidebar colors
    sidebar: {
      background: 'hsl(var(--sidebar-background))',
      foreground: 'hsl(var(--sidebar-foreground))',
      primary: 'hsl(var(--sidebar-primary))',
      accent: 'hsl(var(--sidebar-accent))',
      border: 'hsl(var(--sidebar-border))',
    },
    
    // Additional colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
      heading: ['var(--font-montserrat)', 'system-ui', 'sans-serif'],
      mono: ['var(--font-roboto-mono)', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
  },
  
  // Spacing
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '4rem',
  },
  
  // Borders
  borders: {
    radius: {
      sm: 'calc(var(--radius) - 4px)',
      md: 'calc(var(--radius) - 2px)',
      lg: 'var(--radius)',
      full: '9999px',
    },
  },
  
  // Effects
  effects: {
    textGlow: 'var(--text-glow)',
    boxShadow: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    },
  },
}

// Theme context
type ThemeContextType = typeof themeConfig
const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: ReactNode }) {
  return (
    <ThemeContext.Provider value={themeConfig}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}