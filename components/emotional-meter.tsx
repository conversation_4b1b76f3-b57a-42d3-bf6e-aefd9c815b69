"use client"

import { useEffect, useRef, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { gsap } from "gsap"
import { Heart, AlertTriangle, <PERSON>rk<PERSON>, Zap } from "lucide-react"

interface EmotionalMeterProps {
  emotionalState: any | null
  showDetails?: boolean
  className?: string
}

// Emotion color mapping
const emotionColors: Record<string, string> = {
  joy: "#FFD700",        // Gold
  sadness: "#4169E1",    // Royal Blue
  anger: "#FF4500",      // Red Orange
  fear: "#800080",       // Purple
  surprise: "#00FFFF",   // Cyan
  disgust: "#8B4513",    // Saddle Brown
  trust: "#32CD32",      // Lime Green
  anticipation: "#FFA500", // Orange
  neutral: "#CCCCCC",    // Gray
  confusion: "#9370DB",  // Medium Purple
  fatigue: "#708090"     // Slate Gray
}

// Default color for unknown emotions
const defaultColor = "#ff37a6" // Pink

export function EmotionalMeter({ 
  emotionalState, 
  showDetails = false,
  className = "" 
}: EmotionalMeterProps) {
  const meterRef = useRef<HTMLDivElement>(null)
  const intensityRef = useRef<HTMLDivElement>(null)
  const pulseRef = useRef<HTMLDivElement>(null)
  const [showThresholdAlert, setShowThresholdAlert] = useState(false)
  const [previousIntensity, setPreviousIntensity] = useState(0)
  const [currentEmotion, setCurrentEmotion] = useState<string | null>(null)
  
  // Calculate intensity from emotional state
  const calculateIntensity = () => {
    if (!emotionalState) return 0
    
    // Base intensity on dominant emotion intensity or average of available metrics
    let intensity = 0
    
    if (emotionalState.dominantEmotion && emotionalState.emotionCounts) {
      // Use emotion counts as a factor
      const emotionCount = emotionalState.emotionCounts[emotionalState.dominantEmotion] || 0
      intensity += Math.min(5, emotionCount)
    }
    
    // Factor in speech volume (0-100 scale)
    if (typeof emotionalState.averageVolume === 'number') {
      intensity += (emotionalState.averageVolume / 20) // Convert 0-100 to 0-5 scale
    }
    
    // Factor in emotional keywords
    if (emotionalState.keywordCounts) {
      const keywordIntensity = Object.values(emotionalState.keywordCounts).reduce(
        (sum: number, count: any) => sum + (count as number), 0
      )
      intensity += Math.min(3, keywordIntensity / 2) // Cap at 3
    }
    
    // Normalize to 0-10 scale
    return Math.min(10, Math.max(0, intensity))
  }
  
  // Get color based on dominant emotion
  const getEmotionColor = () => {
    if (!emotionalState?.dominantEmotion) return defaultColor
    return emotionColors[emotionalState.dominantEmotion] || defaultColor
  }
  
  // Update meter visualization
  useEffect(() => {
    if (!meterRef.current || !intensityRef.current || !pulseRef.current) return
    
    const intensity = calculateIntensity()
    const color = getEmotionColor()
    
    // Check if emotion has changed
    if (emotionalState?.dominantEmotion !== currentEmotion) {
      setCurrentEmotion(emotionalState?.dominantEmotion || null)
    }
    
    // Animate intensity bar
    gsap.to(intensityRef.current, {
      width: `${intensity * 10}%`,
      backgroundColor: color,
      duration: 0.8,
      ease: "power2.out"
    })
    
    // Animate pulse effect
    gsap.to(pulseRef.current, {
      boxShadow: `0 0 ${intensity * 3}px ${color}`,
      opacity: intensity / 10,
      duration: 0.8,
      ease: "power2.out"
    })
    
    // Check for significant changes in intensity
    if (Math.abs(intensity - previousIntensity) > 3 && previousIntensity > 0) {
      setShowThresholdAlert(true)
      
      // Hide alert after 3 seconds
      setTimeout(() => {
        setShowThresholdAlert(false)
      }, 3000)
    }
    
    setPreviousIntensity(intensity)
  }, [emotionalState])
  
  // No emotional data yet
  if (!emotionalState) {
    return (
      <div className={`flex items-center justify-center p-3 bg-[rgba(10,17,40,0.4)] 
                      backdrop-blur-sm rounded-lg border border-[rgba(56,182,255,0.1)] ${className}`}>
        <Heart className="w-5 h-5 text-gray-400 mr-2" />
        <span className="text-gray-400 text-sm">Analyzing emotional state...</span>
      </div>
    )
  }
  
  return (
    <div className={`relative p-3 bg-[rgba(10,17,40,0.4)] backdrop-blur-sm 
                    rounded-lg border border-[rgba(56,182,255,0.1)] ${className}`}>
      {/* Emotion indicator */}
      <div className="flex items-center mb-2">
        <Heart 
          className="w-5 h-5 mr-2" 
          style={{ color: getEmotionColor() }} 
        />
        <span className="text-white text-sm font-medium">
          {emotionalState.dominantEmotion 
            ? emotionalState.dominantEmotion.charAt(0).toUpperCase() + emotionalState.dominantEmotion.slice(1) 
            : "Neutral"}
        </span>
        
        {/* Trend indicator */}
        {emotionalState.trend && (
          <span 
            className={`ml-auto text-xs px-2 py-0.5 rounded-full ${
              emotionalState.trend === 'positive' 
                ? 'bg-green-900/30 text-green-400' 
                : emotionalState.trend === 'negative' 
                  ? 'bg-red-900/30 text-red-400' 
                  : 'bg-gray-900/30 text-gray-400'
            }`}
          >
            {emotionalState.trend === 'positive' 
              ? 'Improving' 
              : emotionalState.trend === 'negative' 
                ? 'Declining' 
                : 'Stable'}
          </span>
        )}
      </div>
      
      {/* Intensity meter */}
      <div ref={meterRef} className="h-2 bg-[rgba(255,255,255,0.1)] rounded-full overflow-hidden">
        <div 
          ref={intensityRef} 
          className="h-full rounded-full" 
          style={{ width: '0%', backgroundColor: getEmotionColor() }}
        />
      </div>
      
      {/* Pulse effect */}
      <div 
        ref={pulseRef}
        className="absolute inset-0 rounded-lg opacity-0 pointer-events-none"
      />
      
      {/* Threshold alert */}
      <AnimatePresence>
        {showThresholdAlert && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute -top-12 left-1/2 transform -translate-x-1/2 
                      bg-[rgba(10,17,40,0.9)] px-3 py-2 rounded-lg border border-[rgba(255,55,166,0.3)]
                      flex items-center shadow-lg"
          >
            <AlertTriangle className="w-4 h-4 text-[#ff37a6] mr-2" />
            <span className="text-white text-xs">Significant emotional shift detected</span>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Details section (optional) */}
      {showDetails && (
        <div className="mt-3 pt-3 border-t border-[rgba(56,182,255,0.1)]">
          {/* Top keywords */}
          {emotionalState.topKeywords && emotionalState.topKeywords.length > 0 && (
            <div className="mb-2">
              <div className="text-xs text-gray-400 mb-1 flex items-center">
                <Sparkles className="w-3 h-3 mr-1" />
                Key emotional words:
              </div>
              <div className="flex flex-wrap gap-1">
                {emotionalState.topKeywords.map((keyword: string, index: number) => (
                  <span 
                    key={index}
                    className="text-xs px-2 py-0.5 rounded-full bg-[rgba(56,182,255,0.1)] text-[#38b6ff]"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {/* Speech metrics */}
          <div className="grid grid-cols-2 gap-2">
            {/* Pace */}
            {typeof emotionalState.averagePace === 'number' && (
              <div className="text-xs">
                <span className="text-gray-400 flex items-center">
                  <Zap className="w-3 h-3 mr-1" />
                  Speech pace:
                </span>
                <span className="text-white">
                  {emotionalState.averagePace < 80 
                    ? 'Slow' 
                    : emotionalState.averagePace > 160 
                      ? 'Fast' 
                      : 'Normal'} 
                  ({emotionalState.averagePace} wpm)
                </span>
              </div>
            )}
            
            {/* Tone */}
            {typeof emotionalState.averageTone === 'string' && (
              <div className="text-xs">
                <span className="text-gray-400 flex items-center">
                  <Heart className="w-3 h-3 mr-1" />
                  Emotional tone:
                </span>
                <span className="text-white">
                  {parseFloat(emotionalState.averageTone) > 0.2 
                    ? 'Positive' 
                    : parseFloat(emotionalState.averageTone) < -0.2 
                      ? 'Negative' 
                      : 'Neutral'} 
                  ({parseFloat(emotionalState.averageTone).toFixed(1)})
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}