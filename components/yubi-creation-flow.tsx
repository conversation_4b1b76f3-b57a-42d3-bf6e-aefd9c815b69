"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { supabase } from "@/lib/supabase"
import { <PERSON><PERSON><PERSON>, Send, Loader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2 } from "lucide-react"
import { gsap } from "gsap"
import { TextPlugin } from "gsap/TextPlugin"

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(TextPlugin)
}

// Define SpeechRecognition type
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}

// The 6 questions with their improved Gemini prompts with scientific backing
const QUESTIONS = [
  {
    id: "name",
    prompt:
      "You are <PERSON><PERSON>, a gentle AI companion grounded in interpersonal neurobiology. Ask the user for their name in a single, warm sentence that activates the brain's social engagement system. Your question should create psychological safety through prosodic cues in language. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
  {
    id: "feeling",
    prompt:
      "You are <PERSON><PERSON>, a gentle AI companion trained in affective neuroscience. Ask the user how they're really feeling today in a single, thoughtful sentence. Your question should encourage interoception (internal body awareness) and emotional labeling, which research shows reduces amygdala activity. Be genuine and caring. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
  {
    id: "wish",
    prompt:
      "You are Yubi, a gentle AI companion versed in narrative identity theory. Ask the user what they wish people asked them more often in a single, thoughtful sentence. This question activates the medial prefrontal cortex, associated with self-knowledge and social cognition. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
  {
    id: "dream",
    prompt:
      "You are Yubi, a gentle AI companion informed by positive psychology research. Ask the user to share a dream they have, even if it feels too big or far away, in a single, encouraging sentence. This question engages the brain's prospection systems and activates reward circuitry associated with hope and possibility. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
  {
    id: "fear",
    prompt:
      "You are Yubi, a gentle AI companion trained in emotional processing theory. Ask the user to share something that scares them but matters to them anyway in a single, supportive sentence. This question helps process fear through cognitive labeling, which research shows reduces amygdala reactivity. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
  {
    id: "remember",
    prompt:
      "You are Yubi, a gentle AI companion grounded in memory consolidation research. Ask the user what one thing they would want you to remember about them in a single, meaningful sentence. This question engages hippocampal-cortical networks involved in autobiographical memory and identity formation. Do not use asterisks, parentheses, or any special formatting. Do not include directorial notes or tone instructions. Just ask the question directly in plain text.",
  },
]

interface YubiCreationFlowProps {
  userId: string
}

export function YubiCreationFlow({ userId }: YubiCreationFlowProps) {
  const [hasStarted, setHasStarted] = useState(false)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [questionText, setQuestionText] = useState("")
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [userResponse, setUserResponse] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isComplete, setIsComplete] = useState(false)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const [responses, setResponses] = useState<Record<string, string>>({})
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const questionRef = useRef<HTMLHeadingElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Voice input states
  const [isListening, setIsListening] = useState(false)
  const [speechRecognition, setSpeechRecognition] = useState<any | null>(null)
  const [isVoiceSupported, setIsVoiceSupported] = useState(false)
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false)

  // Add these state variables at the top of YubiCreationFlow
  const [completionMessage, setCompletionMessage] = useState("")
  const [completionAudio, setCompletionAudio] = useState<HTMLAudioElement | null>(null)
  const [isGeneratingCompletion, setIsGeneratingCompletion] = useState(false)

  // Check if speech recognition is supported
  useEffect(() => {
    if (typeof window !== "undefined") {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      if (SpeechRecognition) {
        setIsVoiceSupported(true)
      } else {
        console.log("Speech recognition not supported")
        setIsVoiceSupported(false)
      }
    }
  }, [])

  // Initialize GSAP animations
  useEffect(() => {
    if (containerRef.current) {
      // Create a subtle floating animation for the container
      gsap.to(containerRef.current, {
        y: 10,
        duration: 4, /////////////////
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      })
    }
  }, [])

  // Load the current question
  useEffect(() => {
    if (hasStarted && currentQuestionIndex < QUESTIONS.length) {
      loadQuestion(QUESTIONS[currentQuestionIndex].prompt)
    } else if (currentQuestionIndex >= QUESTIONS.length) {
      setIsComplete(true)
      saveAllResponses()
    }
  }, [currentQuestionIndex, hasStarted])

  // Animate question text when it changes
  useEffect(() => {
    if (questionRef.current && questionText && !isLoading) {
      // Clear any existing animations
      gsap.killTweensOf(questionRef.current)

      // Set initial state
      gsap.set(questionRef.current, { opacity: 0, y: 20 })

      // Animate in
      gsap.to(questionRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out",
      })

      // Optional: Add a typing effect
      // gsap.to(questionRef.current, {
      //   text: questionText,
      //   duration: 1.5,
      //   ease: "none",
      //   delay: 0.3
      // })
    }
  }, [questionText, isLoading])

  // Function to load a question
  async function loadQuestion(prompt: string) {
    setIsLoading(true)
    setAudioUrl(null)
    setUserResponse("")

    try {
      // Include previous responses for context
      const previousResponses = { ...responses }

      const response = await fetch("/api/generate-question", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          previousResponses,
          questionIndex: currentQuestionIndex,
        }),
      })

      const data = await response.json()

      if (data.error) {
        console.error("Error generating question:", data.error)
        setQuestionText("I'm having trouble connecting. Can you tell me your answer?")
        setIsLoading(false)
        return
      }

      setQuestionText(data.message)

      // Create audio element if we have audio data
      if (data.audio) {
        const audioUrl = `data:audio/mpeg;base64,${data.audio}`
        setAudioUrl(audioUrl)

        const audio = new Audio(audioUrl)
        setAudioElement(audio)

        audio.oncanplaythrough = () => {
          setIsLoading(false)
          audio.play().catch((err) => {
            console.error("Failed to play audio:", err)
            setIsLoading(false)
          })
        }

        audio.onerror = () => {
          console.error("Audio playback failed")
          setIsLoading(false)
        }
      } else {
        setIsLoading(false)
      }
    } catch (error) {
      console.error("Failed to load question:", error)
      setQuestionText("I'm having trouble connecting. Can you tell me your answer?")
      setIsLoading(false)
    }
  }

  // Function to handle user response submission
  async function handleSubmitResponse() {
    if (!userResponse.trim() || isSaving) return

    setIsSaving(true)

    // Store the response
    const currentQuestion = QUESTIONS[currentQuestionIndex]
    const updatedResponses = {
      ...responses,
      [currentQuestion.id]: userResponse.trim(),
    }
    setResponses(updatedResponses)

    // Save the individual response to Supabase
    try {
      await supabase.from("yubi_responses").upsert({
        user_id: userId,
        question_id: currentQuestion.id,
        response: userResponse.trim(),
        created_at: new Date().toISOString(),
      })

      console.log(`Response saved to Supabase: ${currentQuestion.id} = ${userResponse.trim()}`)
    } catch (error) {
      console.error("Failed to save response:", error)
    }

    // Animate out the current question
    if (containerRef.current) {
      gsap.to(containerRef.current, {
        opacity: 0,
        y: -20,
        duration: 0.5,
        onComplete: () => {
          // Move to the next question
          setIsSaving(false)
          setCurrentQuestionIndex(currentQuestionIndex + 1)
        },
      })
    } else {
      // Fallback if ref is not available
      setIsSaving(false)
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  // Function to save all responses at the end
  async function saveAllResponses() {
    try {
      // Update the user profile with completion status
      await supabase.from("profiles").upsert({
        id: userId,
        yubi_creation_complete: true,
        updated_at: new Date().toISOString(),
      })

      console.log("Profile updated with completion status")
    } catch (error) {
      console.error("Failed to update profile:", error)
    }
  }

  // Handle key press for submitting with Enter
  function handleKeyPress(e: React.KeyboardEvent) {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmitResponse()
    }
  }

  // Function to start voice recognition
  function startListening() {
    if (!isVoiceSupported) return

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.lang = "en-US"
    recognition.interimResults = true
    recognition.continuous = true

    recognition.onstart = () => {
      setIsListening(true)
      setIsProcessingSpeech(false)
    }

    recognition.onresult = (event: { results: Iterable<unknown> | ArrayLike<unknown> }) => {
      const transcript = Array.from(event.results)
        .map((result) => ((result as SpeechRecognitionResult)[0]).transcript)
        .join("")

      setUserResponse(transcript)

      // Auto-stop after a pause in speaking
      if ((event.results as SpeechRecognitionResultList)[0].isFinal) {
        setIsProcessingSpeech(true)
      }
    }

    recognition.onerror = (event: { error: any }) => {
      console.error("Speech recognition error", event.error)
      setIsListening(false)
      setIsProcessingSpeech(false)
    }

    recognition.onend = () => {
      setIsListening(false)
      setIsProcessingSpeech(false)
    }

    recognition.start()
    setSpeechRecognition(recognition)
  }

  // Function to stop voice recognition
  function stopListening() {
    if (speechRecognition) {
      speechRecognition.stop()
      setIsListening(false)
    }
  }

  // Replay the current question audio
  function replayQuestion() {
    if (audioElement) {
      audioElement.currentTime = 0
      audioElement.play().catch((err) => {
        console.error("Failed to replay audio:", err)
      })
    }
  }

  // Add this function to generate the completion message
  async function generateCompletionMessage() {
    setIsGeneratingCompletion(true)
    try {
      const response = await fetch("/api/generate-completion", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          responses,
        }),
      })

      const data = await response.json()

      if (data.error) {
        console.error("Error generating completion:", data.error)
        return
      }

      setCompletionMessage(data.message)

      if (data.audio) {
        const audioUrl = `data:audio/mpeg;base64,${data.audio}`
        const audio = new Audio(audioUrl)
        setCompletionAudio(audio)
        audio.play().catch(console.error)
      }
    } catch (error) {
      console.error("Failed to generate completion:", error)
    } finally {
      setIsGeneratingCompletion(false)
    }
  }

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-4">
      {!hasStarted ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl w-full futuristic-panel p-8 rounded-lg text-center"
        >
          <h2 className="text-2xl md:text-3xl font-light text-glow cinematic-text mb-8">
            Ready to create your Yubi?
          </h2>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setHasStarted(true)}
            className="px-8 py-4 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full text-lg font-medium neon-border"
          >
            <span className="text-glow">Begin</span>
          </motion.button>
        </motion.div>
      ) : (
        <AnimatePresence mode="wait">
          {isComplete ? (
            <motion.div
              key="complete"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.8 }}
              className="max-w-2xl w-full text-center futuristic-panel p-8 rounded-lg"
            >
              <Sparkles className="w-16 h-16 mx-auto mb-6 text-[#38b6ff] cinematic-pulse" />
              <h1 className="text-3xl md:text-4xl font-light mb-6 text-glow">Your Yubi has been created</h1>
              
              {!completionMessage ? (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={generateCompletionMessage}
                  disabled={isGeneratingCompletion}
                  className="px-8 py-4 mb-8 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full text-lg font-medium neon-border"
                >
                  {isGeneratingCompletion ? (
                    <span className="flex items-center">
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      <span className="text-glow">Yubi is processing...</span>
                    </span>
                  ) : (
                    <span className="text-glow">Hear what Yubi thinks</span>
                  )}
                </motion.button>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mb-8 text-xl text-gray-300 cinematic-text"
                >
                  {completionMessage}
                </motion.div>
              )}

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.98 }}
                className="px-8 py-4 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full text-lg font-medium neon-border"
                onClick={() => (window.location.href = "/dashboard")}
              >
                <span className="text-glow">Continue to Your Yubi</span>
              </motion.button>
            </motion.div>
          ) : (
            <motion.div
              ref={containerRef}
              key="question"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="max-w-2xl w-full futuristic-panel p-8 rounded-lg"
            >
              <div className="mb-12">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-[#38b6ff] mr-2 cinematic-pulse" />
                    <span className="text-sm text-gray-400">
                      Question {currentQuestionIndex + 1} of {QUESTIONS.length}
                    </span>
                  </div>

                  {audioUrl && (
                    <button
                      onClick={replayQuestion}
                      className="flex items-center text-sm text-gray-400 hover:text-[#38b6ff] transition-colors"
                    >
                      <Volume2 className="w-4 h-4 mr-1" />
                      <span>Replay</span>
                    </button>
                  )}
                </div>

                <div className="min-h-[100px] flex items-center">
                  {isLoading ? (
                    <div className="flex items-center text-gray-300">
                      <Loader2 className="w-5 h-5 mr-2 animate-spin text-[#38b6ff]" />
                      <span>Yubi is thinking...</span>
                    </div>
                  ) : (
                    <h2 ref={questionRef} className="text-2xl md:text-3xl font-light text-glow cinematic-text">
                      {questionText}
                    </h2>
                  )}
                </div>
              </div>

              <div className="relative">
                <textarea
                  ref={inputRef}
                  value={userResponse}
                  onChange={(e) => setUserResponse(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder={isListening ? "Listening..." : "Type your response or use the microphone..."}
                  className={`w-full p-4 pr-24 bg-[rgba(10,17,40,0.4)] border ${
                    isListening ? "neon-purple-border" : "border-[rgba(56,182,255,0.3)]"
                  } rounded-lg text-white resize-none h-32 focus:outline-none focus:ring-2 focus:ring-[#38b6ff] focus:border-transparent`}
                  disabled={isLoading || isSaving || isListening}
                />

                <div className="absolute right-3 bottom-3 flex">
                  {isVoiceSupported && (
                    <button
                      onClick={isListening ? stopListening : startListening}
                      disabled={isLoading || isSaving}
                      className={`p-2 mr-2 rounded-full ${
                        isListening
                          ? "bg-[#a137ff] text-white animate-pulse"
                          : "bg-[rgba(56,182,255,0.2)] text-white hover:bg-[rgba(56,182,255,0.3)]"
                      } disabled:opacity-50 disabled:cursor-not-allowed transition-colors`}
                      aria-label={isListening ? "Stop recording" : "Start recording"}
                    >
                      {isListening ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
                    </button>
                  )}

                  <button
                    onClick={handleSubmitResponse}
                    disabled={!userResponse.trim() || isLoading || isSaving || isListening}
                    className="p-2 rounded-full bg-[#38b6ff] text-white disabled:opacity-50 disabled:cursor-not-allowed glow-effect"
                    aria-label="Send response"
                  >
                    {isSaving ? <Loader2 className="w-5 h-5 animate-spin" /> : <Send className="w-5 h-5" />}
                  </button>
                </div>

                {isListening && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="absolute left-0 -bottom-8 text-sm text-[#a137ff] flex items-center"
                  >
                    <span className="mr-2">{isProcessingSpeech ? "Processing..." : "Listening..."}</span>
                    <span className="flex space-x-1 typing-indicator">
                      <span className="w-1 h-1 bg-[#a137ff] rounded-full"></span>
                      <span className="w-1 h-1 bg-[#a137ff] rounded-full"></span>
                      <span className="w-1 h-1 bg-[#a137ff] rounded-full"></span>
                    </span>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  )
}
