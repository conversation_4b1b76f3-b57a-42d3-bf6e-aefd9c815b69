"use client"

import { useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Spark<PERSON> } from "lucide-react"
import { gsap } from "gsap"

interface CreateYubiButtonProps {
  onClick: () => void
}

export function CreateYubiButton({ onClick }: CreateYubiButtonProps) {
  const buttonRef = useRef<HTMLButtonElement>(null)
  const glowRef = useRef<HTMLDivElement>(null)
  const sparklesRef = useRef<SVGSVGElement>(null)

  useEffect(() => {
    if (buttonRef.current && glowRef.current && sparklesRef.current) {
      // Create a pulsing glow effect
      gsap.to(glowRef.current, {
        opacity: 0.7,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      })

      // Animate sparkles
      gsap.to(sparklesRef.current, {
        rotate: 10,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      })

      // Create a subtle hover animation
      buttonRef.current.addEventListener("mouseenter", () => {
        gsap.to(buttonRef.current, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out",
        })

        gsap.to(glowRef.current, {
          opacity: 1,
          duration: 0.3,
        })
      })

      buttonRef.current.addEventListener("mouseleave", () => {
        gsap.to(buttonRef.current, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out",
        })

        gsap.to(glowRef.current, {
          opacity: 0.7,
          duration: 0.3,
        })
      })
    }
  }, [])

  return (
    <div className="relative">
      {/* Glow effect */}
      <div
        ref={glowRef}
        className="absolute inset-0 bg-gradient-to-r from-[rgba(190,75,255,0.4)] to-[rgba(56,182,255,0.4)] rounded-full blur-xl opacity-0"
      ></div>

      <motion.button
        ref={buttonRef}
        onClick={onClick}
        className="relative px-8 py-4 bg-gradient-to-r from-[#be4bff] to-[#38b6ff] text-white rounded-full text-lg font-medium 
                 shadow-lg z-10 neon-purple-border"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="flex items-center">
          <Sparkles ref={sparklesRef} className="mr-2 h-5 w-5 text-white" />
          <span className="text-glow">Create your own Yubi</span>
        </div>
      </motion.button>
    </div>
  )
}
