'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GmailIntegrationProps {
  userId: string;
}

interface GmailStatus {
  connected: boolean;
  email?: string;
  lastSync?: string;
  error?: string;
}

export function GmailIntegration({ userId }: GmailIntegrationProps) {
  const [gmailStatus, setGmailStatus] = useState<GmailStatus>({ connected: false });
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    checkGmailStatus();
  }, [userId]);

  const checkGmailStatus = async () => {
    try {
      const response = await fetch('/api/gmail/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      if (response.ok) {
        const data = await response.json();
        setGmailStatus(data);
      } else {
        setGmailStatus({ connected: false, error: 'Failed to check Gmail status' });
      }
    } catch (error) {
      console.error('Error checking Gmail status:', error);
      setGmailStatus({ connected: false, error: 'Connection error' });
    } finally {
      setLoading(false);
    }
  };

  const connectGmail = async () => {
    setConnecting(true);
    try {
      const response = await fetch(`/api/gmail/auth?userId=${userId}`);
      const data = await response.json();

      if (data.authUrl) {
        // Open Gmail OAuth in a new window
        const authWindow = window.open(
          data.authUrl,
          'gmail-auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        // Poll for window closure (user completed auth)
        const pollTimer = setInterval(() => {
          if (authWindow?.closed) {
            clearInterval(pollTimer);
            // Check status again after auth
            setTimeout(() => {
              checkGmailStatus();
              setConnecting(false);
            }, 1000);
          }
        }, 1000);

        // Timeout after 5 minutes
        setTimeout(() => {
          clearInterval(pollTimer);
          if (authWindow && !authWindow.closed) {
            authWindow.close();
          }
          setConnecting(false);
        }, 300000);
      } else {
        throw new Error('Failed to get authorization URL');
      }
    } catch (error) {
      console.error('Error connecting Gmail:', error);
      toast({
        title: 'Connection Failed',
        description: 'Failed to connect to Gmail. Please try again.',
        variant: 'destructive'
      });
      setConnecting(false);
    }
  };

  const disconnectGmail = async () => {
    try {
      const response = await fetch('/api/gmail/disconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      if (response.ok) {
        setGmailStatus({ connected: false });
        toast({
          title: 'Gmail Disconnected',
          description: 'Your Gmail account has been disconnected from Yubi.'
        });
      } else {
        throw new Error('Failed to disconnect Gmail');
      }
    } catch (error) {
      console.error('Error disconnecting Gmail:', error);
      toast({
        title: 'Disconnection Failed',
        description: 'Failed to disconnect Gmail. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const testEmailSummary = async () => {
    try {
      const response = await fetch('/api/gmail/summarize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: 'Email Summary Test',
          description: `Found ${data.totalEmails} emails with ${data.unreadCount} unread.`
        });
      } else {
        throw new Error(data.error || 'Test failed');
      }
    } catch (error) {
      console.error('Error testing email summary:', error);
      toast({
        title: 'Test Failed',
        description: 'Failed to test email summary. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Gmail Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="w-5 h-5" />
          Gmail Integration
        </CardTitle>
        <CardDescription>
          Connect your Gmail account to enable email management through voice commands
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            {gmailStatus.connected ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                <AlertCircle className="w-3 h-3 mr-1" />
                Not Connected
              </Badge>
            )}
          </div>
        </div>

        {gmailStatus.connected && gmailStatus.email && (
          <div className="text-sm text-gray-600">
            Connected as: <span className="font-medium">{gmailStatus.email}</span>
          </div>
        )}

        {gmailStatus.error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
            {gmailStatus.error}
          </div>
        )}

        <div className="flex gap-2">
          {!gmailStatus.connected ? (
            <Button 
              onClick={connectGmail} 
              disabled={connecting}
              className="flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              {connecting ? 'Connecting...' : 'Connect Gmail'}
            </Button>
          ) : (
            <>
              <Button 
                onClick={testEmailSummary}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Mail className="w-4 h-4" />
                Test Email Summary
              </Button>
              <Button 
                onClick={disconnectGmail}
                variant="outline"
                className="text-red-600 hover:text-red-700"
              >
                Disconnect
              </Button>
            </>
          )}
        </div>

        {gmailStatus.connected && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Voice Commands Available:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• "Hey Yubi, summarize my emails"</li>
              <li>• "Hey Yubi, what are my emails today?"</li>
              <li>• "Hey Yubi, send an email to [name] saying [message]"</li>
              <li>• "Hey Yubi, reply to [name] with [message]"</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
