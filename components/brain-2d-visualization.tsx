"use client";

import { useState } from "react";
import { Loader2 } from "lucide-react";

interface BrainRegionData {
  name: string;
  activity: number;
}

interface Brain2DVisualizationProps {
  brainRegionData: BrainRegionData[];
  isLoading?: boolean;
}

export function Brain2DVisualization({ brainRegionData, isLoading = false }: Brain2DVisualizationProps) {
  const [selectedRegion, setSelectedRegion] = useState<BrainRegionData | null>(null);

  // Get color based on activity level (0-10)
  const getColorForActivity = (activity: number): string => {
    // Blue (low) to purple (medium) to pink (high)
    if (activity < 3.33) return "#38b6ff";
    if (activity < 6.67) return "#a137ff";
    return "#ff37a6";
  };

  // Define proper mapping of brain regions to visual coordinates and paths
  const brainRegionMap: Record<string, {path: string, x: number, y: number}> = {
    "Hippocampus": {
      path: "M40,60 C42,63 45,64 48,63 C51,62 53,59 52,56 C51,53 48,52 45,53 C42,54 39,57 40,60 Z",
      x: 45, 
      y: 60
    },
    "Amygdala": {
      path: "M38,55 C39,58 42,59 45,58 C48,57 49,54 48,51 C47,48 44,47 41,48 C38,49 37,52 38,55 Z",
      x: 42, 
      y: 53
    },
    "Dorsolateral Prefrontal Cortex": {
      path: "M30,30 C28,35 27,40 28,45 C29,50 32,53 35,55 C38,53 40,50 41,45 <PERSON>42,40 41,35 39,30 C36,29 33,29 30,30 Z",
      x: 35, 
      y: 40
    },
    "Anterior Cingulate Cortex": {
      path: "M45,40 C47,43 50,44 53,43 C56,42 58,39 57,36 C56,33 53,32 50,33 C47,34 44,37 45,40 Z",
      x: 50, 
      y: 38
    },
    "Ventromedial Prefrontal Cortex": {
      path: "M60,30 C58,35 57,40 58,45 C59,50 62,53 65,55 C68,53 70,50 71,45 C72,40 71,35 69,30 C66,29 63,29 60,30 Z",
      x: 65, 
      y: 40
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="w-8 h-8 animate-spin text-[#38b6ff]" />
      </div>
    );
  }

  return (
    <div className="relative w-full h-full min-h-[300px] rounded-lg overflow-hidden bg-[#0a0e24] border border-[rgba(56,182,255,0.2)]">
      {/* Brain visualization */}
      <div className="absolute inset-0 flex items-center justify-center">
        <svg viewBox="0 0 100 100" className="w-[90%] h-[90%]">
          {/* Brain outline - more anatomically correct */}
          <path
            d="M25,30 C20,40 20,60 25,70 C30,75 35,78 40,80 C45,82 55,82 60,80 C65,78 70,75 75,70 C80,60 80,40 75,30 C70,25 65,22 60,20 C55,18 45,18 40,20 C35,22 30,25 25,30 Z"
            fill="rgba(56,182,255,0.08)"
            stroke="rgba(56,182,255,0.4)"
            strokeWidth="0.8"
          />
          
          {/* Corpus callosum */}
          <path
            d="M40,50 C45,48 55,48 60,50"
            fill="none"
            stroke="rgba(56,182,255,0.2)"
            strokeWidth="0.5"
          />
          
          {/* Brain regions - only show if not selected or is the selected region */}
          {brainRegionData.map((region) => {
            const regionInfo = brainRegionMap[region.name] || {
              path: "M50,50 C52,52 54,52 56,50 C58,48 58,46 56,44 C54,42 52,42 50,44 C48,46 48,48 50,50 Z",
              x: 50,
              y: 50
            };
            
            const activity = region.activity || 0;
            const color = getColorForActivity(activity);
            const glowIntensity = Math.max(1, activity);
            
            return (
              <g key={region.name} onClick={() => setSelectedRegion(region)} style={{ cursor: "pointer" }}>
                {/* Glow effect */}
                <path
                  d={regionInfo.path}
                  fill={`${color}30`}
                  stroke={color}
                  strokeWidth="0.5"
                  filter={`drop-shadow(0 0 ${glowIntensity}px ${color})`}
                />
                
                {/* Region shape */}
                <path
                  d={regionInfo.path}
                  fill={`${color}40`}
                  stroke={color}
                  strokeWidth="0.5"
                />
                
                {/* Add a label for each region */}
                {selectedRegion && selectedRegion.name === region.name && (
                  <text
                    x={regionInfo.x}
                    y={regionInfo.y + 15}
                    textAnchor="middle"
                    fill={color}
                    fontSize="2"
                    className="pointer-events-none"
                  >
                    {region.name.split(' ').slice(-1)[0]}
                  </text>
                )}
              </g>
            );
          })}
        </svg>
      </div>
      
      {/* Small dots for all regions when nothing is selected */}
      {!selectedRegion && (
        <div className="absolute inset-0 pointer-events-none">
          {brainRegionData.map((region) => {
            const regionInfo = brainRegionMap[region.name];
            if (!regionInfo) return null;
            
            const color = getColorForActivity(region.activity);
            
            return (
              <div
                key={region.name}
                className="absolute w-2 h-2 rounded-full cursor-pointer pointer-events-auto"
                style={{
                  left: `${regionInfo.x}%`,
                  top: `${regionInfo.y}%`,
                  transform: "translate(-50%, -50%)",
                  backgroundColor: color,
                  boxShadow: `0 0 8px ${color}`
                }}
                onClick={() => setSelectedRegion(region)}
              />
            );
          })}
        </div>
      )}
      
      {/* Selected region info */}
      {selectedRegion && (
        <div className="absolute bottom-4 left-4 right-4 bg-[rgba(10,14,36,0.9)] p-4 rounded-lg border border-[rgba(56,182,255,0.3)]">
          <div className="flex justify-between items-center">
            <h3 className="font-medium text-[#38b6ff]">{selectedRegion.name}</h3>
            <button 
              className="text-gray-400 hover:text-white text-xs"
              onClick={() => setSelectedRegion(null)}
            >
              Back to all regions
            </button>
          </div>
          <div className="flex items-center mt-2">
            <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-2">
              <div 
                className="h-2 rounded-full" 
                style={{
                  width: `${selectedRegion.activity * 10}%`,
                  backgroundColor: getColorForActivity(selectedRegion.activity)
                }}
              />
            </div>
            <span className="ml-2 text-white">{selectedRegion.activity.toFixed(1)}</span>
          </div>
          <p className="text-xs text-gray-300 mt-2">
            {getBrainRegionDescription(selectedRegion.name)}
          </p>
        </div>
      )}
      
      {/* Instructions */}
      {!selectedRegion && (
        <div className="absolute bottom-4 left-0 right-0 text-center text-xs text-gray-400">
          Click on a brain region to see details
        </div>
      )}
    </div>
  );
}

// Helper function to get descriptions for brain regions
function getBrainRegionDescription(regionName: string): string {
  const descriptions: Record<string, string> = {
    "Medial Prefrontal Cortex (mPFC)": "Central to narrative identity formation and self-referential thinking. This region activates when reflecting on personal values, traits, and future self-projection. Works with the Default Mode Network to build your internal story.",
    "Default Mode Network": "Active during autobiographical memory, self-reflection, and mind-wandering. This network helps you understand your place in your personal narrative and is essential for identity formation.",
    "Amygdala": "Processes emotional responses and is key to emotional regulation and parasocial safety. The amygdala helps you recognize emotional significance in experiences and can be down-regulated through predictable, empathic interactions.",
    "Ventromedial Prefrontal Cortex (vmPFC)": "Plays a crucial role in value integration, emotional regulation, and decision-making based on emotions. This region helps integrate affect into valuation and supports eudaimonic self-alignment.",
    "Anterior Insula": "Responsible for emotional awareness, interoception (sensing internal body states), and integration of bodily signals. This region helps you understand how you feel physically and emotionally.",
    "Hippocampus": "Critical for episodic memory formation and self-continuity. The hippocampus helps you integrate new experiences into your existing understanding of yourself and supports memory consolidation.",
    "Anterior Cingulate Cortex (ACC)": "Monitors conflicts between competing information and detects cognitive dissonance. This region activates when resolving internal conflicts about identity and values.",
    "Orbitofrontal Cortex": "Involved in decision-making, especially reward-based decisions. This region helps you evaluate choices based on your personal values and goals.",
    "Posterior Cingulate Cortex (PCC)": "A key hub within the Default Mode Network involved in self-reference and autobiographical memory. Works with the mPFC to create coherent self-representations.",
    "Angular Gyrus": "Important for integration of sensory information and semantic processing. Contributes to the coherence of self-representations within the Default Mode Network.",
    "Precuneus": "Involved in temporally ordered self-representations and episodic memory retrieval. Works with the hippocampus to support self-continuity across time."
  };
  
  return descriptions[regionName] || "This brain region is involved in cognitive and emotional processing related to self-understanding.";
}
