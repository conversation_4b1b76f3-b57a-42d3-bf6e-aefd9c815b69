"use client"

import { use<PERSON><PERSON>back, useEffect, useRef } from "react"
import Particles from "react-particles"
import { loadSlim } from "tsparticles-slim"
import type { Container, Engine } from "tsparticles-engine"
import { gsap } from "gsap"

export function BackgroundParticles() {
  const particlesInit = useCallback(async (engine: Engine) => {
    await loadSlim(engine)
  }, [])

  const particlesLoaded = useCallback(async (container: Container | undefined) => {
    console.log("Particles loaded", container)
  }, [])

  return (
    <Particles
      id="tsparticles"
      init={particlesInit}
      loaded={particlesLoaded}
      className="fixed inset-0"
      options={{
        fullScreen: {
          enable: true,
          zIndex: 1,
        },
        background: {
          color: {
            value: "transparent",
          },
        },
        fpsLimit: 120,
        particles: {
          number: {
            value: 40, // Reduced number of particles
            density: {
              enable: true,
              value_area: 900,
            },
          },
          color: {
            value: ["#38b6ff", "#a137ff", "#00e5ff"],
          },
          shape: {
            type: ["circle", "triangle"],
          },
          opacity: {
            value: 0.4, // Reduced opacity
            random: true,
            anim: {
              enable: true,
              speed: 0.8,
              opacity_min: 0.1,
              sync: false,
            },
          },
          size: {
            value: { min: 1, max: 2 }, // Slightly smaller particles
            random: true,
            anim: {
              enable: true,
              speed: 1.5,
              size_min: 0.1,
              sync: false,
            },
          },
          line_linked: {
            enable: true,
            distance: 150,
            color: "#38b6ff",
            opacity: 0.2, // Reduced line opacity
            width: 1,
          },
          move: {
            enable: true,
            speed: 1.5, // Slightly slower movement
            direction: "none",
            random: false,
            straight: false,
            out_mode: "out",
            bounce: false,
            attract: {
              enable: true,
              rotateX: 600,
              rotateY: 1200,
            },
          },
        },
        interactivity: {
          detect_on: "window",
          events: {
            onhover: {
              enable: true,
              mode: "grab",
            },
            onclick: {
              enable: true,
              mode: "push",
            },
            resize: true,
          },
          modes: {
            grab: {
              distance: 140,
              line_linked: {
                opacity: 0.3, // Reduced interaction opacity
              },
            },
            push: {
              particles_nb: 3, // Reduced number of particles on click
            },
          },
        },
        retina_detect: true,
      }}
    />
  )
}
