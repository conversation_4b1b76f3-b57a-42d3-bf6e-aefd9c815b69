"use client";

import { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { Card } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

interface BrainRegion {
  name: string;
  activity: number;
  color: string;
  meshName: string;
}

interface BrainVisualizationProps {
  brainRegionData: {
    name: string;
    activity: number;
  }[];
  isLoading?: boolean;
}

export function BrainVisualization({ brainRegionData, isLoading = false }: BrainVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedRegion, setSelectedRegion] = useState<BrainRegion | null>(null);
  
  // Map brain regions to mesh names in the 3D model
  const brainRegionMap: Record<string, string> = {
    "Medial Prefrontal Cortex": "mPFC",
    "Default Mode Network": "DMN",
    "Amygdala": "amygdala",
    "Ventromedial PFC": "vmPFC",
    "Anterior Insula": "insula",
    "Hippocampus": "hippocampus",
    "Anterior Cingulate Cortex": "ACC",
    "Orbitofrontal Cortex": "OFC"
  };
  
  // Color scale for activity levels (0-10)
  const getColorForActivity = (activity: number): string => {
    // Blue (low) to red (high)
    const hue = Math.max(0, Math.min(240 - activity * 24, 240));
    return `hsl(${hue}, 100%, 50%)`;
  };

  useEffect(() => {
    if (!containerRef.current || isLoading || !brainRegionData.length) return;
    
    // Setup scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a1128);
    
    // Setup camera
    const camera = new THREE.PerspectiveCamera(
      75,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 5;
    
    // Setup renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    containerRef.current.appendChild(renderer.domElement);
    
    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    // Add directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);
    
    // Add controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    
    // Load brain model
    const loader = new GLTFLoader();
    loader.load(
      '/models/brain.glb', // You'll need to add this 3D model to your public folder
      (gltf) => {
        const brainModel = gltf.scene;
        scene.add(brainModel);
        
        // Process brain regions
        const brainRegions: BrainRegion[] = brainRegionData.map(region => ({
          name: region.name,
          activity: region.activity,
          color: getColorForActivity(region.activity),
          meshName: brainRegionMap[region.name] || ""
        }));
        
        // Color brain regions based on activity
        brainRegions.forEach(region => {
          if (region.meshName) {
            const mesh = brainModel.getObjectByName(region.meshName);
            if (mesh && mesh instanceof THREE.Mesh) {
              const material = new THREE.MeshStandardMaterial({
                color: new THREE.Color(region.color),
                emissive: new THREE.Color(region.color),
                emissiveIntensity: region.activity / 20,
                transparent: true,
                opacity: 0.8
              });
              mesh.material = material;
              
              // Add click event
              mesh.userData.region = region;
            }
          }
        });
        
        // Add raycaster for interaction
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();
        
        window.addEventListener('click', (event) => {
          // Calculate mouse position in normalized device coordinates
          const rect = renderer.domElement.getBoundingClientRect();
          mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
          mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
          
          // Update the picking ray with the camera and mouse position
          raycaster.setFromCamera(mouse, camera);
          
          // Calculate objects intersecting the picking ray
          const intersects = raycaster.intersectObjects(brainModel.children, true);
          
          if (intersects.length > 0) {
            const object = intersects[0].object;
            if (object.userData.region) {
              setSelectedRegion(object.userData.region);
            }
          } else {
            setSelectedRegion(null);
          }
        });
      },
      undefined,
      (error) => {
        console.error('Error loading brain model:', error);
      }
    );
    
    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();
    
    // Handle resize
    const handleResize = () => {
      if (!containerRef.current) return;
      camera.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
    };
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [brainRegionData, isLoading]);

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                   hover:border-[rgba(56,182,255,0.2)] transition-all duration-300 h-[500px]">
      <h2 className="text-xl font-light mb-4 flex items-center gap-2 text-[#38b6ff]">
        🧠 Neural Activity Visualization
      </h2>
      
      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <Loader2 className="w-8 h-8 animate-spin text-[#38b6ff]" />
        </div>
      ) : brainRegionData.length === 0 ? (
        <div className="text-center py-8 text-gray-400 h-[400px] flex flex-col items-center justify-center">
          <div className="text-6xl mb-4">🧠</div>
          <p>No neural data available yet</p>
          <p className="text-sm mt-2">Your brain profile will appear after more interactions with Yubi</p>
        </div>
      ) : (
        <div className="relative h-[400px]">
          <div ref={containerRef} className="w-full h-full" />
          
          {selectedRegion && (
            <div className="absolute bottom-4 left-4 right-4 bg-[rgba(10,17,40,0.8)] p-4 rounded-lg border border-[rgba(56,182,255,0.3)]">
              <h3 className="font-medium text-[#38b6ff]">{selectedRegion.name}</h3>
              <div className="flex items-center mt-2">
                <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-2">
                  <div 
                    className="h-2 rounded-full" 
                    style={{
                      width: `${selectedRegion.activity * 10}%`,
                      backgroundColor: selectedRegion.color
                    }}
                  />
                </div>
                <span className="ml-2 text-white">{selectedRegion.activity.toFixed(1)}</span>
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
}