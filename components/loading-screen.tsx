"use client"

import { useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { gsap } from "gsap"
import { BackgroundParticles } from "./background-particles"

interface LoadingScreenProps {
  message?: string
}

export function LoadingScreen({ message = "Loading..." }: LoadingScreenProps) {
  const circleRef = useRef<HTMLDivElement>(null)
  const textRef = useRef<HTMLParagraphElement>(null)

  useEffect(() => {
    if (circleRef.current && textRef.current) {
      // Create a timeline for the loading animation
      const tl = gsap.timeline({ repeat: -1 })

      // Animate the circle
      tl.to(circleRef.current, {
        scale: 1.1,
        opacity: 1,
        duration: 1,
        ease: "sine.inOut",
      }).to(circleRef.current, {
        scale: 1,
        opacity: 0.5,
        duration: 1,
        ease: "sine.inOut",
      })

      // Animate the text with a subtle glow effect
      gsap.to(textRef.current, {
        textShadow: "0 0 10px rgba(56, 182, 255, 0.7)",
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
      })
    }
  }, [])

  return (
    <div className="min-h-screen bg-[rgb(10,17,40)] flex flex-col items-center justify-center text-white relative">
      {/* Background particles */}
      <BackgroundParticles />

      {/* Scanline effect */}
      <div className="scanline"></div>

      <div
        ref={circleRef}
        className="w-16 h-16 rounded-full bg-gradient-to-r from-[#38b6ff] to-[#a137ff] mb-8 opacity-0.5"
      />
      <motion.p
        ref={textRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="text-xl font-light text-glow"
      >
        {message}
      </motion.p>
    </div>
  )
}
