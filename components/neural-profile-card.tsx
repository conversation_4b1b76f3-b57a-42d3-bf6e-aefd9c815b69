"use client";

import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Brain, Network, Lightbulb, Loader2, RefreshCw } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Brain2DVisualization } from "@/components/brain-2d-visualization";
import { toast } from "sonner"; // Import toast

interface NeuralProfileData {
  brainRegions: { name: string; activity: number }[];
  cognitiveFunctions: { name: string; strength: number }[];
  neuralNetworks: { name: string; connection: number }[];
}

export function NeuralProfileCard() {
  const [profileData, setProfileData] = useState<NeuralProfileData>({
    brainRegions: [],
    cognitiveFunctions: [],
    neuralNetworks: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("brain-regions");
  const [isGenerating, setIsGenerating] = useState(false);

  // Extract fetchNeuralProfile from useEffect to make it available to generateNeuralProfile
  const fetchNeuralProfile = async () => {
    try {
      setIsLoading(true);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;
      
      // Get user's most recent neural profile
      const { data: profileData, error: profileError } = await supabase
        .from("neural_profiles")
        .select("id")
        .eq("user_id", session.user.id)
        .order("updated_at", { ascending: false })
        .limit(1);
        
      if (profileError) {
        console.error("Error fetching neural profile:", profileError);
        setIsLoading(false);
        return;
      }
      
      if (!profileData || profileData.length === 0) {
        console.log("No neural profile found for user");
        setIsLoading(false);
        return;
      }
      
      const profileId = profileData[0].id;
      
      // Fetch brain region activity
      const { data: brainRegionData } = await supabase
        .from("brain_region_activity")
        .select("region_name, activity_level")
        .eq("profile_id", profileId)
        .order("timestamp", { ascending: false });
        
      // Fetch cognitive functions
      const { data: cognitiveFunctionData } = await supabase
        .from("cognitive_functions")
        .select("function_name, strength_score")
        .eq("profile_id", profileId)
        .order("timestamp", { ascending: false });
        
      // Fetch neural networks
      const { data: neuralNetworkData } = await supabase
        .from("neural_connections")
        .select("network_name, connection_strength")
        .eq("profile_id", profileId)
        .order("timestamp", { ascending: false });
        
      // Transform data for the component
      setProfileData({
        brainRegions: brainRegionData?.map(item => ({
          name: item.region_name,
          activity: item.activity_level
        })) || [],
        cognitiveFunctions: cognitiveFunctionData?.map(item => ({
          name: item.function_name,
          strength: item.strength_score
        })) || [],
        neuralNetworks: neuralNetworkData?.map(item => ({
          name: item.network_name,
          connection: item.connection_strength
        })) || []
      });
    } catch (error) {
      console.error("Error fetching neural profile data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateNeuralProfile = async () => {
    try {
      console.log("Starting neural profile generation...");
      setIsGenerating(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.error("No active session");
        toast.error("No active session. Please log in again.");
        return;
      }
      console.log("User session found, user ID:", session.user.id);
      
      // Check if there are new conversations since last profile update
      console.log("Checking for new conversations...");
      const { data: profileData, error: profileError } = await supabase
        .from("neural_profiles")
        .select("updated_at")
        .eq("user_id", session.user.id)
        .order("updated_at", { ascending: false })
        .limit(1);
      
      if (profileError) {
        console.error("Error fetching profile for update check:", profileError);
        toast.error("Failed to check for profile updates");
        setIsGenerating(false);
        return;
      }
      
      const lastUpdateTime = profileData && profileData.length > 0 && profileData[0].updated_at 
        ? new Date(profileData[0].updated_at) 
        : null;
      
      // Get count of conversations since last update
      const { count } = await supabase
        .from("conversations")
        .select("id", { count: "exact", head: true })
        .eq("user_id", session.user.id)
        .gt("created_at", lastUpdateTime?.toISOString() || '1970-01-01');
      
      if (count === 0 && lastUpdateTime) {
        console.log("No new conversations since last update, skipping regeneration");
        toast.info("No new conversations found. Your neural profile is already up to date.");
        setIsGenerating(false);
        return;
      }
      
      console.log(`Found ${count} new conversations, proceeding with regeneration`);
      
      console.log("Calling trigger-neural-profile API...");
      // Add timeout handling for the API call
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout
      
      try {
        const response = await fetch('/api/trigger-neural-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: session.user.id
          }),
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        console.log("API response received, status:", response.status);
        
        if (!response.ok) {
          const error = await response.json();
          console.error("API returned error:", error);
          toast.error(error.error || "Failed to generate neural profile");
          throw new Error(error.error || 'Failed to generate neural profile');
        }
        
        const result = await response.json();
        console.log("Neural profile generated successfully:", result);
        toast.success("Neural profile generated successfully!");
        
        // Refresh the data
        console.log("Refreshing neural profile data...");
        await fetchNeuralProfile();
        console.log("Neural profile data refreshed");
        
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          console.error("API request timed out after 60 seconds");
          toast.error("Neural profile generation timed out. Please try again.");
          throw new Error("Neural profile generation timed out. Please try again.");
        }
        throw error;
      }
      
    } catch (error) {
      console.error("Error generating neural profile:", error);
      toast.error(
        typeof error === "object" && error !== null && "message" in error 
          ? String(error.message) 
          : "Error generating neural profile"
      );
    } finally {
      setIsGenerating(false);
      console.log("Neural profile generation process completed");
    }
  };

  useEffect(() => {
    fetchNeuralProfile();
  }, []);

  // Generate a color based on score value (0-10)
  const getScoreColor = (score: number) => {
    // Blue (low) to purple (medium) to red (high)
    if (score < 3.33) return "#38b6ff";
    if (score < 6.67) return "#a137ff";
    return "#ff37a6";
  };

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                   hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-light flex items-center gap-2 text-[#38b6ff]">
          <Brain className="w-5 h-5" />
          Neurocognitive Profile
        </h2>
        
        {/* Regenerate button - only show when profile data exists */}
        {!isLoading && profileData.brainRegions.length > 0 && (
          <button 
            className="px-3 py-1.5 bg-[rgba(56,182,255,0.2)] rounded-md border border-[rgba(56,182,255,0.3)] text-[#38b6ff] text-sm flex items-center gap-1"
            onClick={generateNeuralProfile}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
                <span>Regenerating...</span>
              </>
            ) : (
              <>
                <RefreshCw className="w-3.5 h-3.5" />
                <span>Regenerate</span>
              </>
            )}
          </button>
        )}
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <Loader2 className="w-8 h-8 animate-spin text-[#38b6ff]" />
        </div>
      ) : profileData.brainRegions.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <Brain className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No neural profile data yet</p>
          <p className="text-sm mt-2">Your neural profile will be generated after more interactions with Yubi</p>
          <button 
            className="mt-6 px-4 py-2 bg-[rgba(56,182,255,0.2)] rounded-md border border-[rgba(56,182,255,0.3)] text-[#38b6ff]"
            onClick={generateNeuralProfile}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 inline animate-spin" />
                Generating...
              </>
            ) : (
              "Generate Neural Profile"
            )}
          </button>
        </div>
      ) : (
        <Tabs defaultValue="brain-regions" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6 bg-[rgba(10,17,40,0.6)]">
            <TabsTrigger value="brain-regions" className="data-[state=active]:bg-[rgba(56,182,255,0.2)]">
              <Brain className="w-4 h-4 mr-2" />
              Brain Regions
            </TabsTrigger>
            <TabsTrigger value="cognitive-functions" className="data-[state=active]:bg-[rgba(161,55,255,0.2)]">
              <Lightbulb className="w-4 h-4 mr-2" />
              Cognitive Functions
            </TabsTrigger>
            <TabsTrigger value="neural-networks" className="data-[state=active]:bg-[rgba(255,55,166,0.2)]">
              <Network className="w-4 h-4 mr-2" />
              Neural Networks
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="brain-regions" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {profileData.brainRegions.map((region) => (
                  <div key={region.name} className="mb-4">
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-300">{region.name}</span>
                      <span style={{ color: getScoreColor(region.activity) }}>
                        {region.activity.toFixed(1)}
                      </span>
                    </div>
                    <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-2">
                      <div 
                        className="h-2 rounded-full" 
                        style={{
                          width: `${region.activity * 10}%`,
                          backgroundColor: getScoreColor(region.activity)
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
              <div className="h-[300px]">
                <Brain2DVisualization brainRegionData={profileData.brainRegions} />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="cognitive-functions" className="mt-0">
            <div className="space-y-6">
              {profileData.cognitiveFunctions.map((func) => (
                <div key={func.name} className="bg-[rgba(10,17,40,0.6)] p-4 rounded-lg border border-[rgba(161,55,255,0.2)]">
                  <div className="flex justify-between mb-2">
                    <h3 className="text-white font-medium">{func.name}</h3>
                    <span style={{ color: getScoreColor(func.strength) }}>
                      {func.strength.toFixed(1)}
                    </span>
                  </div>
                  <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-2 mb-3">
                    <div 
                      className="h-2 rounded-full" 
                      style={{
                        width: `${func.strength * 10}%`,
                        backgroundColor: getScoreColor(func.strength)
                      }}
                    />
                  </div>
                  <p className="text-sm text-gray-400">
                    {getCognitiveFunctionDescription(func.name)}
                  </p>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="neural-networks" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {profileData.neuralNetworks.map((network) => (
                <div key={network.name} className="bg-[rgba(10,17,40,0.6)] p-4 rounded-lg border border-[rgba(255,55,166,0.2)]">
                  <div className="flex justify-between mb-2">
                    <h3 className="text-white font-medium">{network.name}</h3>
                    <span style={{ color: getScoreColor(network.connection) }}>
                      {network.connection.toFixed(1)}
                    </span>
                  </div>
                  <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-2 mb-3">
                    <div 
                      className="h-2 rounded-full" 
                      style={{
                        width: `${network.connection * 10}%`,
                        backgroundColor: getScoreColor(network.connection)
                      }}
                    />
                  </div>
                  <p className="text-sm text-gray-400">
                    {getNeuralNetworkDescription(network.name)}
                  </p>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      )}
    </Card>
  );
}

// Helper functions for descriptions
function getCognitiveFunctionDescription(name: string): string {
  const descriptions: Record<string, string> = {
    "Narrative Identity Formation": "Your ability to construct a coherent life story that integrates past experiences, present circumstances, and future aspirations. This function relies on the mPFC and Default Mode Network.",
    "Episodic Memory & Self-Continuity": "Your capacity to recall specific personal experiences and maintain a sense of self across time. This function is supported by the hippocampus and memory graphing processes.",
    "Emotional Regulation & Parasocial Safety": "Your ability to manage emotional responses and form secure attachments, including with digital entities. This involves the amygdala, mPFC, and oxytocin-related neural circuits.",
    "Cognitive Dissonance & Value Integration": "Your capacity to detect conflicts between beliefs or behaviors and integrate them into a coherent value system. This function relies on the ACC and vmPFC.",
    "Longitudinal Self-Modeling": "Your ability to develop and refine an internal model of yourself over time, incorporating feedback and new experiences. This involves meta-cognition, interoception, and memory systems.",
    "Self-Reflection": "Your ability to examine your own thoughts, feelings, and behaviors. This metacognitive process helps you understand yourself better.",
    "Emotional Intelligence": "Your capacity to recognize, understand, and manage your own emotions, as well as recognize and influence the emotions of others.",
    "Perspective Taking": "Your ability to consider situations from different viewpoints, including understanding others' mental states and intentions.",
    "Adaptive Resilience": "Your capacity to recover from difficulties and adapt to changing circumstances while maintaining psychological well-being."
  };
  
  return descriptions[name] || "No description available";
}

function getNeuralNetworkDescription(name: string): string {
  const descriptions: Record<string, string> = {
    "Default Mode Network": "Active during self-reflection, mind-wandering, and thinking about your past and future. This network is central to narrative identity formation and autobiographical memory.",
    "Salience Network": "Helps you identify what's important and deserves your attention in any given moment. This network includes the anterior insula and ACC, and plays a role in emotional awareness.",
    "Executive Control Network": "Supports goal-directed behavior, decision-making, and cognitive control. This network helps you align your actions with your values and long-term goals.",
    "Limbic System": "Processes emotions and helps form memories, particularly those with emotional significance. This system includes the amygdala, hippocampus, and parts of the prefrontal cortex."
  };
  
  return descriptions[name] || "No description available";
}
