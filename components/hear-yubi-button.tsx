"use client"

import type React from "react"

import { useEffect, useRef, forwardRef } from "react"
import { Loader2 } from "lucide-react"
import { gsap } from "gsap"

interface HearYubiButtonProps {
  onClick: () => Promise<void>
  isLoading: boolean
  className?: string
}

export const HearYubiButton = forwardRef<HTMLButtonElement, HearYubiButtonProps>(
  ({ onClick, isLoading, className = "" }, ref) => {
    const innerButtonRef = useRef<HTMLButtonElement>(null)
    const glowRef = useRef<HTMLDivElement>(null)
    const textRef = useRef<HTMLSpanElement>(null)

    // Combine the forwarded ref with our local ref
    const buttonRef = ref || innerButtonRef

    useEffect(() => {
      const button = buttonRef as React.RefObject<HTMLButtonElement>

      if (button.current && glowRef.current && textRef.current) {
        // Create a pulsing glow effect
        gsap.to(glowRef.current, {
          opacity: 0.8,
          duration: 1.5,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut",
        })

        // Create a subtle text glow animation
        gsap.to(textRef.current, {
          textShadow: "0 0 20px rgba(255, 255, 255, 0.9), 0 0 40px rgba(56, 182, 255, 0.7)",
          duration: 2,
          repeat: -1,
          yoyo: true,
          ease: "sine.inOut",
        })

        // Create a subtle hover animation
        button.current.addEventListener("mouseenter", () => {
          gsap.to(button.current, {
            scale: 1.05,
            duration: 0.3,
            ease: "power2.out",
          })

          gsap.to(glowRef.current, {
            opacity: 1,
            duration: 0.3,
          })
        })

        button.current.addEventListener("mouseleave", () => {
          gsap.to(button.current, {
            scale: 1,
            duration: 0.3,
            ease: "power2.out",
          })

          gsap.to(glowRef.current, {
            opacity: 0.7,
            duration: 0.3,
          })
        })

        // Add click animation
        button.current.addEventListener("mousedown", () => {
          if (!isLoading) {
            gsap.to(button.current, {
              scale: 0.95,
              duration: 0.1,
            })
          }
        })

        button.current.addEventListener("mouseup", () => {
          if (!isLoading) {
            gsap.to(button.current, {
              scale: 1.05,
              duration: 0.1,
            })
          }
        })
      }
    }, [isLoading, buttonRef])

    return (
      <button
        ref={buttonRef as React.RefObject<HTMLButtonElement>}
        onClick={onClick}
        disabled={isLoading}
        className={`relative px-12 py-6 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] text-white rounded-full text-2xl font-medium 
                 disabled:opacity-70 disabled:cursor-not-allowed neon-border ${className}`}
        aria-label="Hear Yubi speak"
      >
        {/* Glow effect */}
        <div
          ref={glowRef}
          className="absolute inset-0 bg-gradient-to-r from-[rgba(56,182,255,0.4)] to-[rgba(161,55,255,0.4)] rounded-full blur-xl opacity-0"
        ></div>

        {isLoading ? (
          <div className="flex items-center">
            <Loader2 className="mr-2 h-6 w-6 animate-spin" />
            <span className="text-white">Connecting...</span>
          </div>
        ) : (
          <span ref={textRef} className="text-glow text-white">
            Hear Yubi
          </span>
        )}
      </button>
    )
  },
)

HearYubiButton.displayName = "HearYubiButton"
