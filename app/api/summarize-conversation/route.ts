import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";

// Increase the maxDuration to 300 seconds (5 minutes) to give more time
export const runtime = 'edge';
export const maxDuration = 300; // Increase to 300 seconds (5 minutes)

export async function POST(request: Request) {
  console.log("[summarize-conversation] API endpoint called");
  console.time("summarize-conversation-total");
  
  try {
    const { conversation, userId } = await request.json();
    
    console.log("[summarize-conversation] Received request with conversation length:", conversation.length);
    console.log("[summarize-conversation] User ID:", userId);
    
    if (!userId) {
      console.error("[summarize-conversation] Missing user ID");
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    // Initialize Gemini
    console.log("[summarize-conversation] Initializing Gemini AI");
    console.time("gemini-summary-generation");
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = "gemini-2.0-flash-thinking-exp-01-21";
    console.log("[summarize-conversation] Using model:", model);
    
    // Create summarization prompt with neuroscience backing
    const summaryPrompt = `Analyze this conversation with Yubi through the lens of affective neuroscience and narrative identity theory.

Provide exactly three sections:

1. Summary: A detailed summary (3-4 sentences) that captures the core narrative themes and identity elements discussed. IMPORTANT: Include ALL specific names of people, places, family members, and other entities mentioned by the user. Be extremely specific rather than generic.
2. Emotions: Key emotional markers (comma-separated) that would activate the user's limbic system during this conversation
3. Topics: Main topics discussed (comma-separated) that engage the default mode network and autobiographical memory systems. Include specific subjects, not generic categories.

EXAMPLES OF GOOD SUMMARIES (notice how they include ALL specific details):

Example 1:
User input: "My brother John just got a job at Microsoft as a product manager. He's moving to Seattle next month."
Good summary: "The user's brother John recently secured a product manager position at Microsoft and will be relocating to Seattle next month."
Good emotions: excitement, pride, anticipation, slight concern
Good topics: John's new job, Microsoft employment, Seattle relocation, family career changes

Example 2:
User input: "I went to Florida last weekend and stayed at the Marriott hotel in Miami for 3 days. The beach was beautiful."
Good summary: "The user traveled to Florida last weekend, specifically staying at the Marriott hotel in Miami for 3 days, and enjoyed the beautiful beach."
Good emotions: contentment, relaxation, appreciation, nostalgia
Good topics: Florida vacation, Marriott hotel in Miami, beach experience, weekend getaway

Example 3:
User input: "My friend Sarah and I are planning a trip to Paris in July. We want to visit the Eiffel Tower and the Louvre."
Good summary: "The user and their friend Sarah are organizing a trip to Paris in July with plans to visit specific landmarks including the Eiffel Tower and the Louvre."
Good emotions: excitement, anticipation, curiosity, enthusiasm
Good topics: Paris trip planning, Sarah friendship, Eiffel Tower visit, Louvre museum, July travel plans

Example 4:
User input: "I'm working on a project with my colleague Alex from the marketing department. We need to finish it by next Tuesday for the client presentation."
Good summary: "The user is collaborating with their colleague Alex from marketing on a project that must be completed by next Tuesday for a client presentation."
Good emotions: pressure, focus, slight anxiety, determination
Good topics: work project deadline, Alex collaboration, marketing department, client presentation, Tuesday deadline

Example 5:
User input: "My daughter Emma just turned 8 yesterday. We celebrated at Chuck E. Cheese with her friends Lily and Noah."
Good summary: "The user's daughter Emma celebrated her 8th birthday yesterday at Chuck E. Cheese with her friends Lily and Noah."
Good emotions: joy, parental pride, celebration, nostalgia
Good topics: Emma's 8th birthday, Chuck E. Cheese celebration, Lily and Noah friendship, parenting milestone

Example 6:
User input: "I'm thinking about applying to Stanford for my MBA next year. I need to take the GMAT first though."
Good summary: "The user is considering applying to Stanford for an MBA program next year but needs to complete the GMAT examination first."
Good emotions: ambition, determination, slight anxiety, anticipation
Good topics: Stanford MBA application, GMAT preparation, higher education plans, career advancement

Example 7:
User input: "My boss David gave me feedback on the Henderson account yesterday. He thinks we need to revise the proposal by Friday."
Good summary: "The user received feedback from their boss David regarding the Henderson account yesterday, with a recommendation to revise the proposal by Friday."
Good emotions: concern, pressure, determination, slight frustration
Good topics: David's feedback, Henderson account, proposal revision, Friday deadline, workplace communication

Example 8:
User input: "I've been reading this book called 'Atomic Habits' by James Clear. It's really changing how I think about forming new habits."
Good summary: "The user has been reading 'Atomic Habits' by James Clear, which is significantly influencing their perspective on habit formation."
Good emotions: curiosity, inspiration, motivation, reflection
Good topics: Atomic Habits book, James Clear's writing, habit formation, personal development reading

Example 9:
User input: "My cat Whiskers has been sick lately. I took him to Dr. Peterson at Sunshine Veterinary Clinic on Oak Street yesterday."
Good summary: "The user's cat named Whiskers has been ill recently, prompting a visit to Dr. Peterson at Sunshine Veterinary Clinic on Oak Street yesterday."
Good emotions: concern, worry, care, hope
Good topics: Whiskers' health issues, Dr. Peterson consultation, Sunshine Veterinary Clinic, pet care, Oak Street location

Example 10:
User input: "I'm planning to meet my college roommate Lisa for dinner at Olive Garden next Thursday at 7pm."
Good summary: "The user is arranging to meet their college roommate Lisa for dinner at Olive Garden next Thursday at 7pm."
Good emotions: anticipation, nostalgia, happiness, social connection
Good topics: Lisa reunion, college roommate relationship, Olive Garden dinner, Thursday evening plans, 7pm meeting

Example 11:
User input: "My supervisor Rachel approved my vacation request for the last two weeks of August. I'm going to visit my grandparents in Phoenix where it's probably going to be 100 degrees."
Good summary: "The user's supervisor Rachel approved their vacation request for the last two weeks of August, during which they plan to visit their grandparents in Phoenix despite the expected high temperature of 100 degrees."
Good emotions: relief, gratitude, excitement, familial affection, slight concern about heat
Good topics: Rachel's vacation approval, August time-off, Phoenix trip planning, grandparents visit, hot weather concerns

Format your response with clear section headers like this:
Summary: [your detailed summary with ALL specific names, places, and entities]
Emotions: [emotion1, emotion2, emotion3]
Topics: [specific_topic1, specific_topic2, specific_topic3]

Your analysis should identify patterns of emotional processing, self-narrative construction, and meaning-making that occurred during this exchange, while preserving ALL specific details mentioned.

Conversation:
${JSON.stringify(conversation)}`;

    const contents = [
      {
        role: "user",
        parts: [{ text: summaryPrompt }],
      },
    ];

    // Get summary from Gemini with improved retries
    console.log("[summarize-conversation] Sending request to Gemini");
    let summaryResponse = "";
    let retries = 3; // Increase number of retries
    let retryDelay = 1000; // Start with 1 second delay

    while (retries >= 0) {
      try {
        const geminiPromise = ai.models.generateContentStream({
          model,
          contents,
        });
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Gemini API timeout")), 60000); // Increase timeout to 60 seconds
        });
        
        const response = await Promise.race([geminiPromise, timeoutPromise]) as ReturnType<typeof ai.models.generateContentStream>;
        
        for await (const chunk of await response) {
          summaryResponse += chunk.text;
        }
        
        // If we get here, the request succeeded
        break;
      } catch (error) {
        console.error(`[summarize-conversation] Error or timeout in Gemini request (${retries} retries left):`, error);
        retries--;
        
        // If we've exhausted retries, use fallback
        if (retries < 0) {
          summaryResponse = `Summary: This conversation covered personal reflections and experiences.
Emotions: thoughtful, reflective, curious
Topics: self-reflection, personal growth, experiences`;
          console.log("[summarize-conversation] Using fallback summary after exhausting retries");
        } else {
          console.log(`[summarize-conversation] Retrying Gemini request (${retries} retries left)`);
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          retryDelay *= 2; // Double the delay for next retry
        }
      }
    }
    console.timeEnd("gemini-summary-generation");

    console.log("[summarize-conversation] Raw Gemini response length:", summaryResponse.length);
    console.log("[summarize-conversation] First 100 chars of response:", summaryResponse.substring(0, 100));

    // If we still don't have a good summary after retries, try a simpler prompt
    if (!summaryResponse || summaryResponse.length < 50) {
      console.log("[summarize-conversation] Main summarization failed, trying simpler prompt");
      try {
        const simplifiedPrompt = `Summarize this conversation in three parts:
1. Summary: 2-3 sentences capturing the main points
2. Emotions: comma-separated list of emotions
3. Topics: comma-separated list of topics

Conversation:
${JSON.stringify(conversation)}`;

        const simplifiedContents = [
          {
            role: "user",
            parts: [{ text: simplifiedPrompt }],
          },
        ];

        const simplifiedResponse = await ai.models.generateContent({
          model,
          contents: simplifiedContents,
        });
        
        if (simplifiedResponse.text) {
          summaryResponse = simplifiedResponse.text;
          console.log("[summarize-conversation] Successfully generated simplified summary");
        }
      } catch (fallbackError) {
        console.error("[summarize-conversation] Fallback summarization also failed:", fallbackError);
        // Keep the existing fallback response
      }
    }

    // Parse the response more robustly
    console.time("parse-summary-response");
    let summary = "", emotions = "", topics = "";
    
    // Extract summary
    const summaryMatch = summaryResponse.match(/Summary:\s*([\s\S]*?)(?=Emotions:|$)/);
    if (summaryMatch && summaryMatch[1]) {
      summary = summaryMatch[1].trim();
    }
    
    // Extract emotions
    const emotionsMatch = summaryResponse.match(/Emotions:\s*([\s\S]*?)(?=Topics:|$)/);
    if (emotionsMatch && emotionsMatch[1]) {
      emotions = emotionsMatch[1].trim();
    }
    
    // Extract topics
    const topicsMatch = summaryResponse.match(/Topics:\s*([\s\S]*?)(?=$)/);
    if (topicsMatch && topicsMatch[1]) {
      topics = topicsMatch[1].trim();
    }
    console.timeEnd("parse-summary-response");
    
    console.log("[summarize-conversation] Parsed data:", { 
      summaryLength: summary.length,
      emotionsLength: emotions.length,
      topicsLength: topics.length 
    });

    // If parsing failed, use a fallback approach
    if (!summary) {
      console.log("[summarize-conversation] Primary parsing failed, using fallback approach");
      const lines = summaryResponse.split('\n').filter(line => line.trim());
      if (lines.length >= 1) summary = lines[0].replace(/^Summary:\s*/, '').trim();
      if (lines.length >= 2) emotions = lines[1].replace(/^Emotions:\s*/, '').trim();
      if (lines.length >= 3) topics = lines[2].replace(/^Topics:\s*/, '').trim();
      console.log("[summarize-conversation] Fallback parsing results:", { 
        summaryLength: summary.length,
        emotionsLength: emotions.length,
        topicsLength: topics.length 
      });
    }

    // Create a direct Supabase client without using cookies
    console.log("[summarize-conversation] Creating Supabase client");
    console.time("supabase-operations");
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role key for server operations
    );

    const emotionalMarkers = emotions ? emotions.split(',').map(e => e.trim()) : [];
    const topicsArray = topics ? topics.split(',').map(t => t.trim()) : [];

    console.log("[summarize-conversation] Inserting conversation into database");
    console.time("supabase-insert-conversation");
    const { data, error } = await supabase
      .from("conversations")
      .insert({
        user_id: userId,
        summary: summary || "No summary available",
        full_conversation: conversation,
        emotional_markers: emotionalMarkers,
        topics: topicsArray,
        conversation_type: 'reflection', // Default type
      })
      .select()
      .single();
    console.timeEnd("supabase-insert-conversation");

    console.log("[summarize-conversation] Supabase insert result:", { 
      success: !error,
      dataReceived: !!data,
      conversationId: data?.id
    });

    if (error) {
      console.error("[summarize-conversation] Supabase insert error:", error);
      throw error;
    }

    // Extract and store individual emotions with intensity
    console.log("[summarize-conversation] Processing emotions with intensity");
    const emotionsArray = emotions ? emotions.split(',').map(e => e.trim()) : [];
    if (emotionsArray.length > 0) {
      console.log("[summarize-conversation] Found emotions to process:", emotionsArray);
      // Use AI to assign intensity to each emotion
      console.time("emotion-intensity-generation");
      const emotionIntensityPrompt = `
    For each emotion in this list, assign an intensity score from 1-10 (10 being most intense).
    Return ONLY a JSON array with objects containing emotion and intensity.
    Example: [{"emotion":"joy","intensity":7},{"emotion":"anxiety","intensity":4}]

    Emotions: ${emotions}

    Context from conversation:
    ${summary}
    `;

      const intensityContents = [
        {
          role: "user",
          parts: [{ text: emotionIntensityPrompt }],
        },
      ];

      console.log("[summarize-conversation] Requesting emotion intensity from Gemini");
      let intensityText = "";
      try {
        const intensityPromise = ai.models.generateContentStream({
          model,
          contents: intensityContents,
        });
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("Emotion intensity generation timeout")), 20000); // 20 second timeout
        });
        
        const intensityResponse = await Promise.race([intensityPromise, timeoutPromise]) as ReturnType<typeof ai.models.generateContentStream>;
        
        for await (const chunk of await intensityResponse) {
          intensityText += chunk.text;
        }
      } catch (error) {
        console.error("[summarize-conversation] Error or timeout in emotion intensity generation:", error);
        // Continue with partial data rather than failing completely
        console.log("[summarize-conversation] Continuing with partial or no emotion intensity data");
      }
      console.timeEnd("emotion-intensity-generation");

      console.log("[summarize-conversation] Raw emotion intensity response length:", intensityText.length);
      console.log("[summarize-conversation] First 100 chars:", intensityText.substring(0, 100));
      
      // Extract JSON array from response
      console.time("parse-emotion-intensity");
      const jsonMatch = intensityText.match(/\[.*\]/s);
      if (jsonMatch) {
        try {
          console.log("[summarize-conversation] Found JSON match in response");
          const emotionsWithIntensity = JSON.parse(jsonMatch[0]);
          console.log("[summarize-conversation] Parsed emotions with intensity:", emotionsWithIntensity);
          
          // Insert each emotion into the emotional_tracking table
          console.time("insert-emotional-tracking");
          for (const item of emotionsWithIntensity) {
            console.log("[summarize-conversation] Inserting emotion:", item.emotion, "with intensity:", item.intensity);
            
            // Convert milliseconds to seconds for in_conversation_timestamp to fit in integer range
            const inConversationTimestampSeconds = Math.floor(Date.now() / 1000);
            
            await supabase.from("emotional_tracking").insert({
              user_id: userId,
              conversation_id: data.id,
              emotion: item.emotion,
              intensity: item.intensity,
              in_conversation_timestamp: inConversationTimestampSeconds, // Store as seconds
              timestamp: new Date().toISOString()
            });
          }
          console.timeEnd("insert-emotional-tracking");
          
          console.log("[summarize-conversation] Emotional tracking data saved");
        } catch (error) {
          console.error("[summarize-conversation] Error parsing emotion intensities:", error);
        }
      } else {
        console.log("[summarize-conversation] No JSON match found in emotion intensity response");
      }
      console.timeEnd("parse-emotion-intensity");
    } else {
      console.log("[summarize-conversation] No emotions to process");
    }

    // Generate neural profile based on conversation and emotional data
    console.log("[summarize-conversation] API call completed successfully");

    // Trigger neural profile generation asynchronously without waiting for the result
    console.log("[summarize-conversation] Triggering async neural profile generation");
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${request.headers.get('host')}`;
    fetch(`${baseUrl}/api/trigger-neural-profile`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ userId })
    }).catch(error => {
      console.error("[summarize-conversation] Error triggering async neural profile:", error);
    });
    console.log("[summarize-conversation] Async neural profile generation triggered");

    console.timeEnd("summarize-conversation-total");
    return NextResponse.json({
      success: true,
      conversation: data
    });

  } catch (error) {
    console.error("[summarize-conversation] API error:", error);
    console.timeEnd("summarize-conversation-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to summarize conversation" 
    }, { status: 500 });
  }
}
