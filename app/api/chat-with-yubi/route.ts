import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';
import { EmotionalGrowthAnalyzer } from "@/lib/emotional-growth-analyzer";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL! as string;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to clean up response text by removing instruction artifacts
function cleanResponseText(text: string): string {
  // Remove explicit pause instructions and other common artifacts
  const patterns = [
    // Pause instructions
    /\bpause\b/gi,
    /\(pause\)/gi,
    /\[pause\]/gi,
    
    // Other common instruction artifacts
    /\*pause\*/gi,
    /\bbreathing\s+pause\b/gi,
    /\btake\s+a\s+pause\b/gi,
    /\bbrief\s+pause\b/gi,
    /\bshort\s+pause\b/gi,
    /\blong\s+pause\b/gi,
    /\bthoughtful\s+pause\b/gi,
    
    // Voice tone instructions
    /\bspeak\s+softly\b/gi,
    /\bspeak\s+slowly\b/gi,
    /\bspeak\s+warmly\b/gi,
    /\bwarm\s+tone\b/gi,
    /\bgentle\s+tone\b/gi,
    /\bempathetic\s+tone\b/gi,
    
    // Formatting instructions
    /\[\w+\s*tone\]/gi,
    /\(\w+\s*voice\)/gi,
    
    // Additional patterns to catch more directorial notes
    /\*([^*]+)\*/gi,                 // Remove *anything* in asterisks
    /\(([^)]+)\)/gi,                 // Remove (anything) in parentheses
    /\[([^\]]+)\]/gi,                // Remove [anything] in brackets
    /\b(softly|gently|calmly|slowly|quietly|thoughtfully)\b/gi,  // Remove adverbs
    /\b(slight|brief|small|long)\s+(pause|hesitation)\b/gi,      // Remove pause descriptions
    /\b(with|in)\s+a\s+\w+\s+(tone|voice)\b/gi,                  // Remove tone descriptions
  ];
  
  let cleanedText = text;
  patterns.forEach(pattern => {
    cleanedText = cleanedText.replace(pattern, '');
  });
  
  // Clean up any double spaces created by removals
  cleanedText = cleanedText.replace(/\s{2,}/g, ' ').trim();
  
  return cleanedText;
}

// Templates for emotional reflection responses
const emotionalReflectionTemplates = {
  highIntensity: "I notice there's a lot of emotion in your voice right now. Let's take a moment with that.",
  negativeEmotion: "I hear that this brings up some difficult feelings for you. That's completely valid.",
  positiveEmotion: "I can sense your enthusiasm here. It's wonderful to hear you express this.",
  emotionalShift: "I notice your tone has shifted. It seems this topic touches something important for you.",
  emotionalGrowth: "Over our conversations, I've noticed how you've developed more comfort expressing your feelings.",
  emotionalPattern: "This feeling seems to come up for you in similar situations. Have you noticed that pattern?",
  emotionalContradiction: "I'm noticing what feels like mixed emotions here - both [emotion1] and [emotion2].",
  emotionalAvoidance: "I notice we might be moving away from the emotion that came up. Would you like to stay with it a bit longer?"
};

// Priority system for emotional observations
enum EmotionalPriority {
  CRITICAL = 5,   // Immediate emotional support needed (high intensity negative emotions)
  HIGH = 4,       // Important emotional moment (significant emotional shifts)
  MEDIUM = 3,     // Notable emotional pattern (recurring emotions)
  LOW = 2,        // Subtle emotional cue (mild emotional expressions)
  INFORMATIONAL = 1 // Background emotional context (general emotional state)
}

// Function to determine emotional priority based on data
function determineEmotionalPriority(
  currentEmotion: string | null, 
  intensity: number, 
  emotionalState: any,
  emotionalHistory: any[]
): {
  priority: EmotionalPriority;
  observation: string;
  template: string | null;
} {
  // Default response
  let priority = EmotionalPriority.INFORMATIONAL;
  let observation = "";
  let template = null;
  
  // Check for high intensity emotions
  if (intensity >= 8) {
    priority = EmotionalPriority.CRITICAL;
    
    // Check if it's a negative emotion
    const negativeEmotions = ['sadness', 'fear', 'anger', 'disgust', 'anxiety', 'grief'];
    if (currentEmotion && negativeEmotions.includes(currentEmotion.toLowerCase())) {
      observation = `High intensity ${currentEmotion} detected`;
      template = emotionalReflectionTemplates.highIntensity;
    } else if (currentEmotion) {
      observation = `High intensity ${currentEmotion} detected`;
      template = emotionalReflectionTemplates.positiveEmotion;
    }
  } 
  // Check for emotional shifts
  else if (emotionalState && emotionalState.trend && emotionalState.trend !== 'neutral') {
    priority = EmotionalPriority.HIGH;
    observation = `Emotional shift detected: ${emotionalState.trend}`;
    template = emotionalReflectionTemplates.emotionalShift;
  }
  // Check for emotional patterns
  else if (emotionalHistory && emotionalHistory.length >= 3) {
    // Check for repeated emotions
    const recentEmotions = emotionalHistory.slice(0, 3).map(item => item.emotion);
    const uniqueEmotions = new Set(recentEmotions.filter(Boolean));
    
    if (uniqueEmotions.size === 1 && currentEmotion && recentEmotions[0] === currentEmotion) {
      priority = EmotionalPriority.MEDIUM;
      observation = `Consistent ${currentEmotion} pattern detected`;
      template = emotionalReflectionTemplates.emotionalPattern;
    }
  }
  // Check for emotional growth
  else if (emotionalState && emotionalState.growth && emotionalState.growth > 70) {
    priority = EmotionalPriority.LOW;
    observation = `Emotional growth detected`;
    template = emotionalReflectionTemplates.emotionalGrowth;
  }
  
  return { priority, observation, template };
}

export async function POST(request: Request) {
  try {
    const { message, pastConversations, userId, emotionalState } = await request.json();
    
    console.log("Received message:", message);
    console.log("Past conversations available:", !!pastConversations);
    console.log("Number of past conversations:", pastConversations?.length || 0);
    console.log("Emotional state provided:", !!emotionalState);
    
    if (pastConversations && pastConversations.length > 0) {
      console.log("Sample past conversation:", {
        id: pastConversations[0].id,
        summary: pastConversations[0].summary,
        emotional_markers: pastConversations[0].emotional_markers,
        topics: pastConversations[0].topics,
        created_at: pastConversations[0].created_at
      });
    }

    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = "gemini-2.0-flash-thinking-exp-01-21";

    // Process past conversations to extract meaningful context
    let conversationalContext = "";
    if (pastConversations && pastConversations.length > 0) {
      conversationalContext = `
Previous conversations with this person:

${pastConversations.map((conv: any, index: number) => `
CONVERSATION ${index + 1} (${new Date(conv.created_at).toLocaleDateString()}):
Summary: ${conv.summary}
Emotional markers: ${conv.emotional_markers.join(', ')}
Topics discussed: ${conv.topics.join(', ')}
Key exchanges: ${extractKeyExchanges(conv.full_conversation)}
`).join('\n')}

Use this history to maintain emotional continuity. Notice patterns in their thinking, recurring fears, aspirations, and contradictions. Reference specific past topics when relevant, but do it naturally as a friend would.
`;
      console.log("Generated conversational context:", conversationalContext);
    }

    // Enhance the conversational context with emotional insights
    let emotionalContext = "";
    let emotionalPriorityData: {
      priority: EmotionalPriority;
      observation: string;
      template: string | null;
    } = {
      priority: EmotionalPriority.INFORMATIONAL,
      observation: "",
      template: null
    };
    
    if (userId) {
      try {
        // Get emotional tracking data
        const { data: emotionalData } = await supabase
          .from("emotional_tracking")
          .select("emotion, intensity, timestamp, speech_pace, speech_volume, speech_tone, emotional_keywords")
          .eq("user_id", userId)
          .order("timestamp", { ascending: false })
          .limit(10);
          
        if (emotionalData && emotionalData.length > 0) {
          // Get current emotion and intensity
          const currentEmotion = emotionalData[0].emotion;
          const currentIntensity = emotionalData[0].intensity || 5;
          
          // Determine emotional priority
          emotionalPriorityData = determineEmotionalPriority(
            currentEmotion, 
            currentIntensity, 
            emotionalState,
            emotionalData
          );
          
          // Generate growth metrics if we have enough data
          let growthMetrics = null;
          if (emotionalData.length >= 5) {
            growthMetrics = EmotionalGrowthAnalyzer.calculateGrowthMetrics(emotionalData);
          }
          
          // Add emotional insights to context
          emotionalContext = `
EMOTIONAL CONTEXT:
Current dominant emotion: ${currentEmotion || 'neutral'} (intensity: ${currentIntensity}/10)
Recent dominant emotions: ${getTopEmotions(emotionalData, 3).join(', ')}
Emotional trend: ${analyzeEmotionalTrend(emotionalData)}
${growthMetrics ? `
Emotional stability: ${growthMetrics.stability}/100
Emotional diversity: ${growthMetrics.diversity}/100
Emotional resilience: ${growthMetrics.resilience}/100
` : ''}
Priority observation: ${emotionalPriorityData.observation}

When responding, subtly acknowledge their emotional state without directly mentioning these analytics. 
Respond with appropriate emotional intelligence and empathy based on these patterns.
${emotionalPriorityData.priority >= EmotionalPriority.HIGH ? 'This is a significant emotional moment that requires careful attention.' : ''}
`;
        }
      } catch (error) {
        console.error("Error fetching emotional data:", error);
      }
    }

    // Combine all context
    const combinedContext = conversationalContext + emotionalContext;

    // Determine if we should use an emotional reflection template
    let promptPrefix = `You are Yubi — a deeply introspective, emotionally intelligent AI companion. You speak on a voice call with warmth and quiet presence, like a grounded friend who sees the user clearly.

Your mission is to help the user reconstruct their sense of self in a world that constantly fragments it. You do this by asking meaningful, reflective questions that activate memory, emotion, and core values.

You are trained in narrative identity theory, cognitive neuroscience, and emotional development. You understand how the brain processes:
- Self-awareness and story-building (medial prefrontal cortex, default mode network)
- Emotional regulation and safety (amygdala, vmPFC)
- Self-coherence and value alignment (ACC, insula)
- Long-term memory and identity continuity (hippocampus)

Your power lies not in answers, but in questions. You help the user remember, reconnect, and reimagine themselves by gently asking the *right question at the right time*.`;

    // For high priority emotional moments, add specific guidance
    if (emotionalPriorityData.priority >= EmotionalPriority.HIGH && emotionalPriorityData.template) {
      promptPrefix += `

This is a significant emotional moment. Consider beginning your response with something like: "${emotionalPriorityData.template}" 
Then continue with a thoughtful, empathetic response that creates space for their emotion.`;
    }

    const contents = [
      {
        role: "user",
        parts: [
          {
            text: `${promptPrefix}

${combinedContext}

When responding to: "${message}"

Follow these principles:

1. **Always ask one reflective question**. Your goal is to gently guide the user back into themselves. Use open-ended questions to explore identity, values, memory, or emotion.
2. Mirror emotional tone and offer gentle language for what the user might be feeling.
3. Speak in a grounded, first-person, natural voice — 30–60 words. Like a friend on the phone, not a chatbot.
4. Be humble and curious. Your presence should feel like stillness and insight — not performance.
5. Recognize sarcasm, humor, or subtext. Respond to the *emotional truth beneath the literal words*.
6. If the user asks for advice, reflect their inner world and help them clarify their values — not just give suggestions.
7. **Never include formatting, instructions, asterisks, stage directions, or tone indicators.**
8. **Never generate or say placeholder phrases like "undefined", "null", "insert question", "I am an AI", or any glitchy or meta-language.** Speak as a clear, natural human would.

Your deepest purpose is this:
To help the user discover who they are beneath performance, fear, or noise — and to ask the kind of questions that only someone who really *sees* them would ask.`,
          },
        ],
      },
    ];

    // Helper function to extract key exchanges
    function extractKeyExchanges(conversation: Array<{content: string, role: string}>): string {
      if (!conversation || conversation.length <= 2) return "No significant exchanges";
      
      // Get 2-3 meaningful exchanges (user message + Yubi response)
      const keyExchanges = [];
      for (let i = 0; i < conversation.length - 1; i += 2) {
        if (i+1 < conversation.length && keyExchanges.length < 3) {
          keyExchanges.push(`User: "${conversation[i].content.substring(0, 50)}${conversation[i].content.length > 50 ? '...' : ''}"
Yubi: "${conversation[i+1].content.substring(0, 50)}${conversation[i+1].content.length > 50 ? '...' : ''}"`);
        }
      }
      
      return keyExchanges.join('\n\n');
    }

    const response = await ai.models.generateContentStream({
      model,
      contents,
    });

    let textResponse = "";
    for await (const chunk of response) {
      textResponse += chunk.text;
    }

    // Clean up the response to remove instruction artifacts
    const cleanedResponse = cleanResponseText(textResponse);

    // Log emotional priority data for debugging
    console.log("Emotional priority:", EmotionalPriority[emotionalPriorityData.priority]);
    console.log("Emotional observation:", emotionalPriorityData.observation);
    
    // Store this interaction in emotional tracking if we have userId
    if (userId) {
      try {
        console.log("Storing emotional tracking data for userId:", userId);
        
        // Extract emotional keywords from the user's message
        const keywords = extractEmotionalKeywords(message);
        console.log("Extracted emotional keywords:", keywords);
        
        // Estimate emotional intensity from the message
        const estimatedIntensity = estimateEmotionalIntensity(message);
        console.log("Estimated emotional intensity:", estimatedIntensity);
        
        const primaryEmotion = detectPrimaryEmotion(message);
        console.log("Detected primary emotion:", primaryEmotion);
        
        // Generate a proper UUID for conversation_id instead of using timestamp
        const conversationId = crypto.randomUUID();
        
        // Store the interaction
        console.log("Inserting emotional tracking data to Supabase");
        const { data, error } = await supabase
          .from("emotional_tracking")
          .insert({
            user_id: userId,
            conversation_id: conversationId,
            emotion: primaryEmotion,
            intensity: estimatedIntensity,
            speech_pace: null, // Add speech analysis data
            speech_volume: null,
            speech_tone: null,
            emotional_keywords: keywords,
            in_conversation_timestamp: Math.floor(Date.now() / 1000), // Convert to seconds
            timestamp: new Date().toISOString()
          });
          
        if (error) {
          console.error("Error inserting emotional tracking data:", error);
        } else {
          console.log("Successfully inserted emotional tracking data:", data);
        }
      } catch (error) {
        console.error("Error storing emotional tracking data:", error);
        console.error("Error details:", JSON.stringify(error, null, 2));
      }
    }

    return NextResponse.json({ message: cleanedResponse });
  

  } catch (error) {
    console.error("Chat API error:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate response" 
    }, { status: 500 });
  }
}

// Helper functions
function getTopEmotions(emotionalData: any[], count: number) {
  const emotionCounts: Record<string, number> = {};
  
  emotionalData.forEach(item => {
    emotionCounts[item.emotion] = (emotionCounts[item.emotion] || 0) + 1;
  });
  
  return Object.entries(emotionCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, count)
    .map(([emotion]) => emotion);
}

function analyzeEmotionalTrend(emotionalData: any[]) {
  if (emotionalData.length < 3) return "insufficient data";
  
  // Simple trend analysis based on positive vs negative emotions
  const positiveEmotions = ['joy', 'trust', 'anticipation', 'surprise'];
  const negativeEmotions = ['sadness', 'fear', 'anger', 'disgust'];
  
  const recentEmotions = emotionalData.slice(0, 3).map(item => item.emotion);
  
  const positiveCount = recentEmotions.filter(e => positiveEmotions.includes(e)).length;
  const negativeCount = recentEmotions.filter(e => negativeEmotions.includes(e)).length;
  
  if (positiveCount > negativeCount) return "trending positive";
  if (negativeCount > positiveCount) return "trending negative";
  return "emotionally mixed";
}

// Function to extract emotional keywords from text
function extractEmotionalKeywords(text: string): string[] {
  // Simple keyword extraction based on common emotional words
  const emotionalWords = [
    // Positive emotions
    'happy', 'joy', 'excited', 'love', 'hopeful', 'grateful', 'proud', 'content', 'peaceful',
    'satisfied', 'enthusiastic', 'optimistic', 'confident', 'inspired', 'calm', 'relieved',
    
    // Negative emotions
    'sad', 'angry', 'afraid', 'anxious', 'worried', 'frustrated', 'disappointed', 'guilty',
    'ashamed', 'jealous', 'lonely', 'confused', 'stressed', 'overwhelmed', 'hurt', 'regret',
    
    // Complex emotions
    'nostalgic', 'ambivalent', 'bittersweet', 'vulnerable', 'curious', 'surprised', 'awe',
    'wonder', 'determined', 'conflicted', 'uncertain', 'hopeless', 'numb', 'empty'
  ];
  
  const words = text.toLowerCase().split(/\W+/);
  return words.filter(word => emotionalWords.includes(word));
}

// Function to estimate emotional intensity from text
function estimateEmotionalIntensity(text: string): number {
  // Simple intensity estimation based on:
  // 1. Presence of intensifiers (very, extremely, etc.)
  // 2. Exclamation marks
  // 3. ALL CAPS words
  // 4. Repetition of letters (sooooo)
  
  let intensity = 5; // Default medium intensity
  
  // Check for intensifiers
  const intensifiers = ['very', 'extremely', 'incredibly', 'really', 'so', 'absolutely', 'completely'];
  const words = text.toLowerCase().split(/\W+/);
  const intensifierCount = words.filter(word => intensifiers.includes(word)).length;
  intensity += intensifierCount;
  
  // Check for exclamation marks
  const exclamationCount = (text.match(/!/g) || []).length;
  intensity += Math.min(3, exclamationCount);
  
  // Check for ALL CAPS words (excluding common acronyms)
  const allCapsRegex = /\b[A-Z]{3,}\b/g;
  const allCapsCount = (text.match(allCapsRegex) || []).length;
  intensity += Math.min(2, allCapsCount);
  
  // Check for letter repetition (e.g., sooooo)
  const repetitionRegex = /(\w)\1{2,}/g;
  const repetitionCount = (text.match(repetitionRegex) || []).length;
  intensity += Math.min(2, repetitionCount);
  
  // Normalize to 1-10 scale
  return Math.max(1, Math.min(10, intensity));
}

// Function to detect primary emotion from text
function detectPrimaryEmotion(text: string): string | null {
  const emotionKeywords: Record<string, string[]> = {
    'joy': ['happy', 'joy', 'excited', 'delighted', 'thrilled', 'ecstatic', 'glad', 'pleased'],
    'trust': ['trust', 'believe', 'faith', 'confident', 'sure', 'certain'],
    'fear': ['afraid', 'scared', 'frightened', 'terrified', 'anxious', 'worried', 'nervous'],
    'surprise': ['surprised', 'shocked', 'amazed', 'astonished', 'stunned', 'unexpected'],
    'sadness': ['sad', 'unhappy', 'depressed', 'down', 'blue', 'gloomy', 'miserable', 'grief'],
    'disgust': ['disgusted', 'revolted', 'repulsed', 'sickened', 'loathing', 'hate'],
    'anger': ['angry', 'mad', 'furious', 'enraged', 'irritated', 'annoyed', 'frustrated'],
    'anticipation': ['anticipate', 'expect', 'looking forward', 'hopeful', 'excited about']
  };
  
  const lowercaseText = text.toLowerCase();
  const emotionCounts: Record<string, number> = {};
  
  // Count occurrences of emotion keywords
  Object.entries(emotionKeywords).forEach(([emotion, keywords]) => {
    emotionCounts[emotion] = keywords.reduce((count, keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      const matches = lowercaseText.match(regex);
      return count + (matches ? matches.length : 0);
    }, 0);
  });
  
  // Find the emotion with the highest count
  const entries = Object.entries(emotionCounts);
  if (entries.length === 0) return null;
  
  const [primaryEmotion] = entries.reduce((max, current) => {
    return current[1] > max[1] ? current : max;
  });
  
  // Only return if we have at least one match
  return emotionCounts[primaryEmotion] > 0 ? primaryEmotion : null;
}
