import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const runtime = 'edge';
export const maxDuration = 60; // Set max duration to 60 seconds

export async function POST(request: Request) {
  console.log("[trigger-neural-profile] API endpoint called");
  console.time("trigger-neural-profile-total");
  
  try {
    const { userId } = await request.json();
    
    if (!userId) {
      console.error("[trigger-neural-profile] Missing user ID");
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    console.log("[trigger-neural-profile] Processing for user ID:", userId);
    
    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Fetch all user conversations instead of just recent ones
    console.log("[trigger-neural-profile] Fetching all user conversations");
    console.time("fetch-conversations");
    const { data: allConversations, error: conversationsError } = await supabase
      .from("conversations")
      .select("id, summary, emotional_markers, topics, full_conversation")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });
    console.timeEnd("fetch-conversations");
      
    if (conversationsError) {
      console.error("[trigger-neural-profile] Error fetching conversations:", conversationsError);
      throw conversationsError;
    }
    
    console.log("[trigger-neural-profile] Fetched conversations count:", allConversations?.length || 0);
    
    if (!allConversations || allConversations.length === 0) {
      console.error("[trigger-neural-profile] No conversations found for user");
      return NextResponse.json({ error: "No conversations found" }, { status: 404 });
    }
    
    // Fetch recent emotional data
    console.log("[trigger-neural-profile] Fetching recent emotional data");
    console.time("fetch-emotional-data");
    const { data: emotionalData, error: emotionalError } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, timestamp")
      .eq("user_id", userId)
      .order("timestamp", { ascending: false })
      .limit(20);
    console.timeEnd("fetch-emotional-data");
      
    if (emotionalError) {
      console.error("[trigger-neural-profile] Error fetching emotional data:", emotionalError);
      throw emotionalError;
    }
    
    console.log("[trigger-neural-profile] Fetched emotional data count:", emotionalData?.length || 0);
    
    // Call the neural profile generation API with timeout handling
    console.log("[trigger-neural-profile] Calling neural profile generation API");
    console.time("neural-profile-api-call");
    
    try {
      // Use Promise.race to implement a timeout
      const responsePromise = fetch(new URL("/api/generate-neural-profile", request.url).toString(), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          emotionalData,
          conversationHistory: allConversations
        }),
      });
      
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Neural profile generation timed out")), 50000); // 50 second timeout
      });
      
      const response = await Promise.race([responsePromise, timeoutPromise]) as Response;
      
      console.log("[trigger-neural-profile] Neural profile API response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("[trigger-neural-profile] Neural profile generation failed:", errorText);
        throw new Error(`Neural profile generation failed: ${response.status}`);
      }
      
      const result = await response.json();
      console.log("[trigger-neural-profile] Neural profile generated successfully:", result);
      
      console.timeEnd("neural-profile-api-call");
      console.timeEnd("trigger-neural-profile-total");
      
      // After successful neural profile generation, fetch the latest profile data
      console.log("[trigger-neural-profile] Fetching latest neural profile data");
      console.time("fetch-neural-profile");

      // Get the profile ID - use limit(1) instead of .single()
      const { data: profileData, error: profileError } = await supabase
        .from("neural_profiles")
        .select("id")
        .eq("user_id", userId)
        .order("updated_at", { ascending: false })
        .limit(1); // Ensure we only get one row

      if (profileError) {
        console.error("[trigger-neural-profile] Error fetching profile:", profileError);
        throw profileError;
      }

      if (!profileData || profileData.length === 0) {
        console.error("[trigger-neural-profile] No profile found after generation");
        throw new Error("Profile not found after generation");
      }

      const profileId = profileData[0].id;

      // Fetch all profile components
      const [brainRegionsResult, cogFunctionsResult, neuralNetworksResult] = await Promise.all([
        supabase
          .from("brain_region_activity")
          .select("*")
          .eq("profile_id", profileId),
        supabase
          .from("cognitive_functions")
          .select("*")
          .eq("profile_id", profileId),
        supabase
          .from("neural_connections")
          .select("*")
          .eq("profile_id", profileId)
      ]);

      console.timeEnd("fetch-neural-profile");

      // Return the complete profile data
      return NextResponse.json({
        success: true,
        profileId: profileId,
        profileData: {
          brainRegions: brainRegionsResult.data || [],
          cognitiveFunction: cogFunctionsResult.data || [],
          neuralNetworks: neuralNetworksResult.data || []
        }
      });
    } catch (error) {
      console.timeEnd("neural-profile-api-call");
      if (error instanceof Error) {
        console.error("[trigger-neural-profile] Neural profile generation error:", error.message);
        
        if (error.message.includes("timed out")) {
          return NextResponse.json({ 
            error: "Neural profile generation timed out. Please try again." 
          }, { status: 504 });
        }
      }
      throw error;
    }
    
  } catch (error) {
    console.error("[trigger-neural-profile] API error:", error);
    console.timeEnd("trigger-neural-profile-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to trigger neural profile generation" 
    }, { status: 500 });
  }
}
