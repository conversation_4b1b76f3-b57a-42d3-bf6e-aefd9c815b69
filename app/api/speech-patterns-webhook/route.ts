import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { userId, destinationUrl } = await request.json();
    
    if (!userId || !destinationUrl) {
      return NextResponse.json({ 
        error: "Missing required parameters" 
      }, { status: 400 });
    }
    
    console.log("[speech-patterns-webhook] Processing request for user:", userId);
    
    // Fetch user's conversation history
    const { data: conversations, error } = await supabase
      .from("conversations")
      .select("id, full_conversation")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .limit(5);
    
    if (error) {
      console.error("[speech-patterns-webhook] Error fetching conversations:", error);
      return NextResponse.json({ 
        error: "Failed to fetch conversation history",
        speech_patterns: ["Unable to analyze speech patterns due to database error"]
      }, { status: 200 }); // Return 200 to avoid 502
    }
    
    if (!conversations || conversations.length === 0) {
      console.log("[speech-patterns-webhook] No conversations found for user");
      return NextResponse.json({ 
        speech_patterns: ["No conversation history available for analysis"]
      });
    }
    
    // Simple speech pattern analysis
    const speechPatterns = analyzeSpeechPatterns(conversations);
    
    console.log("[speech-patterns-webhook] Analysis complete, patterns:", speechPatterns);
    
    // Return the results directly instead of making another API call
    return NextResponse.json({
      speech_patterns: speechPatterns.length > 0 
        ? speechPatterns 
        : ["Not enough conversation data to analyze speech patterns"]
    });
  } catch (error) {
    console.error("[speech-patterns-webhook] Unhandled error:", error);
    // Return a 200 response with error message to avoid 502
    return NextResponse.json({ 
      error: "Speech pattern analysis failed",
      speech_patterns: ["Unable to analyze speech patterns due to server error"]
    }, { status: 200 });
  }
}

// Simple speech pattern analyzer
function analyzeSpeechPatterns(conversations: any[]): string[] {
  try {
    const patterns = [];
    let totalWords = 0;
    let totalMessages = 0;
    let longSentences = 0;
    let shortResponses = 0;
    let questions = 0;
    let exclamations = 0;
    
    // Process each conversation
    conversations.forEach(conv => {
      if (!conv.full_conversation) return;
      
      let messages;
      try {
        messages = typeof conv.full_conversation === 'string' 
          ? JSON.parse(conv.full_conversation) 
          : conv.full_conversation;
      } catch (e) {
        return; // Skip if can't parse
      }
      
      if (!Array.isArray(messages)) return;
      
      // Analyze only user messages
      messages.forEach(msg => {
        if (msg.role !== 'user' || !msg.content) return;
        
        const content = msg.content;
        const words = content.split(/\s+/).filter(Boolean);
        totalWords += words.length;
        totalMessages++;
        
        if (words.length > 20) longSentences++;
        if (words.length < 5) shortResponses++;
        if (content.includes('?')) questions++;
        if (content.includes('!')) exclamations++;
      });
    });
    
    // Generate insights
    if (totalMessages > 0) {
      const avgWordsPerMessage = totalWords / totalMessages;
      
      if (avgWordsPerMessage > 15) {
        patterns.push("Tends to use longer, more detailed responses");
      } else if (avgWordsPerMessage < 8) {
        patterns.push("Typically uses brief, concise responses");
      } else {
        patterns.push("Uses moderate-length responses");
      }
      
      if (longSentences / totalMessages > 0.3) {
        patterns.push("Often expresses complex thoughts with longer sentences");
      }
      
      if (shortResponses / totalMessages > 0.4) {
        patterns.push("Frequently uses short, direct responses");
      }
      
      if (questions / totalMessages > 0.3) {
        patterns.push("Asks questions frequently");
      }
      
      if (exclamations / totalMessages > 0.2) {
        patterns.push("Uses exclamations to express enthusiasm");
      }
    }
    
    return patterns.length > 0 ? patterns : ["No distinctive speech patterns detected"];
  } catch (error) {
    console.error("[speech-patterns-webhook] Error in speech pattern analysis:", error);
    return ["Basic speech pattern analysis unavailable"];
  }
}
