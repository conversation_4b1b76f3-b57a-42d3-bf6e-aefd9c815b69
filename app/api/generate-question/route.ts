import { GoogleGenAI } from "@google/genai"
import { NextResponse } from "next/server"
import { createClient } from '@supabase/supabase-js'

export async function POST(request: Request) {
  try {
    const { prompt, previousResponses, questionIndex } = await request.json()

    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 })
    }

    // Build a context-aware prompt based on previous responses
    let enhancedPrompt = prompt

    // Add context from previous responses if available
    if (previousResponses && Object.keys(previousResponses).length > 0) {
      enhancedPrompt += "\n\nHere's what you know about the user so far:\n"

      if (previousResponses.name) {
        enhancedPrompt += `- Their name is ${previousResponses.name}\n`
      }

      if (previousResponses.feeling) {
        enhancedPrompt += `- They're feeling: ${previousResponses.feeling}\n`
      }

      if (previousResponses.wish) {
        enhancedPrompt += `- They wish people asked them more about: ${previousResponses.wish}\n`
      }

      if (previousResponses.dream) {
        enhancedPrompt += `- A dream they have: ${previousResponses.dream}\n`
      }

      if (previousResponses.fear) {
        enhancedPrompt += `- Something that scares them but matters: ${previousResponses.fear}\n`
      }

      // For questions after the first one, personalize by using their name if available
      if (questionIndex > 0 && previousResponses.name) {
        enhancedPrompt += `\nPersonalize your question by using their name (${previousResponses.name}).`
      }
    }

    // Add context-aware prompt with scientific backing
    enhancedPrompt += "\n\nYour response must be a single sentence question. Frame this question based on principles of narrative identity theory and emotional neuroscience. Ask in a way that activates the medial prefrontal cortex and default mode network - brain regions associated with self-reflection and autobiographical memory. Do not provide multiple options, explanations, or follow-up text. Just ask the question directly in a warm, thoughtful way that encourages introspection."

    console.log("Enhanced prompt:", enhancedPrompt)

    // 1. Get text response from Gemini
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    })

    const config = {
      responseMimeType: "text/plain",
    }

    const model = "gemini-2.0-flash-thinking-exp-01-21"

    const contents = [
      {
        role: "user",
        parts: [
          {
            text: enhancedPrompt,
          },
        ],
      },
    ]

    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    })

    // Clean up the response to ensure it's a single sentence and remove special characters
    let textResponse = ""
    for await (const chunk of response) {
      textResponse += chunk.text
    }
    
    // Clean the text by:
    // 1. Remove Markdown-style formatting (*,_,`)
    // 2. Remove any extra whitespace
    // 3. Ensure proper sentence formatting
    textResponse = textResponse
      .replace(/[\*\_\`]/g, '') // Remove asterisks, underscores, and backticks
      .replace(/\s+/g, ' ')     // Replace multiple spaces with single space
      .trim()

    console.log("Cleaned response:", textResponse)

    // Convert to speech using ElevenLabs
    const voiceId = "EXAVITQu4vr4xnSDxMaL" // Default voice ID

    const elevenlabsResponse = await fetch(
      `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
      {
        method: "POST",
        headers: {
          Accept: "audio/mpeg",
          "Content-Type": "application/json",
          "xi-api-key": process.env.ELEVENLABS_API_KEY!,
        },
        body: JSON.stringify({
          text: textResponse,
          model_id: "eleven_monolingual_v1",
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
          },
        }),
      }
    )

    if (!elevenlabsResponse.ok) {
      return NextResponse.json({
        message: textResponse,
        audio: null,
        error: `Speech generation failed: ${elevenlabsResponse.status}`,
      })
    }

    const audioBuffer = await elevenlabsResponse.arrayBuffer()
    const audioBase64 = Buffer.from(audioBuffer).toString("base64")

    // Store emotional data if needed
    if (previousResponses && questionIndex !== undefined) {
      try {
        // Create Supabase client
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        )
        
        // Store the interaction
        const { error } = await supabase
          .from("emotional_tracking")
          .insert({
            user_id: previousResponses.userId, // Make sure userId is passed in previousResponses
            conversation_id: previousResponses.conversationId || crypto.randomUUID(),
            emotion: "reflection", // Or determine based on question content
            intensity: 5, // Default or determine based on content
            speech_pace: null,
            speech_volume: null, 
            speech_tone: null,
            emotional_keywords: [],
            in_conversation_timestamp: Math.floor(Date.now() / 1000), // Convert to seconds
            timestamp: new Date().toISOString()
          })
          
        if (error) {
          console.error("Error inserting emotional tracking data:", error)
        }
      } catch (error) {
        console.error("Error storing emotional tracking data:", error)
      }
    }

    // Return both text and audio
    return NextResponse.json({
      message: textResponse,
      audio: audioBase64,
    })

  } catch (error) {
    console.error("API error:", error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate response" 
    }, { status: 500 })
  }
}
