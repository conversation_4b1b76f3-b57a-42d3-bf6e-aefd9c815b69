import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Delete Gmail tokens for the user
    const { error } = await supabase
      .from('gmail_tokens')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Error disconnecting Gmail:', error);
      return NextResponse.json({ 
        error: 'Failed to disconnect Gmail' 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true,
      message: 'Gmail disconnected successfully' 
    });
  } catch (error) {
    console.error('Gmail disconnect error:', error);
    return NextResponse.json({ 
      error: 'Failed to disconnect Gmail' 
    }, { status: 500 });
  }
}
