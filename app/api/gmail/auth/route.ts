import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService } from '@/lib/gmail-service';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Create Gmail service instance
    const gmailService = new GmailService();
    
    // Generate OAuth URL
    const authUrl = gmailService.getAuthUrl();
    
    // Store the user ID in the state parameter for callback
    const authUrlWithState = `${authUrl}&state=${userId}`;

    return NextResponse.json({ 
      authUrl: authUrlWithState,
      message: 'Please visit the URL to authorize Gmail access'
    });
  } catch (error) {
    console.error('Gmail auth error:', error);
    return NextResponse.json(
      { error: 'Failed to generate Gmail authorization URL' },
      { status: 500 }
    );
  }
}
