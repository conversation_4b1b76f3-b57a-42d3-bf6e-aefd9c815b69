import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService } from '@/lib/gmail-service';

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get Gmail tokens from database
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: tokenData, error: tokenError } = await supabase
      .from('gmail_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ 
        error: 'Gmail not connected. Please authenticate first.',
        needsAuth: true 
      }, { status: 401 });
    }

    // Create Gmail service instance
    const gmailService = new GmailService();

    // Check if token needs refresh
    let tokens = {
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      token_type: tokenData.token_type,
      expiry_date: tokenData.expiry_date,
      scope: tokenData.scope
    };

    // If token is expired, refresh it
    if (tokenData.expiry_date && Date.now() >= tokenData.expiry_date) {
      try {
        gmailService.setCredentials(tokens);
        const refreshedTokens = await gmailService.refreshTokens();
        
        // Update tokens in database
        await supabase
          .from('gmail_tokens')
          .update({
            access_token: refreshedTokens.access_token,
            expiry_date: refreshedTokens.expiry_date,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        tokens = refreshedTokens;
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return NextResponse.json({ 
          error: 'Gmail authentication expired. Please re-authenticate.',
          needsAuth: true 
        }, { status: 401 });
      }
    }

    // Set credentials and fetch emails
    gmailService.setCredentials(tokens);
    const emails = await gmailService.getTodaysEmails();

    return NextResponse.json({ 
      emails,
      count: emails.length,
      message: `Found ${emails.length} emails from today`
    });
  } catch (error) {
    console.error('Fetch emails error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch emails from Gmail' },
      { status: 500 }
    );
  }
}
