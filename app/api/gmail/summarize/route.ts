import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService, EmailSummary } from '@/lib/gmail-service';
import { GoogleGenAI } from '@google/genai';
import { storeEmailSummaryMemory } from '@/lib/email-memory-storage';

interface EmailSummaryResponse {
  totalEmails: number;
  unreadCount: number;
  summary: string;
  priorityEmails: EmailSummary[];
  categories: {
    urgent: EmailSummary[];
    work: EmailSummary[];
    personal: EmailSummary[];
    newsletters: EmailSummary[];
    other: EmailSummary[];
  };
}

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get Gmail tokens from database
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: tokenData, error: tokenError } = await supabase
      .from('gmail_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ 
        error: 'Gmail not connected. Please authenticate first.',
        needsAuth: true 
      }, { status: 401 });
    }

    // Create Gmail service instance and fetch emails
    const gmailService = new GmailService();
    gmailService.setCredentials({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      token_type: tokenData.token_type,
      expiry_date: tokenData.expiry_date,
      scope: tokenData.scope
    });

    const emails = await gmailService.getTodaysEmails();

    if (emails.length === 0) {
      return NextResponse.json({
        totalEmails: 0,
        unreadCount: 0,
        summary: "You have no new emails today. Your inbox is clear!",
        priorityEmails: [],
        categories: {
          urgent: [],
          work: [],
          personal: [],
          newsletters: [],
          other: []
        }
      });
    }

    // Categorize emails using AI
    const categorizedEmails = await categorizeEmails(emails);
    
    // Generate intelligent summary using Gemini
    const summary = await generateEmailSummary(emails, categorizedEmails);

    const unreadCount = emails.filter(email => email.isUnread).length;

    // Identify priority emails (unread + important senders)
    const priorityEmails = emails
      .filter(email => email.isUnread || categorizedEmails.urgent.includes(email))
      .slice(0, 5); // Top 5 priority emails

    const response: EmailSummaryResponse = {
      totalEmails: emails.length,
      unreadCount,
      summary,
      priorityEmails,
      categories: categorizedEmails
    };

    // Store this email summary interaction in memory
    await storeEmailSummaryMemory(userId, response);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Email summarization error:', error);
    return NextResponse.json(
      { error: 'Failed to summarize emails' },
      { status: 500 }
    );
  }
}

async function categorizeEmails(emails: EmailSummary[]) {
  const categories = {
    urgent: [] as EmailSummary[],
    work: [] as EmailSummary[],
    personal: [] as EmailSummary[],
    newsletters: [] as EmailSummary[],
    other: [] as EmailSummary[]
  };

  emails.forEach(email => {
    const subject = email.subject.toLowerCase();
    const from = email.from.toLowerCase();
    const snippet = email.snippet.toLowerCase();

    // Urgent keywords
    if (subject.includes('urgent') || subject.includes('asap') || subject.includes('important') ||
        snippet.includes('urgent') || snippet.includes('deadline') || snippet.includes('emergency')) {
      categories.urgent.push(email);
    }
    // Work-related
    else if (from.includes('noreply') || from.includes('no-reply') || 
             subject.includes('meeting') || subject.includes('project') || 
             subject.includes('report') || subject.includes('deadline')) {
      categories.work.push(email);
    }
    // Newsletters/Marketing
    else if (from.includes('newsletter') || from.includes('marketing') || 
             subject.includes('unsubscribe') || snippet.includes('unsubscribe')) {
      categories.newsletters.push(email);
    }
    // Personal (common personal email domains)
    else if (from.includes('@gmail.com') || from.includes('@yahoo.com') || 
             from.includes('@hotmail.com') || from.includes('@outlook.com')) {
      categories.personal.push(email);
    }
    // Everything else
    else {
      categories.other.push(email);
    }
  });

  return categories;
}

async function generateEmailSummary(emails: EmailSummary[], categories: any): Promise<string> {
  try {
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = 'gemini-2.0-flash-thinking-exp-01-21';
    
    // Prepare email data for AI analysis
    const emailData = emails.slice(0, 20).map(email => ({
      from: email.from,
      subject: email.subject,
      snippet: email.snippet.substring(0, 100),
      isUnread: email.isUnread
    }));

    const prompt = `You are Yubi, an AI companion helping summarize today's emails. 

Email data:
${JSON.stringify(emailData, null, 2)}

Categories:
- Urgent: ${categories.urgent.length} emails
- Work: ${categories.work.length} emails  
- Personal: ${categories.personal.length} emails
- Newsletters: ${categories.newsletters.length} emails
- Other: ${categories.other.length} emails

Create a natural, conversational summary as if you're personally briefing the user about their emails. 
- Keep it under 3 sentences
- Mention the most important/urgent items first
- Use a warm, helpful tone
- Don't use technical jargon
- Focus on actionable insights

Example: "You have 12 new emails today. There are 3 urgent messages including one from your manager about tomorrow's deadline. The rest are mostly newsletters and a few personal messages from friends."`;

    const response = await ai.models.generateContent({
      model,
      contents: [{
        role: 'user',
        parts: [{ text: prompt }]
      }]
    });

    return response.response.text() || `You have ${emails.length} emails today with ${categories.urgent.length} urgent items that need your attention.`;
  } catch (error) {
    console.error('AI summary generation failed:', error);
    // Fallback summary
    const unreadCount = emails.filter(e => e.isUnread).length;
    return `You have ${emails.length} emails today, with ${unreadCount} unread messages. ${categories.urgent.length > 0 ? `There are ${categories.urgent.length} urgent emails that need your attention.` : 'Nothing urgent requiring immediate action.'}`;
  }
}
