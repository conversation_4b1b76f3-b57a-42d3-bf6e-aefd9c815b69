import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService } from '@/lib/gmail-service';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state'); // This contains the userId
    const error = searchParams.get('error');

    if (error) {
      console.error('Gmail OAuth error:', error);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?gmail_error=${error}`);
    }

    if (!code || !state) {
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?gmail_error=missing_code_or_state`);
    }

    const userId = state;

    // Create Gmail service instance
    const gmailService = new GmailService();
    
    // Exchange code for tokens
    const tokens = await gmailService.getTokens(code);

    // Store tokens in Supabase
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Create or update Gmail tokens for the user
    const { error: dbError } = await supabase
      .from('gmail_tokens')
      .upsert({
        user_id: userId,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        token_type: tokens.token_type,
        expiry_date: tokens.expiry_date,
        scope: tokens.scope,
        updated_at: new Date().toISOString()
      });

    if (dbError) {
      console.error('Error storing Gmail tokens:', dbError);
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?gmail_error=token_storage_failed`);
    }

    // Redirect back to dashboard with success
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?gmail_connected=true`);
  } catch (error) {
    console.error('Gmail callback error:', error);
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard?gmail_error=callback_failed`);
  }
}
