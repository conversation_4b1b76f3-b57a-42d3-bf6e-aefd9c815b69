import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService } from '@/lib/gmail-service';
import { GoogleGenAI } from '@google/genai';
import { extractTextFromGeminiResponse, safeParseJSON } from '@/lib/gemini-utils';

interface VoiceEmailRequest {
  userId: string;
  voiceCommand: string;
  conversationContext?: any;
}

interface EmailIntent {
  action: 'summarize' | 'send' | 'reply' | 'unknown';
  confidence: number;
  parameters: {
    recipientName?: string;
    recipientEmail?: string;
    message?: string;
    timeframe?: string;
  };
}

export async function POST(request: Request) {
  try {
    const { userId, voiceCommand, conversationContext }: VoiceEmailRequest = await request.json();

    if (!userId || !voiceCommand) {
      return NextResponse.json({ error: 'User ID and voice command are required' }, { status: 400 });
    }

    console.log('[gmail-voice-handler] Processing voice command:', voiceCommand);

    // Analyze the voice command to determine email intent
    const emailIntent = await analyzeEmailIntent(voiceCommand);
    
    console.log('[gmail-voice-handler] Detected intent:', emailIntent);

    if (emailIntent.action === 'unknown' || emailIntent.confidence < 0.7) {
      return NextResponse.json({
        success: false,
        message: "I didn't understand that email request. Try saying something like 'summarize my emails' or 'send an email to John'.",
        needsAuth: false
      });
    }

    // Check if user has Gmail connected
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: tokenData, error: tokenError } = await supabase
      .from('gmail_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({
        success: false,
        message: "I need access to your Gmail account first. Please connect your Gmail in the dashboard.",
        needsAuth: true,
        authUrl: `/api/gmail/auth?userId=${userId}`
      });
    }

    // Handle different email actions
    switch (emailIntent.action) {
      case 'summarize':
        return await handleEmailSummarization(userId, emailIntent.parameters);
      
      case 'send':
      case 'reply':
        return await handleEmailSending(userId, emailIntent, voiceCommand);
      
      default:
        return NextResponse.json({
          success: false,
          message: "I can help you summarize emails or send messages. What would you like to do?",
          needsAuth: false
        });
    }
  } catch (error) {
    console.error('[gmail-voice-handler] Error:', error);
    return NextResponse.json({
      success: false,
      message: "I encountered an error processing your email request. Please try again.",
      needsAuth: false
    }, { status: 500 });
  }
}

async function analyzeEmailIntent(voiceCommand: string): Promise<EmailIntent> {
  try {
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = 'gemini-2.0-flash-thinking-exp-01-21';
    
    const prompt = `Analyze this voice command for email-related intent:

Voice command: "${voiceCommand}"

Determine:
1. Action: summarize, send, reply, or unknown
2. Confidence: 0.0 to 1.0 (how sure you are)
3. Parameters: extract any mentioned names, email addresses, messages, timeframes

Examples:
- "summarize my emails" → action: summarize, confidence: 0.95
- "send an email to John saying I'll be late" → action: send, confidence: 0.9, recipientName: "John", message: "I'll be late"
- "reply to Sarah with thanks" → action: reply, confidence: 0.85, recipientName: "Sarah", message: "thanks"
- "what's the weather" → action: unknown, confidence: 0.1

Return ONLY a valid JSON object (no markdown, no code blocks, no formatting) with this exact structure:
{
  "action": "summarize|send|reply|unknown",
  "confidence": 0.0-1.0,
  "parameters": {
    "recipientName": "string or null",
    "recipientEmail": "string or null",
    "message": "string or null",
    "timeframe": "string or null"
  }
}

Do not wrap the response in markdown code blocks or any other formatting.`;

    const response = await ai.models.generateContent({
      model,
      contents: [{
        role: 'user',
        parts: [{ text: prompt }]
      }]
    });

    const result = extractTextFromGeminiResponse(response);

    if (!result) {
      throw new Error('No text response from AI');
    }

    return safeParseJSON(result, {
      action: 'unknown',
      confidence: 0.0,
      parameters: {}
    });
  } catch (error) {
    console.error('Intent analysis failed:', error);
    return {
      action: 'unknown',
      confidence: 0.0,
      parameters: {}
    };
  }
}

async function handleEmailSummarization(userId: string, parameters: any) {
  try {
    // Call the existing summarization endpoint
    const summaryResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gmail/summarize`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId })
    });

    const summaryData = await summaryResponse.json();

    if (!summaryResponse.ok) {
      return NextResponse.json({
        success: false,
        message: summaryData.error || "I couldn't access your emails right now.",
        needsAuth: summaryData.needsAuth || false
      });
    }

    // Format the response for voice
    const voiceResponse = formatSummaryForVoice(summaryData);

    return NextResponse.json({
      success: true,
      message: voiceResponse,
      data: summaryData,
      needsAuth: false
    });
  } catch (error) {
    console.error('Email summarization failed:', error);
    return NextResponse.json({
      success: false,
      message: "I had trouble accessing your emails. Please try again.",
      needsAuth: false
    });
  }
}

async function handleEmailSending(userId: string, emailIntent: EmailIntent, originalCommand: string) {
  try {
    const { recipientName, recipientEmail, message } = emailIntent.parameters;

    if (!recipientName && !recipientEmail) {
      return NextResponse.json({
        success: false,
        message: "I need to know who you want to send the email to. Please specify a name or email address.",
        needsAuth: false
      });
    }

    if (!message) {
      return NextResponse.json({
        success: false,
        message: "What message would you like to send?",
        needsAuth: false
      });
    }

    // Call the send email endpoint
    const sendResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gmail/send-response`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId,
        recipientName,
        recipientEmail,
        message,
        isReply: emailIntent.action === 'reply'
      })
    });

    const sendData = await sendResponse.json();

    if (!sendResponse.ok) {
      return NextResponse.json({
        success: false,
        message: sendData.error || "I couldn't send that email right now.",
        needsAuth: sendData.needsAuth || false
      });
    }

    return NextResponse.json({
      success: true,
      message: `Email sent successfully to ${sendData.recipient}!`,
      data: sendData,
      needsAuth: false
    });
  } catch (error) {
    console.error('Email sending failed:', error);
    return NextResponse.json({
      success: false,
      message: "I had trouble sending that email. Please try again.",
      needsAuth: false
    });
  }
}

function formatSummaryForVoice(summaryData: any): string {
  const { totalEmails, unreadCount, summary, priorityEmails } = summaryData;

  if (totalEmails === 0) {
    return "You have no new emails today. Your inbox is clear!";
  }

  let voiceResponse = summary;

  // Add priority email details if there are any
  if (priorityEmails && priorityEmails.length > 0) {
    const priorityDetails = priorityEmails.slice(0, 3).map((email: any) => 
      `${email.from.split('@')[0]} about ${email.subject}`
    ).join(', ');
    
    voiceResponse += ` Your priority emails are from ${priorityDetails}.`;
  }

  return voiceResponse;
}
