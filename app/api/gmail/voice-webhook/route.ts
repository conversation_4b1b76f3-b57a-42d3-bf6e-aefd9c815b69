import { NextResponse } from 'next/server';

// This webhook will be called by ElevenLabs during voice conversations
// when email-related requests are detected
export async function POST(request: Request) {
  try {
    console.log('[gmail-voice-webhook] Received request');
    
    // Parse the request from Eleven<PERSON>abs
    const body = await request.json();
    console.log('[gmail-voice-webhook] Request body:', JSON.stringify(body, null, 2));

    // Extract user context and conversation data
    const userId = body.conversation_initiation_client_data?.userContext?.user_id;
    const conversationId = body.conversation_id;
    const userMessage = body.user_message || body.transcript || '';

    console.log('[gmail-voice-webhook] Extracted data:', {
      userId,
      conversationId,
      userMessage: userMessage.substring(0, 100) + '...'
    });

    if (!userId) {
      console.error('[gmail-voice-webhook] No user ID found in request');
      return NextResponse.json({
        response: "I need to know who you are to access your emails. Please make sure you're logged in.",
        end_call: false
      });
    }

    if (!userMessage) {
      console.error('[gmail-voice-webhook] No user message found in request');
      return NextResponse.json({
        response: "I didn't catch what you said about emails. Could you repeat that?",
        end_call: false
      });
    }

    // Call our voice handler to process the email request
    const handlerResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/gmail/voice-handler`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userId,
        voiceCommand: userMessage,
        conversationContext: {
          conversationId,
          timestamp: new Date().toISOString()
        }
      })
    });

    const handlerData = await handlerResponse.json();
    console.log('[gmail-voice-webhook] Handler response:', handlerData);

    // Format response for ElevenLabs
    let response = handlerData.message || "I had trouble processing your email request.";
    
    // If authentication is needed, provide guidance
    if (handlerData.needsAuth) {
      response += " You can connect your Gmail account through the Yubi dashboard on your phone or computer.";
    }

    // Return response in ElevenLabs format
    return NextResponse.json({
      response: response,
      end_call: false,
      // Optional: Include additional context for the conversation
      context: {
        email_action_performed: handlerData.success,
        needs_gmail_auth: handlerData.needsAuth || false
      }
    });

  } catch (error) {
    console.error('[gmail-voice-webhook] Error processing request:', error);
    
    return NextResponse.json({
      response: "I encountered an error while trying to help with your emails. Please try again in a moment.",
      end_call: false
    });
  }
}

// Handle GET requests for webhook verification if needed
export async function GET(request: Request) {
  return NextResponse.json({
    status: 'Gmail voice webhook is active',
    timestamp: new Date().toISOString()
  });
}
