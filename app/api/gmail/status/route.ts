import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Check if user has Gmail tokens
    const { data: tokenData, error: tokenError } = await supabase
      .from('gmail_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({
        connected: false,
        error: null
      });
    }

    // Check if token is expired
    const isExpired = tokenData.expiry_date && Date.now() >= tokenData.expiry_date;

    return NextResponse.json({
      connected: true,
      email: tokenData.scope?.includes('gmail') ? 'Connected Gmail Account' : 'Gmail Account',
      lastSync: tokenData.updated_at,
      expired: isExpired
    });
  } catch (error) {
    console.error('Gmail status check error:', error);
    return NextResponse.json({
      connected: false,
      error: 'Failed to check Gmail status'
    });
  }
}
