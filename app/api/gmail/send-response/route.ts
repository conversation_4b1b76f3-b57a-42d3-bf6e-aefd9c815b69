import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { GmailService } from '@/lib/gmail-service';
import { GoogleGenAI } from '@google/genai';
import { storeEmailSendMemory } from '@/lib/email-memory-storage';
import { extractTextFromGeminiResponse } from '@/lib/gemini-utils';

interface SendEmailRequest {
  userId: string;
  recipientName?: string;
  recipientEmail?: string;
  message: string;
  threadId?: string;
  isReply?: boolean;
}

export async function POST(request: Request) {
  try {
    const { userId, recipientName, recipientEmail, message, threadId, isReply }: SendEmailRequest = await request.json();

    if (!userId || !message) {
      return NextResponse.json({ error: 'User ID and message are required' }, { status: 400 });
    }

    // Get Gmail tokens from database
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: tokenData, error: tokenError } = await supabase
      .from('gmail_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (tokenError || !tokenData) {
      return NextResponse.json({ 
        error: 'Gmail not connected. Please authenticate first.',
        needsAuth: true 
      }, { status: 401 });
    }

    // Create Gmail service instance
    const gmailService = new GmailService();
    gmailService.setCredentials({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
      token_type: tokenData.token_type,
      expiry_date: tokenData.expiry_date,
      scope: tokenData.scope
    });

    let finalRecipientEmail = recipientEmail;
    let subject = 'Message from Yubi';
    let finalMessage = message;

    // If this is a reply, get the original thread context
    if (isReply && threadId) {
      try {
        const thread = await gmailService.getEmailThread(threadId);
        const originalMessage = thread.messages[thread.messages.length - 1];
        
        // Extract recipient email from the original sender
        finalRecipientEmail = extractEmailFromString(originalMessage.from);
        
        // Create reply subject
        subject = originalMessage.subject.startsWith('Re:') 
          ? originalMessage.subject 
          : `Re: ${originalMessage.subject}`;

        // Enhance the message with AI if needed
        finalMessage = await enhanceEmailMessage(message, originalMessage.body, recipientName);
      } catch (threadError) {
        console.error('Error fetching thread context:', threadError);
        // Continue with basic message if thread fetch fails
      }
    } else if (recipientName && !recipientEmail) {
      // Try to find recipient email from recent emails
      const emails = await gmailService.getTodaysEmails();
      const matchingEmail = emails.find(email => 
        email.from.toLowerCase().includes(recipientName.toLowerCase())
      );
      
      if (matchingEmail) {
        finalRecipientEmail = extractEmailFromString(matchingEmail.from);
      } else {
        return NextResponse.json({ 
          error: `Could not find email address for ${recipientName}. Please provide the email address.` 
        }, { status: 400 });
      }
    }

    if (!finalRecipientEmail) {
      return NextResponse.json({ 
        error: 'Recipient email address is required' 
      }, { status: 400 });
    }

    // Enhance message with AI for better tone and clarity
    if (!isReply) {
      finalMessage = await enhanceEmailMessage(message, '', recipientName);
    }

    // Send the email
    await gmailService.sendEmail(finalRecipientEmail, subject, finalMessage, threadId);

    // Store the email interaction in memory using the new memory storage system
    await storeEmailSendMemory(userId, {
      recipient: finalRecipientEmail,
      subject,
      message: finalMessage,
      isReply: isReply || false
    });

    return NextResponse.json({
      success: true,
      message: `Email ${isReply ? 'reply' : ''} sent successfully to ${finalRecipientEmail}`,
      recipient: finalRecipientEmail,
      subject
    });
  } catch (error) {
    console.error('Send email error:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}

function extractEmailFromString(emailString: string): string {
  const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
  const match = emailString.match(emailRegex);
  return match ? match[1] : emailString;
}

async function enhanceEmailMessage(userMessage: string, originalContext: string = '', recipientName?: string): Promise<string> {
  try {
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = 'gemini-2.0-flash-thinking-exp-01-21';
    
    const prompt = `You are helping compose a professional but friendly email. 

User's message: "${userMessage}"
${recipientName ? `Recipient: ${recipientName}` : ''}
${originalContext ? `Original email context: "${originalContext.substring(0, 200)}..."` : ''}

Please enhance this message to be:
- Professional yet warm
- Clear and concise
- Properly formatted for email
- Include appropriate greeting and closing
- Maintain the user's intent and tone

Return only the enhanced email body, no additional formatting or explanations.`;

    const response = await ai.models.generateContent({
      model,
      contents: [{
        role: 'user',
        parts: [{ text: prompt }]
      }]
    });

    const enhancedMessage = extractTextFromGeminiResponse(response);
    return enhancedMessage || userMessage;
  } catch (error) {
    console.error('Message enhancement failed:', error);
    // Return original message if AI enhancement fails
    return userMessage;
  }
}


