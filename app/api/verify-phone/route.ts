import { NextResponse } from "next/server";
import twilio from "twilio";
import { createClient } from '@supabase/supabase-js';

// Initialize Twilio client
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID;
const client = twilio(accountSid, authToken);

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: Request) {
  try {
    const { phoneNumber, userId, action, code } = await request.json();
    
    if (!phoneNumber) {
      return NextResponse.json({ 
        success: false, 
        error: "Phone number is required" 
      }, { status: 400 });
    }
    
    // For sending verification code
    if (action === "send") {
      const verification = await client.verify.v2.services(verifyServiceSid!)
        .verifications.create({
          to: phoneNumber,
          channel: "sms"
        });
      
      return NextResponse.json({ 
        success: true, 
        status: verification.status 
      });
    }
    
    // For verifying code
    if (action === "verify" && code) {
      const verificationCheck = await client.verify.v2.services(verifyServiceSid!)
        .verificationChecks.create({
          to: phoneNumber,
          code: code
        });
      
      // If verification is successful and we have a userId, save to database
      if (verificationCheck.status === "approved" && userId) {
        // Format the phone number to E.164 format (e.g., +12345678900)
        // Remove any non-digit characters except the leading +
        let formattedPhoneNumber = phoneNumber;
        if (phoneNumber.startsWith('+')) {
          formattedPhoneNumber = '+' + phoneNumber.substring(1).replace(/\D/g, '');
        } else {
          formattedPhoneNumber = '+' + phoneNumber.replace(/\D/g, '');
        }
        
        // Save verified phone number to user profile
        const { error } = await supabase
          .from("profiles")
          .update({ 
            phone_number: formattedPhoneNumber,
            phone_verified: true
          })
          .eq("id", userId);
          
        if (error) {
          console.error("Error saving phone number:", error);
          return NextResponse.json({ 
            success: false, 
            error: "Failed to save phone number" 
          }, { status: 500 });
        }
      }
      
      return NextResponse.json({ 
        success: true, 
        status: verificationCheck.status 
      });
    }
    
    return NextResponse.json({ 
      success: false, 
      error: "Invalid action" 
    }, { status: 400 });
    
  } catch (error) {
    console.error("Twilio verification error:", error);
    return NextResponse.json({ 
      success: false, 
      error: "Failed to process verification" 
    }, { status: 500 });
  }
}
