import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { userId } = await request.json();
    
    console.log("[elevenlabs-heartbeat] Keeping connection alive for user:", userId);
    
    // Make a minimal request to ElevenLabs to keep the connection active
    const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;
    
    // Simple ping to the ElevenLabs API
    const response = await fetch(
      "https://api.elevenlabs.io/v1/user",
      {
        method: "GET",
        headers: {
          "xi-api-key": ELEVENLABS_API_KEY || "",
        },
      }
    );
    
    if (!response.ok) {
      throw new Error(`ElevenLabs API error: ${response.statusText}`);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Heartbeat API error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to send heartbeat" },
      { status: 500 }
    );
  }
}