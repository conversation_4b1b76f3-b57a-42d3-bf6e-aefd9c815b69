import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export const runtime = 'edge';
export const maxDuration = 55; // Increase to 55 seconds

// Define interfaces for the neural data structure
interface BrainRegion {
  name: string;
  activity: number;
}

interface CognitiveFunction {
  name: string;
  strength: number;
}

interface NeuralNetwork {
  name: string;
  connection: number;
}

interface NeuralData {
  brain_regions: BrainRegion[];
  cognitive_functions: CognitiveFunction[];
  neural_networks: NeuralNetwork[];
}

export async function POST(request: Request) {
  console.log("[generate-neural-profile] API endpoint called");
  console.time("neural-profile-total");
  
  try {
    const { userId, emotionalData, conversationHistory } = await request.json();
    
    console.log("[generate-neural-profile] Received request with:");
    console.log("[generate-neural-profile] User ID:", userId);
    console.log("[generate-neural-profile] Emotional data count:", emotionalData?.length || 0);
    console.log("[generate-neural-profile] Conversation history count:", conversationHistory?.length || 0);
    
    if (!userId) {
      console.error("[generate-neural-profile] Missing user ID");
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    // Initialize Gemini
    console.log("[generate-neural-profile] Initializing Gemini AI");
    console.time("gemini-neural-generation");
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = "gemini-2.0-flash-thinking-exp-01-21";
    console.log("[generate-neural-profile] Using model:", model);
    
    // Create neural profile generation prompt with scientific backing
    const neuralProfilePrompt = `
Based on this user's emotional patterns and conversation history, generate a comprehensive neurocognitive profile.
Analyze which brain regions would likely be most active during their interactions, focusing on these key neuroscience topics:

1. Default Mode Network (DMN) activity - self-referential processing and autobiographical memory
2. Salience Network - emotional significance detection and attentional control
3. Executive Control Network - cognitive regulation and goal-directed behavior
4. Limbic System - emotional processing and memory encoding
5. Prefrontal Cortex - executive function, decision-making, and personality expression

Generate a detailed JSON profile with these components:
1. Brain regions with activity levels (scale 1-10)
2. Cognitive functions with strength ratings (scale 1-10)
3. Neural network connectivity patterns (scale 1-10)

Format the response as valid JSON following this structure:
{
  "brain_regions": [
    {"name": "Anterior Cingulate Cortex", "activity": 7.2},
    {"name": "Ventromedial Prefrontal Cortex", "activity": 8.5},
    {"name": "Dorsolateral Prefrontal Cortex", "activity": 6.3},
    {"name": "Amygdala", "activity": 5.9},
    {"name": "Hippocampus", "activity": 7.8}
  ],
  "cognitive_functions": [
    {"name": "Narrative Identity Formation", "strength": 8.1},
    {"name": "Episodic Memory & Self-Continuity", "strength": 7.4},
    {"name": "Emotional Regulation & Parasocial Safety", "strength": 6.8},
    {"name": "Cognitive Dissonance & Value Integration", "strength": 7.2},
    {"name": "Longitudinal Self-Modeling", "strength": 6.5}
  ],
  "neural_networks": [
    {"name": "Default Mode Network", "connection": 7.8},
    {"name": "Salience Network", "connection": 6.5},
    {"name": "Executive Control Network", "connection": 7.1}
  ]
}

Emotional data: ${JSON.stringify(emotionalData || [])}
Conversation history: ${JSON.stringify(conversationHistory || [])}
`;

    // Use fallback immediately if input data is minimal
    if ((!emotionalData || emotionalData.length === 0) && 
        (!conversationHistory || conversationHistory.length === 0)) {
      console.log("[generate-neural-profile] Minimal input data, using fallback profile");
      return NextResponse.json({
        success: true,
        profileData: JSON.parse(generateFallbackNeuralProfile())
      });
    }

    const contents = [
      {
        role: "user",
        parts: [{ text: neuralProfilePrompt }],
      },
    ];

    // Get neural profile from Gemini with timeout handling and retry logic
    console.log("[generate-neural-profile] Sending request to Gemini");
    let profileResponse = "";
    let retryCount = 3; // Number of retries
    let retryDelay = 1000; // Start with 1 second delay, will increase exponentially

    while (retryCount >= 0) {
      try {
        const response = await Promise.race([
          ai.models.generateContentStream({
            model,
            contents,
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Gemini API timeout')), 30000)
          )
        ]) as ReturnType<typeof ai.models.generateContentStream>;
        
        for await (const chunk of await response) {
          profileResponse += chunk.text;
        }
        console.log("[generate-neural-profile] Gemini response received successfully");
        break; // Success, exit retry loop
      } catch (error) {
        console.error(`[generate-neural-profile] Error or timeout in Gemini request (${retryCount} retries left):`, error);
        
        if (retryCount <= 0) {
          // If we've exhausted retries, use a fallback response or throw
          console.log("[generate-neural-profile] All retries exhausted, using fallback response");
          profileResponse = generateFallbackNeuralProfile();
          break;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay *= 2; // Double the delay for next retry
        retryCount--;
      }
    }
    console.timeEnd("gemini-neural-generation");

    console.log("[generate-neural-profile] Raw Gemini response length:", profileResponse.length);
    console.log("[generate-neural-profile] First 100 chars of response:", profileResponse.substring(0, 100));

    // Process and store neural profile
    return await processAndStoreNeuralProfile(userId, profileResponse);
  } catch (error) {
    console.error("[generate-neural-profile] API error:", error);
    console.timeEnd("neural-profile-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate neural profile" 
    }, { status: 500 });
  }
}

// Helper function to process and store neural profile
async function processAndStoreNeuralProfile(userId: string, profileResponse: string) {
  try {
    console.time("parse-neural-profile");
    const jsonMatch = profileResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      console.error("[generate-neural-profile] Failed to parse neural profile data - no JSON match");
      throw new Error("Failed to parse neural profile data");
    }

    let neuralData: NeuralData;
    try {
      neuralData = JSON.parse(jsonMatch[0]);
      console.log("[generate-neural-profile] Successfully parsed neural data");
      console.log("[generate-neural-profile] Brain regions count:", neuralData.brain_regions?.length || 0);
      console.log("[generate-neural-profile] Cognitive functions count:", neuralData.cognitive_functions?.length || 0);
      console.log("[generate-neural-profile] Neural networks count:", neuralData.neural_networks?.length || 0);
    } catch (error) {
      console.error("[generate-neural-profile] JSON parse error:", error);
      console.error("[generate-neural-profile] JSON string that failed to parse:", jsonMatch[0]);
      throw new Error("Failed to parse neural profile JSON");
    }
    console.timeEnd("parse-neural-profile");

    // Create Supabase client
    console.log("[generate-neural-profile] Creating Supabase client");
    console.time("supabase-operations");
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Create or update neural profile
    console.log("[generate-neural-profile] Upserting neural profile");
    console.time("upsert-neural-profile");
    const { data: profileData, error: profileError } = await supabase
      .from("neural_profiles")
      .upsert({
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .select()
      .limit(1); // Get just one row instead of using .single()
    console.timeEnd("upsert-neural-profile");

    if (profileError) {
      console.error("[generate-neural-profile] Supabase profile upsert error:", profileError);
      throw profileError;
    }

    // Make sure we have a profile ID
    if (!profileData || profileData.length === 0) {
      console.error("[generate-neural-profile] No profile data returned after upsert");
      throw new Error("Failed to create or update neural profile");
    }

    const profileId = profileData[0].id; // Access the first item in the array
    console.log("[generate-neural-profile] Profile ID:", profileId);

    // Process and store all neural data components
    await storeNeuralComponents(supabase, profileId, neuralData);
    
    console.timeEnd("supabase-operations");
    console.timeEnd("neural-profile-total");
    
    return NextResponse.json({
      success: true,
      profileId: profileId
    });
  } catch (error) {
    console.error("[generate-neural-profile] Processing error:", error);
    console.timeEnd("neural-profile-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to process neural profile" 
    }, { status: 500 });
  }
}

// Helper function to store neural components
async function storeNeuralComponents(supabase: any, profileId: string, neuralData: NeuralData) {
  // Store brain regions
  console.time("upsert-brain-regions");
  try {
    for (const region of neuralData.brain_regions) {
      await supabase
        .from("brain_region_activity")
        .upsert({
          profile_id: profileId,
          region_name: region.name,
          activity_level: region.activity,
          timestamp: new Date().toISOString()
        }, {
          onConflict: 'profile_id,region_name'
        });
    }
  } catch (error) {
    console.error("[generate-neural-profile] Error in brain region upsert:", error);
  }
  console.timeEnd("upsert-brain-regions");

  // Store cognitive functions
  console.time("upsert-cognitive-functions");
  try {
    for (const func of neuralData.cognitive_functions) {
      await supabase
        .from("cognitive_functions")
        .upsert({
          profile_id: profileId,
          function_name: func.name,
          strength_score: func.strength,
          timestamp: new Date().toISOString()
        }, {
          onConflict: 'profile_id,function_name'
        });
    }
  } catch (error) {
    console.error("[generate-neural-profile] Error in cognitive function upsert:", error);
  }
  console.timeEnd("upsert-cognitive-functions");

  // Store neural networks
  console.time("upsert-neural-networks");
  try {
    for (const network of neuralData.neural_networks) {
      await supabase
        .from("neural_connections")
        .upsert({
          profile_id: profileId,
          network_name: network.name,
          connection_strength: network.connection,
          timestamp: new Date().toISOString()
        }, {
          onConflict: 'profile_id,network_name'
        });
    }
  } catch (error) {
    console.error("[generate-neural-profile] Error in neural network upsert:", error);
  }
  console.timeEnd("upsert-neural-networks");
}

// Add this function to generate a fallback neural profile when Gemini fails
function generateFallbackNeuralProfile(): string {
  return JSON.stringify({
    "brain_regions": [
      {"name": "Anterior Cingulate Cortex", "activity": 7.0},
      {"name": "Ventromedial Prefrontal Cortex", "activity": 6.5},
      {"name": "Dorsolateral Prefrontal Cortex", "activity": 6.0},
      {"name": "Amygdala", "activity": 5.5},
      {"name": "Hippocampus", "activity": 7.5}
    ],
    "cognitive_functions": [
      {"name": "Narrative Identity Formation", "strength": 7.0},
      {"name": "Episodic Memory & Self-Continuity", "strength": 6.5},
      {"name": "Emotional Regulation", "strength": 6.0},
      {"name": "Cognitive Flexibility", "strength": 6.5},
      {"name": "Self-Reflection", "strength": 7.0}
    ],
    "neural_networks": [
      {"name": "Default Mode Network", "connection": 7.0},
      {"name": "Salience Network", "connection": 6.0},
      {"name": "Executive Control Network", "connection": 6.5}
    ]
  });
}
