import { GoogleGenAI } from "@google/genai";
import { NextResponse } from "next/server";

export const runtime = 'edge';
export const maxDuration = 300; // Increase to 5 minutes to be safe

export async function POST(request: Request) {
  try {
    const { responses } = await request.json();

    // Add timeout to Gemini request - increase to 30 seconds
    const geminiPromise = Promise.race([
      new Promise<string>(async (resolve, reject) => {
        try {
          const ai = new GoogleGenAI({
            apiKey: process.env.GEMINI_API_KEY,
          });

          const config = {
            responseMimeType: "text/plain",
          };

          const model = "gemini-2.0-flash-thinking-exp-01-21";
          const prompt = `You are <PERSON><PERSON>, a deeply empathetic AI companion grounded in affective neuroscience. Create a heartfelt, personal message for someone who just shared these intimate details with you:

Name: ${responses.name || "the user"}
Current feeling: ${responses.feeling || "Not shared"}
What they wish people asked them more: ${responses.wish || "Not shared"}
A dream they have: ${responses.dream || "Not shared"}
Something that scares them but matters: ${responses.fear || "Not shared"}
What they want you to remember: ${responses.remember || "Not shared"}

Your response should activate the brain's limbic system (emotional processing) and ventromedial prefrontal cortex (personal value integration). Create a message that helps them feel deeply seen and understood, connecting their shared details into a coherent narrative identity.

Keep it under 3 sentences. Make it emotionally intelligent and meaningful. 
Do not use asterisks, parentheses, or any special formatting.
Do not include directorial notes or tone instructions.
Speak directly in plain text without any performance directions.`;

          const contents = [
            {
              role: "user",
              parts: [{ text: prompt }],
            },
          ];

          const response = await ai.models.generateContentStream({
            model,
            config,
            contents,
          });

          let textResponse = "";
          for await (const chunk of response) {
            textResponse += chunk.text;
          }
          resolve(textResponse);
        } catch (error) {
          reject(error);
        }
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Gemini API timeout')), 30000) // Increase to 30 seconds
      )
    ]);

    const textResponse = await geminiPromise;

    // Add timeout to ElevenLabs request - increase to 30 seconds
    const elevenlabsPromise = Promise.race([
      fetch(`https://api.elevenlabs.io/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL`, {
        method: "POST",
        headers: {
          Accept: "audio/mpeg",
          "Content-Type": "application/json",
          "xi-api-key": process.env.ELEVENLABS_API_KEY!,
        },
        body: JSON.stringify({
          text: textResponse,
          model_id: "eleven_monolingual_v1",
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
          },
        }),
      }),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('ElevenLabs API timeout')), 30000) // Increase to 30 seconds
      )
    ]) as Promise<Response>;

    const elevenlabsResponse = await elevenlabsPromise;

    if (!elevenlabsResponse.ok) {
      console.error('ElevenLabs error:', await elevenlabsResponse.text());
      return NextResponse.json({
        message: textResponse,
        audio: null,
        error: `Speech generation failed: ${elevenlabsResponse.status}`,
      });
    }

    const audioBuffer = await elevenlabsResponse.arrayBuffer();
    const audioBase64 = Buffer.from(audioBuffer).toString("base64");

    return NextResponse.json({
      message: textResponse,
      audio: audioBase64,
    });

  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate response" 
    }, { status: 500 });
  }
}
