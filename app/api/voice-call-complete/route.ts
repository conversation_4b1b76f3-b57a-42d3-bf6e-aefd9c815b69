import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';
import * as crypto from 'crypto';

export const runtime = 'edge';
export const maxDuration = 60; // Increase to 60 seconds

export async function POST(request: Request) {
  // Set up timeout promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Function execution timeout')), 50000); // 50 seconds
  });

  try {
    return await Promise.race([
      (async () => {
        // Clone the request to read the body multiple times
        const clonedRequest = request.clone();
        const payload = await clonedRequest.text();

        // Log the incoming request for debugging
        console.log("[voice-call-complete] Received webhook payload");
        console.log("[voice-call-complete] Full payload:", payload);

        // Log all headers
        const headers = Object.fromEntries(request.headers.entries());
        console.log("[voice-call-complete] Headers:", JSON.stringify(headers, null, 2));

        // Parse the payload
        const data = JSON.parse(payload);
        console.log("[voice-call-complete] Parsed data type:", data.type);
        
        // Extract phone number from the payload
        const phoneNumber = data.data?.conversation_initiation_client_data?.dynamic_variables?.system__caller_id || 
                            data.data?.dynamic_variables?.system__caller_id;
                  
        console.log("[voice-call-complete] Extracted phone number:", phoneNumber);

        // Extract transcript from the correct location in the payload
        const transcript = data.data?.transcript || [];
        console.log("[voice-call-complete] Transcript length:", transcript.length);
        console.log("[voice-call-complete] First few transcript items:", transcript.slice(0, 3));
        
        // Check if the call ended properly with a reflective statement
        if (transcript.length > 0) {
          const lastMessage = transcript[transcript.length - 1];
          console.log("[voice-call-complete] Last message in call:", lastMessage?.text);
          
          // Check if the last message was from Yubi (agent) and not a question
          const wasProperEnding = lastMessage?.role === 'agent' && 
                                 !lastMessage?.text?.trim().endsWith('?');
          
          console.log("[voice-call-complete] Call ended properly with reflective statement:", 
                     wasProperEnding ? "YES" : "NO");
        }

        // Extract any context that was used during the call
        const contextUsed = data.data?.conversation_initiation_client_data || {};
        console.log("[voice-call-complete] Context data used:", JSON.stringify(contextUsed, null, 2));

        // Log which dynamic variables were actually used
        const dynamicVariablesUsed = contextUsed.dynamic_variables || {};
        console.log("[voice-call-complete] Dynamic variables used during call:", JSON.stringify(dynamicVariablesUsed, null, 2));

        // Check if the transcript contains references to the user's context
        if (transcript.length > 0) {
          console.log("[voice-call-complete] First Yubi response:", transcript[0]?.text);
          
          // Check if the first response contains the user's name
          const userName = dynamicVariablesUsed.user_name || "unknown";
          const containsName = transcript[0]?.text?.includes(userName);
          console.log("[voice-call-complete] First response contains user name:", containsName ? "YES" : "NO");
          
          // Check if the first response references emotional state
          const emotionalState = dynamicVariablesUsed.recent_emotion || "unknown";
          const containsEmotion = transcript[0]?.text?.toLowerCase().includes(emotionalState);
          console.log("[voice-call-complete] First response references emotional state:", containsEmotion ? "YES" : "NO");
        }

        if (!phoneNumber || transcript.length === 0) {
          return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
        }
        
        // Create Supabase client to look up the user ID from phone number
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        
        // Get user ID from phone number
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("id")
          .eq("phone_number", phoneNumber)
          .single();
          
        if (profileError || !profile) {
          console.error("[voice-call-complete] User not found for phone number:", phoneNumber);
          console.log("[voice-call-complete] Error details:", profileError);
          
          // Try a more flexible phone number search (similar to voice-call-init)
          const phoneWithoutPlus = phoneNumber.replace('+', '');
          const { data: profile2, error: profileError2 } = await supabase
            .from("profiles")
            .select("id")
            .eq("phone_number", phoneWithoutPlus)
            .single();
            
          if (profileError2 || !profile2) {
            console.error("[voice-call-complete] User not found with alternative format either");
            return NextResponse.json({ error: "User not found for this phone number" }, { status: 404 });
          }
          
          // Use the profile from the second search
          console.log("[voice-call-complete] Found user with alternative phone format");
          var userId = profile2.id;
        } else {
          // Use the profile from the first search
          var userId = profile.id;
        }
        
        console.log("[voice-call-complete] Resolved userId:", userId);
        
        // Format conversation for summarization
        const formattedConversation = transcript.map((item: any) => ({
          role: item.role === "agent" ? "assistant" : "user",
          content: item.message
        }));
        
        console.log("[voice-call-complete] Formatted conversation:", formattedConversation);
        
        // Call existing summarization API
        const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${request.headers.get('host')}`;
        console.log("[voice-call-complete] Base URL for internal API calls:", baseUrl);

        const summaryResponse = await fetch(`${baseUrl}/api/summarize-conversation`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            conversation: formattedConversation,
            userId: userId
          })
        });

        const summaryResult = await summaryResponse.json();
        console.log("[voice-call-complete] Summary result:", summaryResult);

        // Trigger neural profile update
        await fetch(`${baseUrl}/api/trigger-neural-profile`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ userId: userId })
        });
        
        // After successful summarization
        if (summaryResult.conversation) {
          // Store the conversation as a memory with embedding
          console.log("[voice-call-complete] About to store memory with embedding");
          
          try {
            const { storeMemory } = await import('@/lib/embedding-service');
            
            // Create a memory from the conversation summary
            console.log("[voice-call-complete] Calling storeMemory with params:", {
              userId,
              summaryLength: (summaryResult.summary || "Phone conversation").length,
              memoryType: 'conversation',
              emotion: summaryResult.emotions?.[0] || "neutral",
              conversationId: summaryResult.conversation.id
            });
            
            const emotionalContext = {
              emotion: summaryResult.emotions?.[0] || "neutral",
              intensity: 5,  // Default intensity
              speechPace: summaryResult.speechAnalysis?.pace,
              speechVolume: summaryResult.speechAnalysis?.volume,
              speechTone: summaryResult.speechAnalysis?.tone,
              emotionalKeywords: summaryResult.emotions || []
            };
            
            const memoryId = await storeMemory(
              userId,
              summaryResult.summary || "Phone conversation",
              'conversation',
              emotionalContext,
              summaryResult.conversation.id,
              7  // Higher importance for phone calls
            );
            
            // Ensure the neural graph is updated with this new memory
            const { updateMemoryWithEmotionalContext } = await import('@/lib/advanced-memory-retrieval');
            await updateMemoryWithEmotionalContext(memoryId, emotionalContext);
            
            console.log("[voice-call-complete] Successfully stored memory with ID:", memoryId);
          } catch (error) {
            console.error("[voice-call-complete] Error storing memory:", error);
          }
        }
        
        return NextResponse.json({ 
          success: true,
          message: "Call processed successfully",
          conversationId: summaryResult.conversationId
        });
      })(),
      timeoutPromise
    ]);
  } catch (error) {
    if (error instanceof Error && error.message === 'Function execution timeout') {
      console.log("[voice-call-complete] Function execution timed out, returning minimal response");
      return NextResponse.json({
        success: true,
        message: "Call processing timed out but was received",
      });
    }
    
    console.error("[voice-call-complete] Error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
