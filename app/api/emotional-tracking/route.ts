import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  console.log("[emotional-tracking] API endpoint called with full request:", request);
  console.time("emotional-tracking-total");
  
  try {
    const requestBody = await request.json();
    console.log("[emotional-tracking] Request body:", JSON.stringify(requestBody, null, 2));
    
    const { 
      userId, 
      conversationId, 
      emotion, 
      intensity, 
      speechPace, 
      speechVolume, 
      speechTone, 
      emotionalKeywords,
      timestamp 
    } = requestBody;
    
    console.log("[emotional-tracking] Processing emotional data for user:", userId);
    console.log("[emotional-tracking] Data to be inserted:", {
      user_id: userId,
      conversation_id: conversationId,
      emotion,
      intensity,
      speech_pace: speechPace,
      speech_volume: speechVolume,
      speech_tone: speechTone,
      emotional_keywords: emotionalKeywords,
      timestamp
    });
    
    if (!userId || !conversationId) {
      console.error("[emotional-tracking] Missing required fields:", { userId, conversationId });
      return NextResponse.json({ 
        error: "User ID and conversation ID are required" 
      }, { status: 400 });
    }
    
    // Create Supabase client
    console.log("[emotional-tracking] Creating Supabase client with URL:", process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log("[emotional-tracking] Service role key exists:", !!process.env.SUPABASE_SERVICE_ROLE_KEY);
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role key for server operations
    );
    
    // Insert emotional tracking data
    console.log("[emotional-tracking] Inserting emotional data");
    console.time("supabase-insert");

    const insertData = {
      user_id: userId,
      conversation_id: conversationId,
      emotion: emotion || null,
      intensity: intensity || null,
      speech_pace: speechPace || null,
      speech_volume: speechVolume || null,
      speech_tone: speechTone || null,
      emotional_keywords: emotionalKeywords || [],
      in_conversation_timestamp: typeof requestBody.in_conversation_timestamp === 'number' 
        ? requestBody.in_conversation_timestamp 
        : Math.floor(Date.now() / 1000), // Ensure we're using seconds
      timestamp: new Date().toISOString()
    };

    console.log("[emotional-tracking] Final insert data:", insertData);

    const { data, error } = await supabase
      .from("emotional_tracking")
      .insert(insertData)
      .select()
      .single();

    console.timeEnd("supabase-insert");

    if (error) {
      console.error("[emotional-tracking] Error inserting data:", error);
      console.error("[emotional-tracking] Error details:", JSON.stringify(error, null, 2));
      throw error;
    }
    
    console.log("[emotional-tracking] Insert successful, returned data:", data);
    
    // Get recent emotional data for this conversation
    console.log("[emotional-tracking] Fetching recent emotional data");
    console.time("fetch-recent-data");
    
    const { data: recentData, error: recentError } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, speech_pace, speech_volume, speech_tone, emotional_keywords, in_conversation_timestamp")
      .eq("conversation_id", conversationId)
      .order("in_conversation_timestamp", { ascending: false })
      .limit(10);
    
    console.timeEnd("fetch-recent-data");
    
    if (recentError) {
      console.error("[emotional-tracking] Error fetching recent data:", recentError);
      throw recentError;
    }
    
    // Calculate emotional state summary
    console.log("[emotional-tracking] Calculating emotional state summary");
    const emotionalState = calculateEmotionalState(recentData || []);
    
    console.log("[emotional-tracking] API call completed successfully");
    console.timeEnd("emotional-tracking-total");
    
    return NextResponse.json({
      success: true,
      trackingId: data?.id,
      emotionalState
    });
    
  } catch (error) {
    console.error("[emotional-tracking] API error:", error);
    console.timeEnd("emotional-tracking-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to track emotional data" 
    }, { status: 500 });
  }
}

// Helper function to calculate current emotional state from recent data points
function calculateEmotionalState(recentData: any[]) {
  if (!recentData.length) return null;
  
  // Calculate average metrics
  const avgPace = recentData.reduce((sum, item) => sum + (item.speech_pace || 0), 0) / recentData.length;
  const avgVolume = recentData.reduce((sum, item) => sum + (item.speech_volume || 0), 0) / recentData.length;
  const avgTone = recentData.reduce((sum, item) => sum + (item.speech_tone || 0), 0) / recentData.length;
  
  // Count emotion occurrences
  const emotionCounts: Record<string, number> = {};
  recentData.forEach(item => {
    if (item.emotion) {
      emotionCounts[item.emotion] = (emotionCounts[item.emotion] || 0) + 1;
    }
  });
  
  // Find dominant emotion
  let dominantEmotion = null;
  let maxCount = 0;
  
  Object.entries(emotionCounts).forEach(([emotion, count]) => {
    if (count > maxCount) {
      dominantEmotion = emotion;
      maxCount = count;
    }
  });
  
  // Collect all emotional keywords
  const allKeywords: string[] = [];
  recentData.forEach(item => {
    if (item.emotional_keywords && Array.isArray(item.emotional_keywords)) {
      allKeywords.push(...item.emotional_keywords);
    }
  });
  
  // Count keyword occurrences
  const keywordCounts: Record<string, number> = {};
  allKeywords.forEach(keyword => {
    keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
  });
  
  // Get top keywords
  const topKeywords = Object.entries(keywordCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([keyword]) => keyword);
  
  // Calculate emotional trend (positive, negative, neutral)
  const recentTones = recentData.slice(0, 3).map(item => item.speech_tone || 0);
  let trend = 'neutral';
  
  if (recentTones.length >= 3) {
    const avgRecentTone = recentTones.reduce((sum, tone) => sum + tone, 0) / recentTones.length;
    trend = avgRecentTone > 0.2 ? 'positive' : avgRecentTone < -0.2 ? 'negative' : 'neutral';
  }
  
  return {
    dominantEmotion,
    averagePace: Math.round(avgPace),
    averageVolume: Math.round(avgVolume),
    averageTone: avgTone.toFixed(2),
    topKeywords,
    trend,
    emotionCounts,
    keywordCounts
  };
}

// GET endpoint to retrieve emotional state for a conversation
export async function GET(request: Request) {
  const url = new URL(request.url);
  const conversationId = url.searchParams.get('conversationId');
  const userId = url.searchParams.get('userId');
  
  if (!conversationId || !userId) {
    return NextResponse.json({ 
      error: "Conversation ID and user ID are required" 
    }, { status: 400 });
  }
  
  try {
    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Get emotional data for this conversation
    const { data, error } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, speech_pace, speech_volume, speech_tone, emotional_keywords, in_conversation_timestamp")
      .eq("conversation_id", conversationId)
      .eq("user_id", userId)
      .order("in_conversation_timestamp", { ascending: false });
    
    if (error) {
      throw error;
    }
    
    // Calculate emotional state
    const emotionalState = calculateEmotionalState(data || []);
    
    return NextResponse.json({
      success: true,
      emotionalState,
      dataPoints: data?.length || 0
    });
    
  } catch (error) {
    console.error("[emotional-tracking] GET API error:", error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to retrieve emotional data" 
    }, { status: 500 });
  }
}
