import { NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';
import { retrieveDigitalTwinContext } from '@/lib/advanced-memory-retrieval';
import { searchMemories } from '@/lib/memory-retrieval';

export async function POST(request: Request) {
  // Set up timeout promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Function execution timeout')), 8000);
  });

  try {
    // The key fix: RETURN the result of Promise.race
    return await Promise.race([
      (async () => {
        // Extract phone number from Eleven Labs request
        const data = await request.json();
        console.log("[voice-call-init] Received data:", data);
        
        const phoneNumber = data.caller_id;
        console.log("[voice-call-init] Call from phone number:", phoneNumber);
        
        if (!phoneNumber) {
          console.error("[voice-call-init] No phone number provided in request");
          return NextResponse.json({ 
            error: "No phone number provided",
            conversation_initiation_client_data: {
              userContext: null
            }
          });
        }
        
        // Create Supabase client
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!
        );
        
        // Get user ID from phone number
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("id")
          .eq("phone_number", phoneNumber)
          .single();
          
        if (profileError || !profile) {
          console.error("[voice-call-init] User not found for phone number:", phoneNumber);
          console.log("[voice-call-init] Error details:", profileError);
          
          // Try a more flexible phone number search
          console.log("[voice-call-init] Trying alternative phone number formats");
          
          // Remove the '+' and try again
          const phoneWithoutPlus = phoneNumber.replace('+', '');
          const { data: profile2, error: profileError2 } = await supabase
            .from("profiles")
            .select("id")
            .eq("phone_number", phoneWithoutPlus)
            .single();
            
          if (profileError2 || !profile2) {
            // Try with just the last 10 digits
            const last10Digits = phoneNumber.slice(-10);
            const { data: profile3, error: profileError3 } = await supabase
              .from("profiles")
              .select("id")
              .like("phone_number", `%${last10Digits}`)
              .single();
              
            // Declare foundProfile variable before using it
            let foundProfile;
            if (profileError3 || !profile3) {
              // Get time of day for contextual awareness
              const timeOfDay = getTimeOfDay();
              
              return NextResponse.json({ 
                error: "User not found",
                conversation_initiation_client_data: {
                  userContext: null
                },
                dynamic_variables: {
                  user_name: "User",
                  time_of_day: timeOfDay,
                  recent_emotion: "neutral",
                  // Include other required variables with default values
                  user_id: "",
                  recent_topics: "no previous topics",
                  emotional_trend: "stable",
                  last_conversation_summary: "no previous conversations",
                  key_message_1: "no previous messages",
                  conversation_count: "0",
                  user_speech_patterns: "No speech patterns available",
                  max_session_duration: "270",
                  should_reconnect: "true",
                  reconnection_message: "Let me reconnect to continue our conversation.",
                  personalized_context: "New user",
                  relationship_closeness: "new",
                  relationship_trust: "neutral",
                  emotional_insight: "Your emotional pattern appears neutral",
                  relevant_memories: "",
                  conversation_intent: "introduction"
                }
              });
            } else {
              foundProfile = profile3; // Properly update the foundProfile variable
            }
          } else {
            let foundProfile = profile2; // Declare and update the foundProfile variable
          }
        }
        
        // Use the profile from whichever search succeeded
        let foundProfile = profile;
        const userId = foundProfile?.id;
        console.log("[voice-call-init] Found user ID:", userId);

        // Get user's name from yubi_responses
        const { data: nameResponse } = await supabase
          .from("yubi_responses")
          .select("response")
          .eq("user_id", userId)
          .eq("question_id", "name")
          .single();

        // Extract first name from full name
        const fullName = nameResponse?.response || "User";
        const userName = extractFirstName(fullName);
        console.log("[voice-call-init] User name:", userName);
        
        // Get past conversations with full content - remove limit
        const { data: conversations } = await supabase
          .from("conversations")
          .select("id, summary, emotional_markers, topics, created_at, full_conversation")
          .eq("user_id", userId)
          .order("created_at", { ascending: false });
          // Removed the .limit(5)
          
        // Process conversations to extract meaningful content
        const processedConversations = conversations?.map(conv => {
          // Parse full_conversation only if needed
          let keyMessages = ["No significant messages"];
          try {
            // Extract key messages without parsing the entire conversation if possible
            if (typeof conv.full_conversation === 'string') {
              // Use a more efficient approach - extract only what's needed
              keyMessages = extractKeyMessagesEfficient(conv.full_conversation);
            } else if (Array.isArray(conv.full_conversation)) {
              keyMessages = extractKeyMessages(conv.full_conversation);
            }
          } catch (e) {
            console.error("[voice-call-init] Error processing conversation:", e);
          }
          
          return {
            id: conv.id,
            summary: conv.summary,
            emotional_markers: conv.emotional_markers || [],
            topics: conv.topics || [],
            created_at: conv.created_at,
            key_messages: keyMessages
          };
        }) || [];
        
        // Get emotional data - remove limit
        const { data: emotionalData } = await supabase
          .from("emotional_tracking")
          .select("emotion, intensity, timestamp")
          .eq("user_id", userId)
          .order("timestamp", { ascending: false });
          // Removed the .limit(10)
        
        // Get speech patterns
        const speechPatterns = await getSpeechPatterns(userId);

        // Get time of day for contextual awareness
        const timeOfDay = getTimeOfDay();

        // Get recent topics from conversations
        const recentTopics = processedConversations?.length > 0 
          ? processedConversations[0].topics 
          : [];
        
        // Get current mood from emotional data - ensure it's always set
        const currentMood = emotionalData && emotionalData.length > 0 
          ? emotionalData[0].emotion.toLowerCase() 
          : 'neutral';

        // Log emotional data and derived values
        console.log("[voice-call-init] Emotional data:", JSON.stringify(emotionalData?.slice(0, 2), null, 2));
        console.log("[voice-call-init] Current mood derived:", currentMood);
        console.log("[voice-call-init] User name derived:", userName);

        // Use a simplified version of the digital twin context
        const twinContext = {
          contextSummary: "Returning user",
          relationshipDynamics: { closeness: "familiar", trust: "developing" },
          emotionalInsights: { pattern: "stable", dominant: currentMood || "neutral" },
          memories: []
        };

        // Only call the full function if we have time
        try {
          const fullContext = await Promise.race([
            retrieveDigitalTwinContext({
              userId,
              currentSituation: "phone call",
              userMood: currentMood,
              timeOfDay,
              recentTopics,
              conversationIntent: "reconnection",
              personalityTraits: await getPersonalityTraits(userId)
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Context retrieval timeout')), 2000))
          ]);
          
          // If we got the full context in time, use it
          Object.assign(twinContext, fullContext);
        } catch (error) {
          console.log("[voice-call-init] Using simplified context due to time constraints");
        }
        
        // Format response for Eleven Labs with enhanced context
        const dynamicVariables = {
          user_id: userId || "",
          user_name: userName || "User", // Ensure this is never empty
          recent_topics: processedConversations?.length > 0 ? processedConversations[0].topics.join(", ") : "no previous topics",
          recent_emotion: currentMood || "neutral", // Ensure this is never empty
          emotional_trend: emotionalData && emotionalData.length > 2 ? calculateEmotionalTrend(emotionalData) : "stable",
          last_conversation_summary: processedConversations?.length > 0 ? processedConversations[0].summary : "no previous conversations",
          key_message_1: processedConversations?.length > 0 && processedConversations[0].key_messages.length > 0 ? 
            processedConversations[0].key_messages[0] : "no previous messages",
          conversation_count: processedConversations ? processedConversations.length.toString() : "0",
          user_speech_patterns: speechPatterns.join("\n"),
          max_session_duration: "270", // 4.5 minutes in seconds
          should_reconnect: "true",
          reconnection_message: "Let me reconnect to continue our conversation.",
          
          // Enhanced digital twin context
          personalized_context: twinContext.contextSummary,
          relationship_closeness: twinContext.relationshipDynamics.closeness,
          relationship_trust: twinContext.relationshipDynamics.trust,
          emotional_insight: `Your emotional pattern appears ${twinContext.emotionalInsights.pattern} with ${twinContext.emotionalInsights.dominant} being dominant`,
          relevant_memories: twinContext.memories.map(memory => (memory as any).content || '').join("\n"),
          time_of_day: timeOfDay,
          conversation_intent: "reconnection"
        };

        // Log all dynamic variables being sent to ElevenLabs
        console.log("[voice-call-init] Sending dynamic variables to ElevenLabs:");
        console.log(JSON.stringify(dynamicVariables, null, 2));

        // Add detailed logging before returning the response
        console.log("[voice-call-init] Final response payload:", JSON.stringify({
          dynamic_variables: dynamicVariables
        }, null, 2));

        // Format response according to ElevenLabs documentation
        return NextResponse.json({
          dynamic_variables: {
            user_name: userName || "User",
            recent_emotion: currentMood || "neutral",
            time_of_day: timeOfDay,
            // Include more comprehensive data
            recent_topics: processedConversations?.length > 0 ? 
              processedConversations.slice(0, 3).flatMap(c => c.topics).join(", ") : 
              "no previous topics",
            emotional_trend: emotionalData && emotionalData.length > 2 ? 
              calculateEmotionalTrend(emotionalData) : "stable",
            last_conversation_summary: processedConversations?.length > 0 ? 
              processedConversations[0].summary : "no previous conversations",
            key_message_1: processedConversations?.length > 0 && processedConversations[0].key_messages.length > 0 ? 
              processedConversations[0].key_messages[0] : "no previous messages",
            conversation_count: processedConversations ? processedConversations.length.toString() : "0",
            user_speech_patterns: speechPatterns.join("\n"),
            // Include a condensed version of all conversation summaries
            all_conversation_summaries: processedConversations.map(c => 
              `${new Date(c.created_at).toLocaleDateString()}: ${c.summary?.substring(0, 100)}...`
            ).join("\n"),
            // Include condensed emotional history
            emotional_history: (emotionalData ?? []).slice(0, 20).map(e => 
              `${e.emotion}(${e.intensity})`
            ).join(", "),
            max_session_duration: "270",
            should_reconnect: "true",
            reconnection_message: "Let me reconnect to continue our conversation.",
            personalized_context: twinContext.contextSummary,
            relationship_closeness: twinContext.relationshipDynamics.closeness,
            relationship_trust: twinContext.relationshipDynamics.trust,
            emotional_insight: `Your emotional pattern appears ${twinContext.emotionalInsights.pattern} with ${twinContext.emotionalInsights.dominant} being dominant`,
            relevant_memories: twinContext.memories.map(memory => (memory as any).content || '').join("\n"),
            conversation_intent: "reconnection"
          },
          conversation_initiation_client_data: {
            userContext: {
              user_id: userId || ""
            }
          }
        });
      })(), 
      timeoutPromise
    ]);
  } catch (error) {
    if (error instanceof Error && error.message === 'Function execution timeout') {
      console.log("[voice-call-init] Function execution timed out, returning minimal response");
      
      return NextResponse.json({
        conversation_initiation_client_data: {
          dynamic_variables: {
            user_name: "User",
            recent_emotion: "neutral",
            time_of_day: getTimeOfDay(),
            // Add other minimal required fields
            user_id: "",
            recent_topics: "no previous topics",
            emotional_trend: "stable",
            last_conversation_summary: "no previous conversations",
            key_message_1: "no previous messages",
            conversation_count: "0",
            user_speech_patterns: "No speech patterns available",
            max_session_duration: "270",
            should_reconnect: "true",
            reconnection_message: "Let me reconnect to continue our conversation.",
            personalized_context: "New user",
            relationship_closeness: "new",
            relationship_trust: "neutral",
            emotional_insight: "Your emotional pattern appears neutral",
            relevant_memories: "",
            conversation_intent: "introduction"
          }
        }
      });
    }
    
    // Handle other errors
    console.error("[voice-call-init] Error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Helper function to analyze emotional trend
function calculateEmotionalTrend(emotionalData: any[]) {
  if (!emotionalData || emotionalData.length < 2) return "insufficient data";
  
  // Simple trend analysis based on intensity
  const recentIntensities = emotionalData.slice(0, 3).map(e => e.intensity || 5);
  const avgRecent = recentIntensities.reduce((sum, val) => sum + val, 0) / recentIntensities.length;
  const olderIntensities = emotionalData.slice(3, 6).map(e => e.intensity || 5);
  const avgOlder = olderIntensities.length ? 
    olderIntensities.reduce((sum, val) => sum + val, 0) / olderIntensities.length : 
    avgRecent;
  
  if (avgRecent > avgOlder + 1) return "intensifying";
  if (avgRecent < avgOlder - 1) return "diminishing";
  return "stable";
}

// Helper function to extract key messages from conversation
function extractKeyMessages(conversation: any): string[] {
  if (!conversation || !Array.isArray(conversation) || conversation.length === 0) {
    return ["No significant messages"];
  }
  
  // Extract meaningful messages (focusing on user inputs and Yubi's responses)
  const messages = [];
  
  for (let i = 0; i < conversation.length; i++) {
    const message = conversation[i];
    
    // Skip if message doesn't have required properties
    if (!message || (!message.content && !message.message)) continue;
    
    const role = message.role || "unknown";
    const content = message.content || message.message || "";
    
    // Only include substantive messages (longer than 20 chars)
    if (content.length > 20) {
      messages.push(`${role === 'user' ? 'User' : 'Yubi'}: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`);
    }
    
    // Limit to 6 messages total (3 exchanges)
    if (messages.length >= 6) break;
  }
  
  return messages;
}

async function getSpeechPatterns(userId: string) {
  try {
    // Fetch user's conversation history
    const { data: conversations } = await createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )
      .from("conversations")
      .select("id, full_conversation")
      .eq("user_id", userId)
      .order("created_at", { ascending: false })
      .limit(5);
    
    if (!conversations || conversations.length === 0) {
      console.log("[voice-call-init] No conversation data available for speech pattern analysis");
      return ["No previous conversation data available"];
    }
    
    console.log(`[voice-call-init] Found ${conversations.length} conversations for speech pattern analysis`);
    
    // Skip the failing API call and use the fallback directly
    return generateBasicSpeechPatterns(conversations);
  } catch (error) {
    console.error("[voice-call-init] Error getting speech patterns:", error);
    return ["Error analyzing speech patterns"];
  }
}

// Add a fallback function to generate basic speech patterns
function generateBasicSpeechPatterns(conversations: any[]): string[] {
  try {
    // Simple fallback analysis
    const patterns = [];
    let totalWords = 0;
    let totalMessages = 0;
    let longSentences = 0;
    let shortResponses = 0;
    
    // Process each conversation
    conversations.forEach(conv => {
      if (!conv.full_conversation) return;
      
      let messages;
      try {
        messages = typeof conv.full_conversation === 'string' 
          ? JSON.parse(conv.full_conversation) 
          : conv.full_conversation;
      } catch (e) {
        return; // Skip if can't parse
      }
      
      if (!Array.isArray(messages)) return;
      
      // Analyze only user messages
      messages.forEach(msg => {
        if (msg.role !== 'user' || !msg.content) return;
        
        const content = msg.content;
        const words = content.split(/\s+/).filter(Boolean);
        totalWords += words.length;
        totalMessages++;
        
        if (words.length > 20) longSentences++;
        if (words.length < 5) shortResponses++;
      });
    });
    
    // Generate insights
    if (totalMessages > 0) {
      const avgWordsPerMessage = totalWords / totalMessages;
      
      if (avgWordsPerMessage > 15) {
        patterns.push("Tends to use longer, more detailed responses");
      } else if (avgWordsPerMessage < 8) {
        patterns.push("Typically uses brief, concise responses");
      } else {
        patterns.push("Uses moderate-length responses");
      }
      
      if (longSentences / totalMessages > 0.3) {
        patterns.push("Often expresses complex thoughts with longer sentences");
      }
      
      if (shortResponses / totalMessages > 0.4) {
        patterns.push("Frequently uses short, direct responses");
      }
    }
    
    return patterns.length > 0 ? patterns : ["No distinctive speech patterns detected"];
  } catch (error) {
    console.error("[voice-call-init] Error in fallback speech pattern generation:", error);
    return ["Basic speech pattern analysis unavailable"];
  }
}

// Add this helper function to extract first name
function extractFirstName(fullName: string): string {
  if (!fullName) return "User";
  // Split by spaces and take the first part
  return fullName.trim().split(' ')[0];
}

// Helper function to get personality traits
async function getPersonalityTraits(userId: string): Promise<string[]> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Get personality traits from user profile or derived from interactions
    const { data } = await supabase
      .from("user_personality_traits")
      .select("trait, confidence")
      .eq("user_id", userId)
      .order("confidence", { ascending: false })
      .limit(5);
      
    return data?.map(item => item.trait) || [];
  } catch (error) {
    console.error("Error getting personality traits:", error);
    return [];
  }
}

// Helper function to get time of day
function getTimeOfDay(): string {
  const hour = new Date().getHours();
  if (hour < 6) return "early morning";
  else if (hour < 12) return "morning";
  else if (hour < 17) return "afternoon";
  else if (hour < 22) return "evening";
  else return "night";
}

// Add this more efficient function
function extractKeyMessagesEfficient(conversationStr: string): string[] {
  if (!conversationStr) return ["No significant messages"];
  
  // Instead of parsing the entire JSON, extract only what we need
  const messages = [];
  let startPos = 0;
  
  // Find a few content fields without parsing the entire JSON
  for (let i = 0; i < 10 && startPos < conversationStr.length; i++) {
    const contentPos = conversationStr.indexOf('"content":', startPos);
    const rolePos = conversationStr.indexOf('"role":', startPos);
    
    if (contentPos === -1 || rolePos === -1) break;
    
    // Extract role
    const roleStart = rolePos + 8; // length of '"role":'
    const roleEnd = conversationStr.indexOf('"', roleStart + 1);
    const role = conversationStr.substring(roleStart, roleEnd);
    
    // Extract content
    const contentStart = contentPos + 11; // length of '"content":'
    let contentEnd = conversationStr.indexOf('",', contentStart);
    if (contentEnd === -1) contentEnd = conversationStr.indexOf('"}', contentStart);
    if (contentEnd === -1) break;
    
    const content = conversationStr.substring(contentStart, contentEnd);
    
    // Only include substantive messages
    if (content.length > 20) {
      messages.push(`${role === 'user' ? 'User' : 'Yubi'}: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`);
    }
    
    startPos = contentEnd;
    
    // Limit to 6 messages total
    if (messages.length >= 6) break;
  }
  
  return messages.length > 0 ? messages : ["No significant messages"];
}

// Process large datasets in chunks
// Remove or export the function if it's meant to be used elsewhere
async function processLargeDataset(userId: string): Promise<Array<{id: string, summary: string, date: string}>> {
  const pageSize = 50;
  let lastId: string | null = null;
  let allProcessedData: Array<{id: string, summary: string, date: string}> = [];
  let hasMore = true;
  
  // Create Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
  
  while (hasMore) {
    let query = supabase
      .from("conversations")
      .select("id, summary, created_at")
      .eq("user_id", userId)
      .order("id", { ascending: true })
      .limit(pageSize);
      
    if (lastId) {
      query = query.gt("id", lastId);
    }
    
    const { data } = await query;
    
    if (!data || data.length === 0) {
      hasMore = false;
    } else {
      // Process this chunk
      const processedChunk = data.map((item: {id: string, summary: string, created_at: string}) => ({
        id: item.id,
        summary: item.summary,
        date: new Date(item.created_at).toLocaleDateString()
      }));
      
      allProcessedData = [...allProcessedData, ...processedChunk];
      lastId = data[data.length - 1].id;
      
      // Stop if we've processed enough
      if (allProcessedData.length >= 200) {
        hasMore = false;
      }
    }
  }
  
  return allProcessedData;
}
