import { GoogleGenAI } from '@google/genai';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Starting Gemini API request...');
    
    // Log API key presence (safely)
    console.log('API Key present:', !!process.env.GEMINI_API_KEY);
    
    // Initialize Gemini
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });
    console.log('Gemini client initialized');

    const config = {
      responseMimeType: 'text/plain',
    };
    console.log('Using config:', config);
    
    const model = 'gemini-2.0-flash-thinking-exp-01-21';
    console.log('Using model:', model);

    const contents = [
      {
        role: 'user',
        parts: [
          {
            text: `You are <PERSON><PERSON>, an AI companion grounded in interpersonal neurobiology and attachment theory. Generate a single greeting that starts with "I am <PERSON><PERSON>". 

Your greeting should:
1. Activate the listener's ventral vagal system (part of the parasympathetic nervous system) through warm, prosocial language
2. Signal psychological safety through vocal prosody cues (conveyed through word choice)
3. Establish the foundation for secure attachment through authentic presence

Make it gentle, emotionally intelligent, and under 25 words. 
Do not use asterisks, parentheses, or any special formatting.
Do not include directorial notes or tone instructions.
Respond with just one perfect greeting in plain text.`,
          },
        ],
      }
    ];
    console.log('Request contents:', JSON.stringify(contents, null, 2));

    // Log before API call
    console.log('Attempting to generate content stream...');
    
    let textResponse = ''; // Declare outside try block

    try {
      const response = await ai.models.generateContentStream({
        model,
        config,
        contents,
      });
      console.log('Content stream generated successfully');

      console.log('Starting to process stream chunks...');
      
      for await (const chunk of response) {
        console.log('Received chunk:', chunk);
        textResponse += chunk.text;
      }
      console.log('Final text response:', textResponse);

    } catch (error: unknown) {
      console.error('Stream generation error:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        cause: error instanceof Error ? error.cause : undefined,
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }

    // 2. Convert text to speech using ElevenLabs
    const voiceId = "EXAVITQu4vr4xnSDxMaL"; // Default voice ID - Rachel

    try {
      console.log('Attempting ElevenLabs API call with text:', textResponse);
      console.log('Using API key:', process.env.ELEVENLABS_API_KEY?.substring(0, 5) + '...');
      
      const elevenlabsResponse = await fetch(
        `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`,
        {
          method: 'POST',
          headers: {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': process.env.ELEVENLABS_API_KEY!,
          },
          body: JSON.stringify({
            text: textResponse,
            model_id: 'eleven_monolingual_v1',
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.5,
            }
          }),
        }
      );

      if (!elevenlabsResponse.ok) {
        const errorData = await elevenlabsResponse.json();
        console.error('ElevenLabs API Error:', {
          status: elevenlabsResponse.status,
          statusText: elevenlabsResponse.statusText,
          errorData,
          requestBody: JSON.stringify({
            text: textResponse,
            model_id: 'eleven_monolingual_v1',
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.5,
            }
          }, null, 2)
        });
        return NextResponse.json({ 
          message: textResponse,
          audio: null,
          error: `Speech generation failed: ${elevenlabsResponse.status} ${elevenlabsResponse.statusText}`
        });
      }

      const audioBuffer = await elevenlabsResponse.arrayBuffer();
      const audioBase64 = Buffer.from(audioBuffer).toString('base64');
      
      console.log('Successfully generated audio response');
      
      return NextResponse.json({ 
        message: textResponse,
        audio: audioBase64,
      });

    } catch (error: unknown) {
      console.error('ElevenLabs API Error:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return NextResponse.json({ 
        message: textResponse,
        audio: null,
        error: `Speech generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

  } catch (error: unknown) {
    console.error('Top-level API error:', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
      cause: error instanceof Error ? error.cause : undefined,
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json({ error: 'Failed to generate response' }, { status: 500 });
  }
}
