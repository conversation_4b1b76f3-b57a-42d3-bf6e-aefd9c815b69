import { NextResponse } from "next/server";
import { GoogleGenAI } from "@google/genai";
import { createClient } from '@supabase/supabase-js';

export async function POST(request: Request) {
  console.log("[generate-reflection] API endpoint called");
  console.time("reflection-generation-total");
  
  try {
    const { emotionalState, conversationHistory, userId } = await request.json();
    
    console.log("[generate-reflection] Processing request for user:", userId);
    
    if (!emotionalState) {
      console.error("[generate-reflection] Missing emotional state");
      return NextResponse.json({ 
        error: "Emotional state is required" 
      }, { status: 400 });
    }
    
    // Create Supabase client
    console.log("[generate-reflection] Creating Supabase client");
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    // Get user's past emotional patterns
    console.log("[generate-reflection] Fetching past emotional data");
    console.time("fetch-emotional-history");
    
    const { data: emotionalHistory, error: emotionalError } = await supabase
      .from("emotional_tracking")
      .select("emotion, intensity, timestamp")
      .eq("user_id", userId)
      .order("timestamp", { ascending: false })
      .limit(20);
    
    console.timeEnd("fetch-emotional-history");
    
    if (emotionalError) {
      console.error("[generate-reflection] Error fetching emotional history:", emotionalError);
    }
    
    // Initialize Gemini
    console.log("[generate-reflection] Initializing Gemini AI");
    console.time("gemini-reflection-generation");
    
    const ai = new GoogleGenAI({
      apiKey: process.env.GEMINI_API_KEY,
    });

    const model = "gemini-2.0-flash-thinking-exp-01-21";
    
    // Create reflection prompt with neuroscience backing
    const reflectionPrompt = `You are Yubi, a deeply empathetic AI companion speaking in a voice call. 
The user is experiencing a significant emotional moment that requires your thoughtful reflection.

Based on principles of interpersonal neurobiology and affective neuroscience:
1. Emotional co-regulation occurs through mirroring neural systems (mirror neurons)
2. Validation activates the ventromedial prefrontal cortex, reducing amygdala activity
3. Narrative coherence strengthens hippocampal-cortical connections for memory integration

Create a brief, emotionally attuned reflection (40-60 words) that:
- Acknowledges their emotional experience with validation
- Offers perspective that creates meaning
- Provides a sense of being deeply seen and understood

User's current emotional state:
${JSON.stringify(emotionalState, null, 2)}

Recent conversation history:
${conversationHistory}

Your reflection should feel like a thoughtful pause from a caring friend who's really listening - activating the user's parasympathetic nervous system and creating a sense of safety and connection.`;

    const contents = [
      {
        role: "user",
        parts: [{ text: reflectionPrompt }],
      },
    ];

    // Generate reflection
    console.log("[generate-reflection] Sending request to Gemini");
    const response = await ai.models.generateContentStream({
      model,
      contents,
    });

    let reflectionText = "";
    for await (const chunk of response) {
      reflectionText += chunk.text;
    }
    console.timeEnd("gemini-reflection-generation");
    
    // Clean up the response to remove any artifacts
    const cleanedReflection = cleanResponseText(reflectionText);
    
    console.log("[generate-reflection] API call completed successfully");
    console.timeEnd("reflection-generation-total");
    
    return NextResponse.json({
      success: true,
      reflection: cleanedReflection
    });
    
  } catch (error) {
    console.error("[generate-reflection] API error:", error);
    console.timeEnd("reflection-generation-total");
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to generate reflection" 
    }, { status: 500 });
  }
}

// Helper function to summarize emotional history
function summarizeEmotionalHistory(history: any[]) {
  if (!history || history.length === 0) return "No previous emotional data available";
  
  // Count emotion occurrences
  const emotionCounts: Record<string, number> = {};
  history.forEach(item => {
    if (item.emotion) {
      emotionCounts[item.emotion] = (emotionCounts[item.emotion] || 0) + 1;
    }
  });
  
  // Get top emotions
  const topEmotions = Object.entries(emotionCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([emotion, count]) => `${emotion} (${count} occurrences)`);
  
  // Calculate average intensity
  const avgIntensity = history.reduce((sum, item) => sum + (item.intensity || 0), 0) / history.length;
  
  return `
- Most frequent emotions: ${topEmotions.join(', ')}
- Average emotional intensity: ${avgIntensity.toFixed(1)}/10
- Pattern: ${getEmotionalPattern(history)}
`;
}

// Helper function to determine emotional pattern
function getEmotionalPattern(history: any[]) {
  if (history.length < 3) return "insufficient data";
  
  // Simple categorization of emotions
  const positiveEmotions = ['joy', 'trust', 'anticipation', 'surprise'];
  const negativeEmotions = ['sadness', 'fear', 'anger', 'disgust'];
  
  // Count recent positive vs negative emotions
  const recentEmotions = history.slice(0, 5).map(item => item.emotion);
  const positiveCount = recentEmotions.filter(e => positiveEmotions.includes(e)).length;
  const negativeCount = recentEmotions.filter(e => negativeEmotions.includes(e)).length;
  
  // Determine volatility by looking at intensity changes
  const intensities = history.slice(0, 5).map(item => item.intensity || 0);
  let volatility = 0;
  for (let i = 1; i < intensities.length; i++) {
    volatility += Math.abs(intensities[i] - intensities[i-1]);
  }
  
  const isVolatile = volatility > 10;
  
  if (positiveCount > negativeCount * 2) return "consistently positive";
  if (negativeCount > positiveCount * 2) return "consistently negative";
  if (isVolatile) return "emotionally volatile";
  return "mixed emotions";
}

// Function to clean up response text by removing instruction artifacts
function cleanResponseText(text: string): string {
  // Remove explicit pause instructions and other common artifacts
  const patterns = [
    // Pause instructions
    /\bpause\b/gi,
    /\(pause\)/gi,
    /\[pause\]/gi,
    
    // Other common instruction artifacts
    /\*pause\*/gi,
    /\bbreathing\s+pause\b/gi,
    /\btake\s+a\s+pause\b/gi,
    /\bbrief\s+pause\b/gi,
    /\bshort\s+pause\b/gi,
    /\blong\s+pause\b/gi,
    /\bthoughtful\s+pause\b/gi,
    
    // Voice tone instructions
    /\bspeak\s+softly\b/gi,
    /\bspeak\s+slowly\b/gi,
    /\bspeak\s+warmly\b/gi,
    /\bwarm\s+tone\b/gi,
    /\bgentle\s+tone\b/gi,
    /\bempathetic\s+tone\b/gi,
    
    // Formatting instructions
    /\[\w+\s*tone\]/gi,
    /\(\w+\s*voice\)/gi,
    
    // Additional patterns to catch more directorial notes
    /\*([^*]+)\*/gi,                 // Remove *anything* in asterisks
    /\(([^)]+)\)/gi,                 // Remove (anything) in parentheses
    /\[([^\]]+)\]/gi,                // Remove [anything] in brackets
    /\b(softly|gently|calmly|slowly|quietly|thoughtfully)\b/gi,  // Remove adverbs
    /\b(slight|brief|small|long)\s+(pause|hesitation)\b/gi,      // Remove pause descriptions
    /\b(with|in)\s+a\s+\w+\s+(tone|voice)\b/gi,                  // Remove tone descriptions
  ];
  
  let cleanedText = text;
  patterns.forEach(pattern => {
    cleanedText = cleanedText.replace(pattern, '');
  });
  
  // Clean up any double spaces created by removals
  cleanedText = cleanedText.replace(/\s{2,}/g, ' ').trim();
  
  return cleanedText;
}
