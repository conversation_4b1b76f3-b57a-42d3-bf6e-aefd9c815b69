"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { YubiCreationFlow } from "@/components/yubi-creation-flow"
import { LoadingScreen } from "@/components/loading-screen"
import { BackgroundParticles } from "@/components/background-particles"

export default function CreateYubiPage() {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    async function checkUser() {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()

        if (!session) {
          router.push("/")
          return
        }

        // Check if user has already completed Yubi creation
        const { data: profile } = await supabase
          .from('profiles')
          .select('yubi_creation_complete')
          .single()

        if (profile?.yubi_creation_complete) {
          router.push("/dashboard")
          return
        }

        setUser(session.user)
        setLoading(false)
      } catch (error) {
        console.error("Error checking user:", error)
        // If there's an error, we'll show the creation flow to be safe
        setLoading(false)
      }
    }

    checkUser()
  }, [router])

  if (loading) {
    return <LoadingScreen message="Preparing your Yubi experience..." />
  }

  return (
    <main className="min-h-screen bg-[rgb(10,17,40)] text-white flex flex-col relative">
      <BackgroundParticles />
      <div className="scanline"></div>
      <YubiCreationFlow userId={user?.id} />
    </main>
  )
}
