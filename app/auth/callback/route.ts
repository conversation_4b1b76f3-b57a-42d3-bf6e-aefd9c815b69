import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get("code")

  if (code) {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    const { data: { session } } = await supabase.auth.exchangeCodeForSession(code)
    
    console.log("Session user ID:", session?.user?.id);
    console.log("Auth exchange completed");

    if (session) {
      // Use service role client for database operations
      const adminClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )
      
      // Check if profile exists
      const { data: existingProfile, error: profileCheckError } = await adminClient
        .from('profiles')
        .select('id')
        .eq('id', session.user.id)
        .single()
      
      console.log("Profile check result:", existingProfile ? "Profile exists" : "No profile found", "Error:", profileCheckError?.message);

      if ((profileCheckError || !existingProfile) && session.user.id) {
        // Create profile with service role client
        const { error: createProfileError } = await adminClient
          .from('profiles')
          .insert({ id: session.user.id })
        
        console.log("Profile creation result:", createProfileError ? `Error: ${createProfileError.message}` : "Success");

        if (createProfileError) {
          console.error('Error creating profile:', createProfileError)
          return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/error?message=Failed to create user profile: ${createProfileError.message}`)
        }
      }
      
      // Check if user has completed Yubi creation
      const { data: profile } = await adminClient
        .from('profiles')
        .select('yubi_creation_complete')
        .eq('id', session.user.id)
        .single()

      // Redirect based on completion status
      if (profile?.yubi_creation_complete) {
        return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/dashboard`)
      }
    }
  }

  // Default redirect for new users or if any errors occur
  return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SITE_URL}/create-yubi`)
}
