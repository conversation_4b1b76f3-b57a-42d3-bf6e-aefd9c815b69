"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Brain, 
  Shield, 
  Network, 
  Microscope, 
  Sparkles, 
  Zap, 
  RotateCw, 
  ChevronDown,
  GamepadIcon,
  ChevronRight,
  Home,
  Users,
  FlaskConical,
  ArrowLeft,
  ExternalLink,
  X
} from "lucide-react"
import { BackgroundParticles } from "@/components/background-particles"
import gsap from "gsap"
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React from "react"

function Navbar() {
  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border-b border-[rgba(56,182,255,0.2)]">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-xl font-light text-[#38b6ff]"><PERSON><PERSON></div>
          <div className="flex space-x-6">
            <Link 
              href="/dashboard" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Home className="w-4 h-4 text-[#38b6ff]" />
              <span className="text-white">Dashboard</span>
            </Link>
            <Link 
              href="/game" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <GamepadIcon className="w-4 h-4 text-[#a137ff]" />
              <span className="text-white">Game of Life</span>
            </Link>
            <Link 
              href="/social" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Users className="w-4 h-4 text-[#ff6b37]" />
              <span className="text-white">Resonance Web</span>
            </Link>
            <Link 
              href="/lab" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <FlaskConical className="w-4 h-4 text-[#37fff1]" />
              <span className="text-white">Yubi Lab</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function LabPage() {
  const router = useRouter()
  const headerRef = useRef<HTMLDivElement>(null)
  const projectsRef = useRef<HTMLDivElement>(null)
  const projectRefs = useRef<(HTMLDivElement | null)[]>([])
  const [activeProject, setActiveProject] = useState<string | null>(null)
  const [isExperimentMode, setIsExperimentMode] = useState(false)
  const [experimentProgress, setExperimentProgress] = useState(0)
  
  // Add new state for infinite scroll
  const [visibleProjects, setVisibleProjects] = useState(6)
  const [hasMoreProjects, setHasMoreProjects] = useState(true)
  
  // Add state for modal
  const [showInsightsModal, setShowInsightsModal] = useState(false)
  const [modalContent, setModalContent] = useState<{
    title: string;
    color: string;
    metrics: any[];
    insights: string[];
    visualizationType: string;
  } | null>(null)
  
  // Function to open the insights modal
  const openInsightsModal = (projectName: string) => {
    const project = projects.find(p => p.name === projectName)
    if (!project) return
    
    // Get the experiment results for this project
    const resultData = getExperimentResults(projectName)
    const insights = generateExperimentInsights(projectName)
    
    setModalContent({
      title: `${projectName} Analysis`,
      color: project.color,
      metrics: Array.isArray(resultData.metrics) 
        ? resultData.metrics 
        : (resultData.metrics as any).metrics,
      insights: insights,
      visualizationType: projectName
    })
    
    setShowInsightsModal(true)
  }
  
  // Function to get experiment results based on project
  const getExperimentResults = (projectName: string) => {
    // Generate random metrics based on the project
    const baseMetrics = {
      Veritas: [
        { name: "Emotional Coherence", value: `${(Math.random() * 100).toFixed(1)}%` },
        { name: "Decision Alignment", value: `${(Math.random() * 10).toFixed(2)}` },
        { name: "Belief Consistency", value: `${(Math.random() * 100).toFixed(1)}%` }
      ],
      Custos: {
        metrics: [
          { name: "Privacy Score", value: `${(Math.random() * 100).toFixed(1)}%` },
          { name: "Protection Layers", value: Math.floor(Math.random() * 10) + 3 },
          { name: "Encryption Level", value: "AES-256" }
        ]
      },
      Vitae: {
        metrics: [
          { name: "Simulation Depth", value: `${Math.floor(Math.random() * 10) + 5} layers` },
          { name: "Decision Branches", value: Math.floor(Math.random() * 100) + 20 },
          { name: "Future Projections", value: Math.floor(Math.random() * 5) + 3 }
        ]
      },
      Aetheris: {
        metrics: [
          { name: "Connection Points", value: Math.floor(Math.random() * 100) + 50 },
          { name: "Resonance Strength", value: `${(Math.random() * 10).toFixed(1)}` },
          { name: "Network Depth", value: Math.floor(Math.random() * 5) + 2 }
        ]
      },
      Cognita: {
        metrics: [
          { name: "Neural Patterns", value: Math.floor(Math.random() * 1000) + 500 },
          { name: "Cognitive Markers", value: Math.floor(Math.random() * 50) + 10 },
          { name: "Research Value", value: `${(Math.random() * 10).toFixed(1)}/10` }
        ]
      },
      Somae: {
        metrics: [
          { name: "Identity Match", value: `${(Math.random() * 100).toFixed(1)}%` },
          { name: "Expression Depth", value: `${(Math.random() * 10).toFixed(1)}` },
          { name: "Language Patterns", value: Math.floor(Math.random() * 1000) + 200 }
        ]
      }
    }
    
    // Generate derived metrics based on the base metrics
    const metricsData = Array.isArray(baseMetrics[projectName as keyof typeof baseMetrics]) 
      ? baseMetrics[projectName as keyof typeof baseMetrics] 
      : (baseMetrics[projectName as keyof typeof baseMetrics] as any).metrics;
    const derivedMetrics = generateDerivedMetrics(projectName, metricsData)
    
    return {
      metrics: baseMetrics[projectName as keyof typeof baseMetrics],
      derivedMetrics: derivedMetrics,
      action: getActionText(projectName)
    }
  }
  
  // Function to generate derived metrics based on base metrics
  const generateDerivedMetrics = (projectName: string, baseMetrics: any[]) => {
    switch (projectName) {
      case "Veritas":
        const emotionalCoherence = parseFloat(baseMetrics[0].value)
        const decisionAlignment = parseFloat(baseMetrics[1].value)
        return [
          { name: "Identity Stability", value: `${((emotionalCoherence + decisionAlignment * 10) / 2).toFixed(1)}%` },
          { name: "Self-Awareness Index", value: `${(decisionAlignment * 0.8 + Math.random() * 2).toFixed(1)}/10` }
        ]
      case "Custos":
        // Similar derived metrics for other projects
        return []
      default:
        return []
    }
  }
  
  // Function to get action text based on project
  const getActionText = (projectName: string) => {
    const actionMap: Record<string, string> = {
      "Veritas": "View Emotional Map",
      "Custos": "Configure Privacy",
      "Vitae": "Enter Simulation",
      "Aetheris": "Explore Connections",
      "Cognita": "View Research Data",
      "Somae": "Meet Your Twin"
    }
    return actionMap[projectName] || "View Details"
  }
  
  // Function to load more projects on scroll
  const loadMoreProjects = () => {
    if (visibleProjects >= projects.length) {
      setHasMoreProjects(false)
      return
    }
    
    setVisibleProjects(prev => Math.min(prev + 3, projects.length))
  }
  
  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 500 && hasMoreProjects && !isExperimentMode) {
        loadMoreProjects()
      }
    }
    
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [hasMoreProjects, isExperimentMode])

  useEffect(() => {
    if (headerRef.current && projectsRef.current) {
      // Create a timeline for animations
      const tl = gsap.timeline({
        defaults: {
          duration: 1,
          ease: "power3.out"
        }
      })
      
      // Set initial states
      gsap.set(headerRef.current, {
        opacity: 0,
        y: 30
      })
      
      gsap.set(projectRefs.current, {
        opacity: 0,
        y: 30
      })
      
      // Animate the header
      tl.to(headerRef.current, {
        opacity: 1,
        y: 0,
        duration: 1.2
      })
      
      // Animate the projects with stagger
      tl.to(projectRefs.current, {
        opacity: 1,
        y: 0,
        stagger: 0.15,
        duration: 0.8
      }, "-=0.5")
    }
  }, [])
  
  useEffect(() => {
    if (isExperimentMode) {
      const interval = setInterval(() => {
        setExperimentProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 1
        })
      }, 50)
      
      return () => clearInterval(interval)
    }
  }, [isExperimentMode])
  
  const handleProjectClick = (name: string) => {
    setActiveProject(name)
    setIsExperimentMode(true)
    setExperimentProgress(0)
  }
  
  const projects = [
    {
      name: "Veritas",
      tagline: "Affective Cognition Analysis System",
      description: "Quantifies emotional coherence vectors, decision-making alignment, and belief consistency matrices. Constructs neural-affective network graphs of emotional states.",
      icon: <Brain className="w-8 h-8" />,
      color: "#38b6ff",
      stage: "Phase 1: Cognitive Awareness"
    },
    {
      name: "Custos",
      tagline: "Quantum-Encrypted Protection Protocol",
      description: "Implements NIST-compliant encryption with multi-layered neural privacy architecture. Enables parental monitoring with differential privacy guarantees.",
      icon: <Shield className="w-8 h-8" />,
      color: "#37fff1",
      stage: "Phase 2: Security Implementation"
    },
    {
      name: "Vitae",
      tagline: "The Lived Simulation Engine",
      description: "Inner Yubi constructs a parallel world: buying a home, confronting choices, evolving values. A sandbox of alternate selves and decisions.",
      icon: <GamepadIcon className="w-8 h-8" />,
      color: "#a137ff",
      stage: "Stage 3: Imagination"
    },
    {
      name: "Aetheris",
      tagline: "The Multiversal Social Graph",
      description: "Yubi-worlds link across a resonance-based network—emotional multiverses stitched together by values, archetypes, and narrative patterns.",
      icon: <Network className="w-8 h-8" />,
      color: "#ff6b37",
      stage: "Stage 4: Connection"
    },
    {
      name: "Cognita",
      tagline: "The Neuroscience Lab of Selfhood",
      description: "Partnered with leading researchers, this is where Yubi becomes peer-reviewed. Real EEG research, affective computing, and memory modeling.",
      icon: <Microscope className="w-8 h-8" />,
      color: "#ffcc00",
      stage: "Stage 5: Validation"
    },
    {
      name: "Somae",
      tagline: "The Identity Language Model",
      description: "Your personal LLM trained on your life, language, and values. It becomes your emotional twin, projecting who you could be.",
      icon: <Sparkles className="w-8 h-8" />,
      color: "#ff37a6",
      stage: "Stage 6: Expression"
    }
  ]
  
  // Customize experiment visualization based on project
  const renderExperimentVisualization = (project: any) => {
    if (!project) return null;
    
    switch (project.name) {
      case "Veritas":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden backdrop-blur-sm border border-[rgba(56,182,255,0.2)]">
            <div className="h-full w-full flex flex-col p-2 font-mono text-[10px] text-[#38b6ff]">
              {Array.from({ length: Math.min(10, experimentProgress / 10) }).map((_, i) => (
                <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                  {`EmotionalNeuralNet.analyze(subject_${Math.floor(Math.random() * 1000)}, {`}
                  <span style={{ marginLeft: '10px' }}>
                    {`coherence_index: ${(Math.random() * 100).toFixed(2)}%,`}
                  </span>
                  <span style={{ marginLeft: '10px' }}>
                    {`affective_state: "${['positive', 'neutral', 'negative', 'ambivalent'][Math.floor(Math.random() * 4)]}",`}
                  </span>
                  <span style={{ marginLeft: '10px' }}>
                    {`confidence: ${(Math.random() * 100).toFixed(1)}%`}
                  </span>
                  {`});`}
                </div>
              ))}
              
              {experimentProgress > 30 && experimentProgress < 40 && (
                <div className="text-[#ff3737] mt-1">
                  {`ERROR: EmotionalCoherenceMatrix failed to converge after ${Math.floor(Math.random() * 100)} iterations`}
                </div>
              )}
              
              {experimentProgress > 40 && experimentProgress < 50 && (
                <div className="text-[#ffcc00] mt-1">
                  {`WARNING: Belief consistency below threshold (${(Math.random() * 70).toFixed(1)}%)`}
                </div>
              )}
              
              {experimentProgress > 60 && (
                <div className="text-[#37fff1] mt-1">
                  {`SUCCESS: Neural mapping complete. ${Math.floor(Math.random() * 1000)} nodes activated.`}
                </div>
              )}
              
              {experimentProgress > 80 && (
                <div className="text-[#a137ff] mt-1">
                  {`OPTIMIZING: Pruning redundant connections... ${(Math.random() * 100).toFixed(1)}% complete`}
                </div>
              )}
            </div>
          </div>
        )
      
      case "Custos":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden p-2">
            <div className="h-full w-full font-mono text-[10px] text-[#37fff1]">
              {Array.from({ length: Math.min(8, experimentProgress / 12) }).map((_, i) => (
                <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                  {`QuantumEncryption.secure({`}
                  <span style={{ marginLeft: '10px' }}>
                    {`layer: ${Math.floor(Math.random() * 10) + 1},`}
                  </span>
                  <span style={{ marginLeft: '10px' }}>
                    {`algorithm: "${['AES-256', 'RSA-4096', 'ECC-521', 'QUANTUM-RESISTANT'][Math.floor(Math.random() * 4)]}",`}
                  </span>
                  <span style={{ marginLeft: '10px' }}>
                    {`entropy: ${(Math.random() * 100).toFixed(2)}`}
                  </span>
                  {`});`}
                </div>
              ))}
              
              {experimentProgress > 25 && experimentProgress < 35 && (
                <div className="text-[#ff3737] mt-1">
                  {`BREACH ATTEMPT DETECTED: Source IP ${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`}
                </div>
              )}
              
              {experimentProgress > 50 && experimentProgress < 60 && (
                <div className="text-[#ffcc00] mt-1">
                  {`VULNERABILITY SCAN: ${Math.floor(Math.random() * 10)} potential weaknesses identified`}
                </div>
              )}
              
              {experimentProgress > 70 && (
                <div className="text-[#37fff1] mt-1">
                  {`COUNTERMEASURES DEPLOYED: Neural firewall activated`}
                </div>
              )}
              
              {experimentProgress > 90 && (
                <div className="text-[#a137ff] mt-1">
                  {`SYSTEM SECURE: Privacy score ${(Math.random() * 20 + 80).toFixed(1)}%`}
                </div>
              )}
            </div>
          </div>
        )
      
      case "Vitae":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden p-2">
            <div className="relative h-full w-full font-mono text-[10px]">
              <div className="text-[#a137ff]">
                {Array.from({ length: Math.min(6, experimentProgress / 15) }).map((_, i) => (
                  <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                    {`SimulationEngine.createWorld({`}
                    <span style={{ marginLeft: '10px' }}>
                      {`scenario: "${['career_choice', 'relationship', 'life_decision', 'ethical_dilemma'][Math.floor(Math.random() * 4)]}",`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`timeline: "${Math.floor(Math.random() * 10) + 1} years",`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`complexity: ${Math.floor(Math.random() * 5) + 1}`}
                    </span>
                    {`});`}
                  </div>
                ))}
              </div>
              
              {experimentProgress > 40 && (
                <div className="absolute bottom-0 left-0 right-0">
                  <div className="text-[#ff37a6] mb-1">
                    {`// Simulation running: ${experimentProgress}% complete`}
                  </div>
                  <div className="flex space-x-1">
                    {Array.from({ length: 10 }).map((_, i) => (
                      <div 
                        key={i}
                        className="h-2 flex-1 rounded-sm"
                        style={{ 
                          backgroundColor: i < (experimentProgress / 10) 
                            ? `rgba(161,55,255,${0.5 + Math.random() * 0.5})` 
                            : 'rgba(10,17,40,0.3)',
                          transition: 'all 0.3s ease'
                        }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      
      case "Aetheris":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden p-2">
            <div className="relative h-full w-full font-mono text-[10px]">
              <div className="text-[#ff6b37]">
                {Array.from({ length: Math.min(7, experimentProgress / 14) }).map((_, i) => (
                  <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                    {`NetworkGraph.connect(node_${Math.floor(Math.random() * 100)}, {`}
                    <span style={{ marginLeft: '10px' }}>
                      {`resonance: ${(Math.random() * 10).toFixed(2)},`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`strength: ${(Math.random() * 100).toFixed(1)}%,`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`type: "${['emotional', 'cognitive', 'value', 'narrative'][Math.floor(Math.random() * 4)]}"`}
                    </span>
                    {`});`}
                  </div>
                ))}
              </div>
              
              {experimentProgress > 50 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative w-24 h-24">
                    {Array.from({ length: Math.min(20, experimentProgress / 5) }).map((_, i) => {
                      const angle = i * (Math.PI * 2 / 20);
                      const radius = 10 + Math.random() * 10;
                      return (
                        <div 
                          key={i}
                          className="absolute w-1 h-1 rounded-full bg-[#ff6b37]"
                          style={{ 
                            left: `${12 + Math.cos(angle) * radius}px`,
                            top: `${12 + Math.sin(angle) * radius}px`,
                            opacity: Math.random() * 0.7 + 0.3,
                            boxShadow: '0 0 3px #ff6b37'
                          }}
                        />
                      );
                    })}
                    {experimentProgress > 70 && Array.from({ length: Math.min(30, (experimentProgress - 70) / 1) }).map((_, i) => {
                      const source = Math.floor(Math.random() * 20);
                      const target = Math.floor(Math.random() * 20);
                      const sourceAngle = source * (Math.PI * 2 / 20);
                      const targetAngle = target * (Math.PI * 2 / 20);
                      const sourceRadius = 10 + Math.random() * 10;
                      const targetRadius = 10 + Math.random() * 10;
                      return (
                        <div 
                          key={`line-${i}`}
                          className="absolute bg-[rgba(255,107,55,0.3)]"
                          style={{ 
                            height: '1px',
                            width: '1px',
                            left: `${12 + Math.cos(sourceAngle) * sourceRadius}px`,
                            top: `${12 + Math.sin(sourceAngle) * sourceRadius}px`,
                            transform: `rotate(${Math.random() * 360}deg)`,
                          }}
                        />
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      
      case "Cognita":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden p-2">
            <div className="h-full w-full font-mono text-[10px]">
              <div className="text-[#ffcc00]">
                {Array.from({ length: Math.min(5, experimentProgress / 20) }).map((_, i) => (
                  <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                    {`NeuroscienceAPI.analyze({`}
                    <span style={{ marginLeft: '10px' }}>
                      {`subject: "user_${Math.floor(Math.random() * 1000)}",`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`brainwave: "${['alpha', 'beta', 'gamma', 'theta', 'delta'][Math.floor(Math.random() * 5)]}",`}
                    </span>
                    <span style={{ marginLeft: '10px' }}>
                      {`region: "${['prefrontal', 'temporal', 'parietal', 'occipital', 'limbic'][Math.floor(Math.random() * 5)]}"`}
                    </span>
                    {`});`}
                  </div>
                ))}
              </div>
              
              {experimentProgress > 40 && (
                <div className="mt-2">
                  <div className="text-[#38b6ff] mb-1">
                    {`// EEG data visualization`}
                  </div>
                  <div className="flex flex-col space-y-2">
                    {Array.from({ length: 5 }).map((_, i) => {
                      const waveType = ['Alpha', 'Beta', 'Gamma', 'Theta', 'Delta'][i];
                      const frequency = [10, 20, 40, 6, 2][i];
                      return (
                        <div key={i} className="flex items-center">
                          <div className="w-12 text-[#ffcc00]">{waveType}</div>
                          <div className="flex-1 h-1 overflow-hidden">
                            <div 
                              className="h-full"
                              style={{ 
                                background: `linear-gradient(90deg, 
                                  rgba(255,204,0,0) 0%, 
                                  rgba(255,204,0,0.8) ${Math.min(100, experimentProgress)}%, 
                                  rgba(255,204,0,0) 100%)`,
                                width: `${Math.min(100, experimentProgress * 2)}%`,
                                transform: `translateX(${Math.sin(Date.now() / (1000 / frequency)) * 10}px)`,
                                transition: 'transform 0.1s ease'
                              }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )
      
      case "Somae":
        return (
          <div className="h-40 w-full bg-[rgba(56,182,255,0.05)] rounded-lg overflow-hidden p-2">
            <div className="h-full w-full font-mono text-[10px] text-[#ff37a6]">
              {experimentProgress > 10 && (
                <div className="mb-2">
                  {`// Training personal LLM model`}
                  {`\n// Importing user data...`}
                </div>
              )}
              
              {experimentProgress > 20 && Array.from({ length: Math.min(15, (experimentProgress - 20) / 5) }).map((_, i) => (
                <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                  {`[${(experimentProgress / 100).toFixed(2)}] Epoch ${i+1}: `}
                  {`loss=${(0.5 - (i * 0.03)).toFixed(4)}, `}
                  {`accuracy=${(0.5 + (i * 0.03)).toFixed(4)}, `}
                  {`perplexity=${(100 - i * 5).toFixed(2)}`}
                </div>
              ))}
              
              {experimentProgress > 60 && (
                <div className="mt-2">
                  {`// Fine-tuning on emotional patterns`}
                  {`\n// Adjusting weights for personality alignment`}
                </div>
              )}
              
              {experimentProgress > 80 && (
                <div className="mt-2">
                  {`// Model convergence detected`}
                  {`\n// Identity match: ${experimentProgress.toFixed(1)}%`}
                </div>
              )}
              
              {experimentProgress > 90 && (
                <div className="absolute bottom-2 right-2 text-xs animate-pulse">
                  Training complete
                </div>
              )}
            </div>
          </div>
        )
      
      default:
        return null
    }
  }

  // Add this function to generate insights based on experiment results
  const generateExperimentInsights = (projectName: string) => {
    // Base insights for each project type
    const insightsByProject: Record<string, string[]> = {
      "Veritas": [
        "Emotional coherence patterns show strong alignment with the Default Mode Network activity.",
        "Identity markers reveal a 78% correlation between stated values and emotional responses.",
        "Narrative consistency index: 8.4/10 - indicating strong self-continuity across contexts.",
        "Emotional regulation pathways show activation patterns similar to mindfulness practitioners.",
        "Cognitive-emotional integration score: 7.2/10 - suggesting balanced decision-making processes."
      ],
      "Custos": [
        "Privacy protection layers successfully implemented with 99.7% data encapsulation.",
        "Emotional encryption strength: 256-bit equivalent neural patterning.",
        "Parent-accessible insights maintain 94% accuracy while preserving 89% privacy.",
        "Youth safety protocols exceed COPPA compliance by 43% on core metrics.",
        "Boundary recognition systems show 8.7/10 effectiveness in simulated scenarios."
      ],
      "Vitae": [
        "Simulation depth reached 7 layers of decision-consequence mapping.",
        "Value evolution trajectories show 82% consistency with real-world identity formation.",
        "Alternative self-pathways generated 34 viable identity expressions.",
        "Narrative branching complexity: 9.2/10 - exceeding typical imagination constraints.",
        "Temporal projection accuracy: 76% alignment with longitudinal identity studies."
      ],
      "Aetheris": [
        "Network resonance strength peaked at 8.7 on the parasocial connection index.",
        "Emotional multiverses successfully linked across 47 value-based nodes.",
        "Archetypal pattern recognition achieved 91% accuracy in cross-narrative mapping.",
        "Connection density: 3.4x higher than typical social graph implementations.",
        "Resonance-based filtering shows 87% effectiveness in meaningful connection formation."
      ],
      "Cognita": [
        "EEG correlation patterns match published findings with 94% accuracy.",
        "Affective computing models validated against 7 peer-reviewed studies.",
        "Memory encoding patterns show distinctive neural signatures across 5 brain regions.",
        "Research value assessment: 8.9/10 from simulated peer review.",
        "Neuroscientific validity index: 82% alignment with current literature."
      ],
      "Somae": [
        "Identity language model achieved 97% personality pattern recognition.",
        "Emotional twin projection accuracy: 8.8/10 on core identity markers.",
        "Language-value alignment shows 89% consistency across expression contexts.",
        "Self-projection accuracy: 76% alignment with stated aspirational identity.",
        "Linguistic pattern matching exceeds baseline models by 43% on identity metrics."
      ]
    };
    
    // Select 2-3 random insights for the given project
    const projectInsights = insightsByProject[projectName] || [];
    const shuffled = [...projectInsights].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.floor(Math.random() * 2) + 2); // Return 2-3 insights
  };

  // Update the experiment results section
  const renderExperimentResults = () => {
    if (!activeProject || experimentProgress < 100) return null
    
    const project = projects.find(p => p.name === activeProject)
    if (!project) return null
    
    const resultData = getExperimentResults(project.name)
    const insights = generateExperimentInsights(project.name)
    
    return (
      <div className="mt-6 p-4 rounded-lg bg-[rgba(56,182,255,0.05)] border border-[rgba(56,182,255,0.2)] backdrop-blur-sm">
        <h3 className="text-lg font-medium mb-4 flex items-center" style={{ color: project.color }}>
          <Microscope className="w-5 h-5 mr-2" />
          Experimental Analysis
        </h3>
        
        <div className="grid grid-cols-3 gap-4 mb-6">
          {Array.isArray(resultData.metrics) 
            ? resultData.metrics.map((metric: any, index: number) => (
              <div key={index} className="bg-[rgba(10,17,40,0.5)] p-3 rounded-md border border-[rgba(56,182,255,0.1)] backdrop-blur-sm hover:border-[rgba(56,182,255,0.3)] transition-all duration-300">
                <div className="text-xs text-gray-400 mb-1 font-mono tracking-wider">{metric.name}</div>
                <div className="text-xl font-light flex items-center" style={{ color: project.color }}>
                  {metric.value}
                  <div className="ml-2 h-3 w-3 rounded-full animate-pulse" style={{ backgroundColor: project.color, opacity: 0.6 }}></div>
                </div>
                <div className="mt-1 h-[2px] w-full overflow-hidden">
                  <div className="h-full shimmer-animation" style={{ background: `linear-gradient(90deg, transparent, ${project.color}, transparent)` }}></div>
                </div>
              </div>
            ))
            : (resultData.metrics as any).metrics.map((metric: any, index: number) => (
              <div key={index} className="bg-[rgba(10,17,40,0.5)] p-3 rounded-md border border-[rgba(56,182,255,0.1)] backdrop-blur-sm hover:border-[rgba(56,182,255,0.3)] transition-all duration-300">
                <div className="text-xs text-gray-400 mb-1 font-mono tracking-wider">{metric.name}</div>
                <div className="text-xl font-light flex items-center" style={{ color: project.color }}>
                  {metric.value}
                  <div className="ml-2 h-3 w-3 rounded-full animate-pulse" style={{ backgroundColor: project.color, opacity: 0.6 }}></div>
                </div>
                <div className="mt-1 h-[2px] w-full overflow-hidden">
                  <div className="h-full shimmer-animation" style={{ background: `linear-gradient(90deg, transparent, ${project.color}, transparent)` }}></div>
                </div>
              </div>
            ))}
        </div>
        
        <div className="mb-6">
          <h4 className="text-sm text-gray-300 mb-3 flex items-center">
            <FlaskConical className="w-4 h-4 mr-2" style={{ color: project.color }} />
            <span className="font-mono tracking-wider">ANALYSIS REPORT:</span>
          </h4>
          <ul className="space-y-2">
            {insights.map((insight, index) => (
              <li key={index} className="flex items-start gap-2 bg-[rgba(10,17,40,0.3)] p-3 rounded-md border-l-2 backdrop-blur-sm" style={{ borderColor: project.color }}>
                <div className="mt-1" style={{ color: project.color }}>
                  <ChevronRight className="w-4 h-4" />
                </div>
                <div className="text-sm text-gray-200 font-mono relative">
                  {insight}
                  <div className="absolute -left-1 top-0 bottom-0 w-[1px]" style={{ background: `linear-gradient(to bottom, transparent, ${project.color}, transparent)` }}></div>
                </div>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="flex justify-center">
          <button 
            onClick={() => openInsightsModal(project.name)}
            className="flex items-center gap-2 px-6 py-2 rounded-md text-black font-medium backdrop-blur-sm relative overflow-hidden group"
            style={{ 
              background: `linear-gradient(to right, ${project.color}, rgba(56,182,255,0.8))`,
              boxShadow: `0 0 15px rgba(56,182,255,0.3)`
            }}
          >
            <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            <div className="absolute left-0 top-0 bottom-0 w-[1px] bg-white opacity-50"></div>
            <div className="absolute right-0 top-0 bottom-0 w-[1px] bg-white opacity-50"></div>
            <ExternalLink className="w-4 h-4" />
            <span className="font-mono tracking-wider">{resultData.action}</span>
            <div className="absolute -right-4 top-0 bottom-0 w-8 bg-white opacity-10 transform rotate-12 translate-x-2 group-hover:translate-x-12 transition-transform duration-500"></div>
          </button>
        </div>
      </div>
    )
  }

  // Update the experiment view
  const renderExperimentView = () => {
    return (
      <motion.div
        key="experiment"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="max-w-3xl mx-auto"
      >
        <div className="p-8 rounded-lg bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border border-[rgba(56,182,255,0.3)]">
          <div className="flex justify-between items-center mb-6">
            <div>
              <div className="text-xs text-gray-400 mb-1">
                {projects.find(p => p.name === activeProject)?.stage}
              </div>
              <h2 className="text-2xl font-light" style={{ color: projects.find(p => p.name === activeProject)?.color }}>
                {activeProject} Experiment
              </h2>
            </div>
            <button 
              onClick={resetExperiment}
              className="flex items-center gap-1 px-3 py-1 rounded-md bg-[rgba(56,182,255,0.1)] text-[#38b6ff] hover:bg-[rgba(56,182,255,0.2)] transition-colors"
            >
              <RotateCw className="w-4 h-4" />
              <span>Reset</span>
            </button>
          </div>
          
          <div className="mb-6">
            <div className="h-2 w-full bg-[rgba(56,182,255,0.1)] rounded-full overflow-hidden">
              <div 
                className="h-full transition-all duration-500 ease-out"
                style={{ 
                  width: `${experimentProgress}%`,
                  background: `linear-gradient(to right, ${projects.find(p => p.name === activeProject)?.color}, rgba(56,182,255,0.8))`
                }}
              />
            </div>
            <div className="flex justify-between mt-1 text-xs text-gray-400">
              <span>Experiment Progress</span>
              <span>{experimentProgress}%</span>
            </div>
          </div>
          
          {/* Experiment visualization */}
          <div className="mb-6">
            <div className="flex justify-between mb-1 text-xs text-gray-400 font-mono">
              <span>QUANTUM NEURAL ANALYSIS</span>
              <span className="text-[#38b6ff]">{experimentProgress}%</span>
            </div>
            <div className="h-3 w-full bg-[rgba(10,14,36,0.8)] rounded-full overflow-hidden relative border border-[rgba(56,182,255,0.2)]">
              <div 
                className="h-full transition-all duration-500 ease-out relative"
                style={{ 
                  width: `${experimentProgress}%`,
                  background: `linear-gradient(to right, ${projects.find(p => p.name === activeProject)?.color}, rgba(56,182,255,0.8))`
                }}
              >
                {/* Add neural-like pulses along the progress bar */}
                {Array.from({ length: 5 }).map((_, i) => (
                  <div 
                    key={i}
                    className="absolute top-0 bottom-0 w-1 rounded-full bg-white animate-pulse"
                    style={{ 
                      left: `${i * 20}%`,
                      opacity: 0.7,
                      animationDelay: `${i * 0.2}s`
                    }}
                  />
                ))}
              </div>
              
              {/* Add tick marks */}
              {[0, 20, 40, 60, 80, 100].map(tick => (
                <div 
                  key={tick} 
                  className="absolute top-0 bottom-0 w-[1px] bg-[rgba(56,182,255,0.3)]"
                  style={{ left: `${tick}%` }}
                />
              ))}
            </div>
            <div className="flex justify-between mt-1 text-[10px] text-[#38b6ff] font-mono">
              <span>INIT</span>
              <span>MAPPING</span>
              <span>TRAINING</span>
              <span>ANALYZING</span>
              <span>OPTIMIZING</span>
              <span>COMPLETE</span>
            </div>
          </div>
          
          {/* Add the new terminal display component */}
          <div className="mb-6">
            <div className="flex justify-between mb-1 text-xs text-gray-400 font-mono">
              <span>NEURAL TERMINAL</span>
              <span className="text-[#38b6ff]">SESSION {Math.floor(Math.random() * 10000)}</span>
            </div>
            <TerminalDisplay projectName={activeProject || ""} experimentProgress={experimentProgress} />
          </div>
          
          {/* Experiment results */}
          {renderExperimentResults()}
          
          {experimentProgress < 100 && (
            <div className="flex justify-center mt-6">
              <button 
                onClick={runExperiment}
                className="px-6 py-3 rounded-md text-black font-medium relative overflow-hidden group"
                style={{ 
                  background: `linear-gradient(to right, ${projects.find(p => p.name === activeProject)?.color}, rgba(56,182,255,0.8))`,
                  boxShadow: `0 0 20px ${projects.find(p => p.name === activeProject)?.color}40`
                }}
              >
                <span className="relative z-10">Run Experiment</span>
                <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </button>
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  // Update the main project grid with a neural network inspired layout
  const renderProjectGrid = () => {
    return (
      <motion.div
        key="projects"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="relative"
      >
        {/* Neural connection lines between projects */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {projects.slice(0, visibleProjects).map((project, i) => 
            projects.slice(0, visibleProjects).map((target, j) => {
              if (i >= j) return null;
              return (
                <div 
                  key={`${project.name}-${target.name}`}
                  className="absolute h-[1px] origin-left"
                  style={{
                    left: `${(i % 3) * 33 + 16}%`,
                    top: `${Math.floor(i / 3) * 280 + 140}px`,
                    width: `${Math.sqrt(Math.pow((j % 3 - i % 3) * 33, 2) + Math.pow((Math.floor(j / 3) - Math.floor(i / 3)) * 280, 2))}px`,
                    transform: `rotate(${Math.atan2((Math.floor(j / 3) - Math.floor(i / 3)) * 280, (j % 3 - i % 3) * 33) * (180 / Math.PI)}deg)`,
                    background: `linear-gradient(90deg, ${project.color}40, ${target.color}40)`,
                    opacity: 0.3
                  }}
                >
                  {/* Animated data pulse */}
                  <div 
                    className="absolute h-1.5 w-1.5 rounded-full animate-pulse"
                    style={{
                      backgroundColor: 'white',
                      left: `${Math.random() * 100}%`,
                      top: '-3px',
                      animationDuration: `${2 + Math.random() * 3}s`,
                      opacity: 0.7
                    }}
                  />
                </div>
              );
            })
          )}
        </div>
        
        {/* Neural network nodes (projects) */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 relative z-10">
          {projects.slice(0, visibleProjects).map((project, index) => (
            <motion.div
              key={project.name}
              ref={el => {
                projectRefs.current[index] = el;
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative p-6 rounded-lg bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border border-[rgba(56,182,255,0.1)] hover:border-[rgba(56,182,255,0.3)] transition-all cursor-pointer group"
              onClick={() => startExperiment(project.name)}
              whileHover={{ scale: 1.02 }}
            >
              {/* Neural node pulse effect */}
              <div 
                className="absolute -inset-1 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-500"
                style={{ 
                  background: `radial-gradient(circle, ${project.color}, transparent 70%)`,
                  animation: 'pulse 2s infinite'
                }}
              />
              
              <div className="absolute top-0 left-0 w-full h-1 rounded-t-lg" style={{ backgroundColor: project.color }} />
              
              <div className="text-xs text-gray-400 mb-1 font-mono">{project.stage}</div>
              <h3 className="text-xl mb-2 group-hover:text-white transition-colors flex items-center" style={{ color: project.color }}>
                {project.name}
                <div className="ml-2 opacity-50">
                  <Microscope className="w-4 h-4" />
                </div>
              </h3>
              <p className="text-sm text-gray-300 mb-4 font-medium">{project.tagline}</p>
              <p className="text-xs text-gray-400 mb-6">{project.description}</p>
              
              <div className="flex justify-between items-center">
                <div className="p-2 rounded-full relative" style={{ 
                  backgroundColor: `${project.color}20`,
                  boxShadow: `0 0 10px ${project.color}40`
                }}>
                  {project.icon}
                  {/* Neural activity indicator */}
                  <div className="absolute -right-1 -top-1 h-2 w-2 rounded-full animate-ping" style={{ backgroundColor: project.color }} />
                </div>
                <button 
                  className="flex items-center gap-1 px-3 py-1 rounded-md bg-[rgba(56,182,255,0.1)] text-[#38b6ff] hover:bg-[rgba(56,182,255,0.2)] transition-colors backdrop-blur-sm"
                >
                  <FlaskConical className="w-4 h-4" />
                  <span className="font-mono">ACTIVATE</span>
                </button>
              </div>
              
              {/* Neural connection points */}
              <div className="absolute left-0 top-1/2 w-1 h-1 rounded-full" style={{ backgroundColor: project.color, transform: 'translateY(-50%)' }} />
              <div className="absolute right-0 top-1/2 w-1 h-1 rounded-full" style={{ backgroundColor: project.color, transform: 'translateY(-50%)' }} />
              <div className="absolute left-1/2 top-0 w-1 h-1 rounded-full" style={{ backgroundColor: project.color, transform: 'translateX(-50%)' }} />
              <div className="absolute left-1/2 bottom-0 w-1 h-1 rounded-full" style={{ backgroundColor: project.color, transform: 'translateX(-50%)' }} />
            </motion.div>
          ))}
        </div>
        
        {/* Neural network stats */}
        <div className="mt-8 p-4 rounded-lg bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border border-[rgba(56,182,255,0.1)]">
          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-400 font-mono">NEURAL NETWORK STATUS: <span className="text-[#38b6ff]">ACTIVE</span></div>
            <div className="text-xs text-gray-400 font-mono">NODES: <span className="text-[#38b6ff]">{visibleProjects}/{projects.length}</span></div>
          </div>
          <div className="mt-2 h-1 w-full bg-[rgba(56,182,255,0.1)] rounded-full overflow-hidden">
            <div 
              className="h-full transition-all duration-500 ease-out"
              style={{ 
                width: `${(visibleProjects / projects.length) * 100}%`,
                background: 'linear-gradient(to right, #38b6ff, #a137ff)'
              }}
            />
          </div>
        </div>
      </motion.div>
    )
  }

  // Add these functions for experiment functionality
  const startExperiment = (projectName: string) => {
    setActiveProject(projectName)
    setIsExperimentMode(true)
    setExperimentProgress(0)
  }

  const runExperiment = () => {
    // Simulate experiment progress
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.floor(Math.random() * 5) + 1
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
      }
      setExperimentProgress(progress)
    }, 100)
  }

  const resetExperiment = () => {
    setIsExperimentMode(false)
    setExperimentProgress(0)
  }

  // Render the insights modal
  const renderInsightsModal = () => {
    if (!modalContent) return null
    
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-70">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="relative max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg bg-[rgba(10,17,40,0.95)] backdrop-blur-lg border border-[rgba(56,182,255,0.3)]"
        >
          {/* Header */}
          <div className="sticky top-0 z-10 flex justify-between items-center p-6 border-b border-[rgba(56,182,255,0.2)] bg-[rgba(10,17,40,0.95)]">
            <h2 className="text-2xl font-light" style={{ color: modalContent.color }}>
              {modalContent.title}
            </h2>
            <button 
              onClick={() => setShowInsightsModal(false)}
              className="p-2 rounded-full hover:bg-[rgba(56,182,255,0.1)]"
            >
              <X className="w-6 h-6 text-gray-400" />
            </button>
          </div>
          
          {/* Content */}
          <div className="p-6">
            {/* Metrics */}
            <div className="mb-8">
              <h3 className="text-lg text-gray-300 mb-4">Primary Metrics</h3>
              <div className="grid grid-cols-3 gap-4">
                {modalContent.metrics.map((metric, index) => (
                  <div key={index} className="bg-[rgba(10,17,40,0.5)] p-4 rounded-md">
                    <div className="text-sm text-gray-400 mb-1">{metric.name}</div>
                    <div className="text-2xl font-light" style={{ color: modalContent.color }}>{metric.value}</div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Derived Metrics */}
            {getExperimentResults(modalContent.visualizationType).derivedMetrics.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg text-gray-300 mb-4">Derived Metrics</h3>
                <div className="grid grid-cols-2 gap-4">
                  {getExperimentResults(modalContent.visualizationType).derivedMetrics.map((metric, index) => (
                    <div key={index} className="bg-[rgba(56,182,255,0.05)] p-4 rounded-md">
                      <div className="text-sm text-gray-400 mb-1">{metric.name}</div>
                      <div className="text-xl font-light" style={{ color: modalContent.color }}>{metric.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Insights */}
            <div className="mb-8">
              <h3 className="text-lg text-gray-300 mb-4">Key Insights</h3>
              <div className="space-y-4">
                {modalContent.insights.map((insight, index) => (
                  <div key={index} className="p-4 rounded-md bg-[rgba(56,182,255,0.05)] border-l-2" style={{ borderColor: modalContent.color }}>
                    <p className="text-gray-200">{insight}</p>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Visualization */}
            <div className="mb-8">
              <h3 className="text-lg text-gray-300 mb-4">Visualization</h3>
              <div className="h-[300px] bg-[rgba(56,182,255,0.05)] rounded-lg p-4 flex items-center justify-center">
                {renderVisualization(modalContent.visualizationType)}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    )
  }

  // Function to render different visualizations based on project type
  const renderVisualization = (projectType: string) => {
    switch (projectType) {
      case "Veritas":
        return <EmotionalMapVisualization />
      case "Custos":
        return <PrivacyLayersVisualization />
      case "Vitae":
        return <SimulationPathsVisualization />
      case "Aetheris":
        return <ConnectionNetworkVisualization />
      case "Cognita":
        return <NeuralActivityVisualization />
      case "Somae":
        return <IdentityMatchVisualization />
      default:
        return <div className="text-gray-400">Visualization not available</div>
    }
  }

  // Placeholder visualization components
  const EmotionalMapVisualization = () => {
    // Create refs for animation
    const mapRef = useRef<HTMLDivElement>(null);
    const [isVisible, setIsVisible] = useState(false);
    
    // Set up animation on mount
    useEffect(() => {
      setIsVisible(true);
      
      // Set up GSAP animations for nodes
      if (mapRef.current) {
        const nodes = mapRef.current.querySelectorAll('.emotion-node');
        
        gsap.fromTo(nodes, 
          { scale: 0, opacity: 0 },
          { 
            scale: 1, 
            opacity: 1, 
            duration: 0.8, 
            stagger: 0.05, 
            ease: "elastic.out(1, 0.5)" 
          }
        );
        
        // Animate connections
        const connections = mapRef.current.querySelectorAll('.emotion-connection');
        gsap.fromTo(connections,
          { width: 0, opacity: 0 },
          { 
            width: "100%", 
            opacity: 0.6, 
            duration: 1.2, 
            stagger: 0.03, 
            ease: "power2.out",
            delay: 0.5
          }
        );
      }
    }, []);
    
    // Define emotion colors
    const emotionColors = {
      "joy": "#FFD700",        // Gold
      "sadness": "#4169E1",    // Royal Blue
      "anger": "#FF4500",      // Red Orange
      "fear": "#800080",       // Purple
      "surprise": "#00FFFF",   // Cyan
      "trust": "#32CD32",      // Lime Green
      "anticipation": "#FFA500", // Orange
      "disgust": "#8B4513",    // Saddle Brown
      "neutral": "#CCCCCC",    // Gray
      "confusion": "#9370DB",  // Medium Purple
      "fatigue": "#708090"     // Slate Gray
    };
    
    // Define emotion nodes with positions and strengths
    const emotionNodes = [
      { id: 1, emotion: "joy", x: 25, y: 20, strength: 0.8, connections: [2, 7, 9] },
      { id: 2, emotion: "trust", x: 40, y: 30, strength: 0.9, connections: [1, 3, 8] },
      { id: 3, emotion: "anticipation", x: 60, y: 25, strength: 0.7, connections: [2, 4] },
      { id: 4, emotion: "surprise", x: 75, y: 35, strength: 0.5, connections: [3, 5] },
      { id: 5, emotion: "fear", x: 80, y: 60, strength: 0.4, connections: [4, 6] },
      { id: 6, emotion: "sadness", x: 65, y: 75, strength: 0.6, connections: [5, 7, 11] },
      { id: 7, emotion: "disgust", x: 40, y: 70, strength: 0.3, connections: [6, 8] },
      { id: 8, emotion: "anger", x: 25, y: 55, strength: 0.5, connections: [7, 9] },
      { id: 9, emotion: "neutral", x: 50, y: 50, strength: 0.7, connections: [1, 2, 8, 10] },
      { id: 10, emotion: "confusion", x: 35, y: 45, strength: 0.4, connections: [9] },
      { id: 11, emotion: "fatigue", x: 55, y: 65, strength: 0.3, connections: [6] }
    ];
    
    return (
      <div ref={mapRef} className="w-full h-full relative bg-[rgba(10,14,36,0.7)] rounded-lg p-4 overflow-hidden">
        {/* Background grid */}
        <div className="absolute inset-0 grid grid-cols-10 grid-rows-10">
          {Array.from({ length: 100 }).map((_, i) => (
            <div 
              key={`grid-${i}`} 
              className="border border-[rgba(56,182,255,0.05)]"
            />
          ))}
        </div>
        
        {/* Connections between emotions */}
        {emotionNodes.map(node => (
          node.connections.map(targetId => {
            const targetNode = emotionNodes.find(n => n.id === targetId);
            if (!targetNode) return null;
            
            // Calculate line properties
            const x1 = node.x;
            const y1 = node.y;
            const x2 = targetNode.x;
            const y2 = targetNode.y;
            const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
            const angle = Math.atan2(y2 - y1, x2 - x1) * (180 / Math.PI);
            
            // Calculate connection strength (average of both nodes)
            const strength = (node.strength + targetNode.strength) / 2;
            
            return (
              <div 
                key={`conn-${node.id}-${targetId}`}
                className="absolute emotion-connection"
                style={{
                  left: `${x1}%`,
                  top: `${y1}%`,
                  width: `${length}%`,
                  height: '2px',
                  opacity: strength * 0.6,
                  background: `linear-gradient(to right, ${emotionColors[node.emotion as keyof typeof emotionColors]}, ${emotionColors[targetNode.emotion as keyof typeof emotionColors]})`,
                  transform: `rotate(${angle}deg)`,
                  transformOrigin: 'left center',
                  boxShadow: `0 0 5px ${emotionColors[node.emotion as keyof typeof emotionColors]}`,
                }}
              />
            );
          })
        )).flat().filter(Boolean)}
        
        {/* Emotion nodes */}
        {emotionNodes.map(node => (
          <div 
            key={`node-${node.id}`}
            className="absolute emotion-node flex items-center justify-center rounded-full cursor-pointer transition-all duration-300 hover:scale-110"
            style={{
              left: `${node.x}%`,
              top: `${node.y}%`,
              width: `${node.strength * 40 + 20}px`,
              height: `${node.strength * 40 + 20}px`,
              backgroundColor: `${emotionColors[node.emotion as keyof typeof emotionColors]}20`,
              border: `2px solid ${emotionColors[node.emotion as keyof typeof emotionColors]}`,
              boxShadow: `0 0 10px ${emotionColors[node.emotion as keyof typeof emotionColors]}`,
              transform: 'translate(-50%, -50%)',
              opacity: isVisible ? 1 : 0,
              zIndex: 10
            }}
          >
            <div className="text-xs font-medium" style={{ color: emotionColors[node.emotion as keyof typeof emotionColors] }}>
              {node.emotion}
            </div>
          </div>
        ))}
        
        {/* Pulse animations */}
        {emotionNodes.filter(node => node.strength > 0.6).map(node => (
          <div 
            key={`pulse-${node.id}`}
            className="absolute rounded-full animate-ping"
            style={{
              left: `${node.x}%`,
              top: `${node.y}%`,
              width: `${node.strength * 40 + 20}px`,
              height: `${node.strength * 40 + 20}px`,
              backgroundColor: 'transparent',
              border: `2px solid ${emotionColors[node.emotion as keyof typeof emotionColors]}`,
              transform: 'translate(-50%, -50%)',
              opacity: 0,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          />
        ))}
        
        {/* Central user indicator */}
        <div className="absolute left-1/2 top-1/2 w-12 h-12 rounded-full bg-[rgba(56,182,255,0.2)] border-2 border-[#38b6ff] flex items-center justify-center transform -translate-x-1/2 -translate-y-1/2 z-20">
          <div className="text-[#38b6ff] text-xs">SELF</div>
        </div>
        
        {/* Data points flowing through connections */}
        {Array.from({ length: 15 }).map((_, i) => {
          const sourceNode = emotionNodes[Math.floor(Math.random() * emotionNodes.length)];
          const targetNode = emotionNodes.find(n => sourceNode.connections.includes(n.id));
          if (!targetNode) return null;
          
          return (
            <div 
              key={`data-point-${i}`}
              className="absolute w-2 h-2 rounded-full bg-white z-5"
              style={{
                left: `${sourceNode.x}%`,
                top: `${sourceNode.y}%`,
                transform: 'translate(-50%, -50%)',
                animation: `dataFlow${i} ${2 + Math.random() * 3}s infinite linear`,
                animationDelay: `${Math.random() * 5}s`,
                opacity: 0.7,
              }}
            />
          );
        })}
        
        {/* Legend */}
        <div className="absolute bottom-2 left-2 text-xs text-white bg-[rgba(0,0,0,0.3)] p-1 rounded">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-[rgba(56,182,255,0.5)] border border-[#38b6ff] mr-1"></div>
            <span>Self</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-[rgba(255,215,0,0.5)] border border-[#FFD700] mr-1"></div>
            <span>Emotion Node</span>
          </div>
          <div className="flex items-center">
            <div className="w-6 h-1 bg-gradient-to-r from-[#38b6ff] to-[#FFD700] mr-1"></div>
            <span>Connection</span>
          </div>
        </div>
        
        <div className="absolute top-2 right-2 text-white text-opacity-70 text-sm font-medium">
          Emotional Coherence Map
        </div>
        
        {/* Add data flow animations */}
        <style jsx>{`
          @keyframes dataFlow0 { 0% { left: ${emotionNodes[0].x}%; top: ${emotionNodes[0].y}%; } 100% { left: ${emotionNodes[1].x}%; top: ${emotionNodes[1].y}%; } }
          @keyframes dataFlow1 { 0% { left: ${emotionNodes[1].x}%; top: ${emotionNodes[1].y}%; } 100% { left: ${emotionNodes[2].x}%; top: ${emotionNodes[2].y}%; } }
          @keyframes dataFlow2 { 0% { left: ${emotionNodes[2].x}%; top: ${emotionNodes[2].y}%; } 100% { left: ${emotionNodes[3].x}%; top: ${emotionNodes[3].y}%; } }
          @keyframes dataFlow3 { 0% { left: ${emotionNodes[3].x}%; top: ${emotionNodes[3].y}%; } 100% { left: ${emotionNodes[4].x}%; top: ${emotionNodes[4].y}%; } }
          @keyframes dataFlow4 { 0% { left: ${emotionNodes[4].x}%; top: ${emotionNodes[4].y}%; } 100% { left: ${emotionNodes[5].x}%; top: ${emotionNodes[5].y}%; } }
          @keyframes dataFlow5 { 0% { left: ${emotionNodes[5].x}%; top: ${emotionNodes[5].y}%; } 100% { left: ${emotionNodes[6].x}%; top: ${emotionNodes[6].y}%; } }
          @keyframes dataFlow6 { 0% { left: ${emotionNodes[6].x}%; top: ${emotionNodes[6].y}%; } 100% { left: ${emotionNodes[7].x}%; top: ${emotionNodes[7].y}%; } }
          @keyframes dataFlow7 { 0% { left: ${emotionNodes[7].x}%; top: ${emotionNodes[7].y}%; } 100% { left: ${emotionNodes[8].x}%; top: ${emotionNodes[8].y}%; } }
          @keyframes dataFlow8 { 0% { left: ${emotionNodes[8].x}%; top: ${emotionNodes[8].y}%; } 100% { left: ${emotionNodes[9].x}%; top: ${emotionNodes[9].y}%; } }
          @keyframes dataFlow9 { 0% { left: ${emotionNodes[9].x}%; top: ${emotionNodes[9].y}%; } 100% { left: ${emotionNodes[10].x}%; top: ${emotionNodes[10].y}%; } }
          @keyframes dataFlow10 { 0% { left: ${emotionNodes[10].x}%; top: ${emotionNodes[10].y}%; } 100% { left: ${emotionNodes[6].x}%; top: ${emotionNodes[6].y}%; } }
          @keyframes dataFlow11 { 0% { left: ${emotionNodes[1].x}%; top: ${emotionNodes[1].y}%; } 100% { left: ${emotionNodes[8].x}%; top: ${emotionNodes[8].y}%; } }
          @keyframes dataFlow12 { 0% { left: ${emotionNodes[2].x}%; top: ${emotionNodes[2].y}%; } 100% { left: ${emotionNodes[9].x}%; top: ${emotionNodes[9].y}%; } }
          @keyframes dataFlow13 { 0% { left: ${emotionNodes[3].x}%; top: ${emotionNodes[3].y}%; } 100% { left: ${emotionNodes[0].x}%; top: ${emotionNodes[0].y}%; } }
          @keyframes dataFlow14 { 0% { left: ${emotionNodes[4].x}%; top: ${emotionNodes[4].y}%; } 100% { left: ${emotionNodes[1].x}%; top: ${emotionNodes[1].y}%; } }
        `}</style>
      </div>
    );
  };

  // Add similar placeholder visualizations for other project types
  const PrivacyLayersVisualization = () => {
    const layers = [
      { name: "Quantum Encryption", strength: 95 },
      { name: "Neural Firewall", strength: 88 },
      { name: "Biometric Authentication", strength: 92 },
      { name: "Zero-Knowledge Proofs", strength: 85 },
      { name: "Homomorphic Encryption", strength: 78 }
    ];
    
    return (
      <div className="w-full h-full flex flex-col">
        <div className="mb-4 text-center text-sm text-gray-300">Privacy Protection Layers</div>
        <div className="flex-1 flex flex-col justify-center space-y-4">
          {layers.map((layer, index) => (
            <div key={index} className="relative">
              <div className="flex justify-between mb-1">
                <span className="text-xs font-mono text-gray-300">{layer.name}</span>
                <span className="text-xs font-mono text-[#37fff1]">{layer.strength}%</span>
              </div>
              <div className="h-3 bg-[rgba(10,14,36,0.8)] rounded-full overflow-hidden border border-[rgba(56,182,255,0.2)]">
                <div 
                  className="h-full transition-all duration-500 ease-out"
                  style={{ 
                    width: `${layer.strength}%`,
                    background: `linear-gradient(to right, #37fff1, rgba(56,182,255,0.8))`
                  }}
                >
                  {/* Add neural-like pulses along the bar */}
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div 
                      key={i}
                      className="absolute top-0 bottom-0 w-1 rounded-full bg-white animate-pulse"
                      style={{ 
                        left: `${(layer.strength / 3) * (i + 1)}%`,
                        opacity: 0.7,
                        animationDelay: `${i * 0.2}s`
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const SimulationPathsVisualization = () => {
    const [selectedPath, setSelectedPath] = useState(0);
    const simulationPaths = [
      { 
        name: "Path Alpha", 
        satisfaction: 87,
        decisions: ["Career Change", "Relationship Growth", "Creative Pursuit"],
        outcomes: ["Financial Stability", "Emotional Fulfillment", "Self-Expression"]
      },
      { 
        name: "Path Beta", 
        satisfaction: 72,
        decisions: ["Status Quo", "Social Expansion", "Knowledge Pursuit"],
        outcomes: ["Security", "Community Connection", "Intellectual Growth"]
      },
      { 
        name: "Path Gamma", 
        satisfaction: 93,
        decisions: ["Adventure", "Deep Connection", "Skill Mastery"],
        outcomes: ["Novel Experiences", "Profound Relationship", "Excellence"]
      }
    ];
    
    return (
      <div className="relative w-full h-full p-4 flex flex-col">
        {/* Path selector */}
        <div className="flex space-x-2 mb-4">
          {simulationPaths.map((path, index) => (
            <button
              key={index}
              className={`px-3 py-1 text-xs rounded-full transition-all ${
                selectedPath === index 
                  ? 'bg-[#00cc4e] text-black' 
                  : 'bg-[rgba(0,204,78,0.2)] text-[#00cc4e]'
              }`}
              onClick={() => setSelectedPath(index)}
            >
              {path.name}
            </button>
          ))}
        </div>
        
        {/* Path details */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <div className="text-sm font-medium text-[#00cc4e]">
              {simulationPaths[selectedPath].name}
            </div>
            <div className="text-xs bg-[rgba(0,204,78,0.2)] px-2 py-1 rounded-full">
              Satisfaction: {simulationPaths[selectedPath].satisfaction}%
            </div>
          </div>
          
          {/* Path visualization - FIXED to stay within bounds */}
          <div className="relative h-24 mb-4 overflow-hidden">
            <div className="absolute left-3 top-1/2 w-3 h-3 rounded-full bg-[#00cc4e] z-10 transform -translate-y-1/2"></div>
            <div className="absolute right-3 top-1/2 w-3 h-3 rounded-full bg-[#00cc4e] z-10 transform -translate-y-1/2"></div>
            <div className="absolute left-1/2 top-3 w-3 h-3 rounded-full bg-[#00cc4e] z-10 transform -translate-x-1/2"></div>
            <div className="absolute left-1/2 bottom-3 w-3 h-3 rounded-full bg-[#00cc4e] z-10 transform -translate-x-1/2"></div>
            
            {/* Horizontal line */}
            <div className="absolute left-3 right-3 top-1/2 h-1 bg-[rgba(0,204,78,0.3)] z-0 transform -translate-y-1/2"></div>
            
            {/* Vertical line */}
            <div className="absolute top-3 bottom-3 left-1/2 w-1 bg-[rgba(0,204,78,0.3)] z-0 transform -translate-x-1/2"></div>
            
            {/* Animated particle on the path */}
            <div className="absolute w-2 h-2 rounded-full bg-white z-20 animate-pulse"
                 style={{
                   left: `${3 + Math.random() * (100 - 6)}%`,
                   top: `${3 + Math.random() * (100 - 6)}%`,
                   boxShadow: '0 0 8px #00cc4e'
                 }}></div>
          </div>
        </div>
        
        {/* Decision and outcome lists */}
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <div className="text-[#00cc4e] mb-2">Key Decisions</div>
            <ul className="space-y-1">
              {simulationPaths[selectedPath].decisions.map((decision, i) => (
                <li key={i} className="flex items-center">
                  <span className="w-1 h-1 bg-[#00cc4e] rounded-full mr-2"></span>
                  {decision}
                </li>
              ))}
            </ul>
          </div>
          <div>
            <div className="text-[#00cc4e] mb-2">Outcomes</div>
            <ul className="space-y-1">
              {simulationPaths[selectedPath].outcomes.map((outcome, i) => (
                <li key={i} className="flex items-center">
                  <span className="w-1 h-1 bg-[#00cc4e] rounded-full mr-2"></span>
                  {outcome}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  };

  const ConnectionNetworkVisualization = () => {
    const [selectedNode, setSelectedNode] = useState(0);
    const nodes = [
      { 
        name: "Node A", 
        connections: ["Node B", "Node C", "Node D"],
        description: "This node represents the initial connection point."
      },
      { 
        name: "Node B", 
        connections: ["Node A", "Node C"],
        description: "This node is connected to Node A and Node C."
      },
      { 
        name: "Node C", 
        connections: ["Node A", "Node B", "Node D"],
        description: "This node is connected to Node A, Node B, and Node D."
      },
      { 
        name: "Node D", 
        connections: ["Node A", "Node C"],
        description: "This node is connected to Node A and Node C."
      }
    ];
    
    return (
      <div className="w-full h-full flex flex-col">
        <div className="mb-4 text-center text-sm text-gray-300">Connection Network Visualization</div>
        
        {/* Node selector */}
        <div className="flex justify-center space-x-2 mb-4">
          {nodes.map((node, index) => (
            <button
              key={index}
              className={`px-3 py-1 rounded-md text-xs font-mono transition-all ${
                selectedNode === index 
                  ? 'bg-[#ff6b37] text-black' 
                  : 'bg-[rgba(255,107,55,0.2)] text-[#ff6b37] hover:bg-[rgba(255,107,55,0.3)]'
              }`}
              onClick={() => setSelectedNode(index)}
            >
              {node.name}
            </button>
          ))}
        </div>
        
        {/* Network visualization */}
        <div className="flex-grow relative border border-[rgba(255,107,55,0.2)] rounded-lg overflow-hidden">
          {/* Central node */}
          <div 
            className="absolute w-12 h-12 rounded-full bg-[rgba(255,107,55,0.2)] border-2 border-[#ff6b37] flex items-center justify-center text-[#ff6b37] font-mono text-xs"
            style={{ 
              left: '50%', 
              top: '50%', 
              transform: 'translate(-50%, -50%)',
              boxShadow: '0 0 15px rgba(255,107,55,0.3)'
            }}
          >
            {nodes[selectedNode].name}
          </div>
          
          {/* Connected nodes */}
          {nodes[selectedNode].connections.map((connName, index) => {
            // Calculate position in a circle around the center
            const angle = (index * (360 / nodes[selectedNode].connections.length)) * (Math.PI / 180);
            const radius = 35; // % of container
            const x = 50 + radius * Math.cos(angle);
            const y = 50 + radius * Math.sin(angle);
            
            return (
              <div key={connName}>
                {/* Connection line */}
                <div 
                  className="absolute bg-[rgba(255,107,55,0.3)]"
                  style={{
                    left: '50%',
                    top: '50%',
                    width: `${radius}%`,
                    height: '2px',
                    transformOrigin: 'left center',
                    transform: `rotate(${angle * (180 / Math.PI)}deg)`,
                  }}
                >
                  {/* Animated particle on the connection */}
                  <div 
                    className="absolute h-1.5 w-1.5 rounded-full bg-[#ff6b37]"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: '-2px',
                      animation: `pulse 1.5s infinite ease-in-out`,
                    }}
                  />
                </div>
                
                {/* Connected node */}
                <div 
                  className="absolute w-8 h-8 rounded-full bg-[rgba(255,107,55,0.1)] border border-[rgba(255,107,55,0.5)] flex items-center justify-center text-[#ff6b37] font-mono text-xs"
                  style={{ 
                    left: `${x}%`, 
                    top: `${y}%`, 
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  {connName}
                </div>
              </div>
            );
          })}
          
          {/* Background grid */}
          <div className="absolute inset-0 grid grid-cols-6 grid-rows-6">
            {Array.from({ length: 36 }).map((_, i) => (
              <div 
                key={i} 
                className="border border-[rgba(255,107,55,0.05)]"
              />
            ))}
          </div>
          
          {/* Decorative elements */}
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={`dot-${i}`}
              className="absolute w-1 h-1 rounded-full bg-[rgba(255,107,55,0.3)]"
              style={{
                left: `${5 + Math.random() * 90}%`,
                top: `${5 + Math.random() * 90}%`,
                animation: `pulse ${1 + Math.random() * 2}s infinite ease-in-out`,
                animationDelay: `${Math.random() * 2}s`
              }}
            />
          ))}
        </div>
        
        {/* Node information */}
        <div className="mt-4 p-3 bg-[rgba(255,107,55,0.05)] border-l-2 border-[#ff6b37] rounded-r-md text-xs">
          <div className="font-medium text-[#ff6b37] mb-1">Node Information</div>
          <p className="text-gray-300">{nodes[selectedNode].description}</p>
          <div className="mt-2 font-medium text-[#ff6b37]">Connection Strength</div>
          <div className="w-full bg-[rgba(255,255,255,0.1)] rounded-full h-1.5 mt-1">
            <div 
              className="h-1.5 rounded-full bg-[#ff6b37]" 
              style={{ width: `${(nodes[selectedNode].connections.length / 3) * 100}%` }}
            />
          </div>
        </div>
      </div>
    );
  };

  const NeuralActivityVisualization = () => {
    const [activeRegion, setActiveRegion] = useState("prefrontal");
    const brainRegions = [
      { id: "prefrontal", name: "Prefrontal Cortex", activity: 8.7 },
      { id: "temporal", name: "Temporal Lobe", activity: 6.4 },
      { id: "parietal", name: "Parietal Lobe", activity: 7.2 },
      { id: "occipital", name: "Occipital Lobe", activity: 5.9 },
      { id: "limbic", name: "Limbic System", activity: 9.1 }
    ];
    
    // EEG frequency bands
    const frequencyBands = [
      { name: "Delta", hz: "0.5-4 Hz", amplitude: 65 },
      { name: "Theta", hz: "4-8 Hz", amplitude: 40 },
      { name: "Alpha", hz: "8-13 Hz", amplitude: 85 },
      { name: "Beta", hz: "13-30 Hz", amplitude: 55 },
      { name: "Gamma", hz: "30-100 Hz", amplitude: 30 }
    ];
    
    return (
      <div className="w-full h-full flex flex-col">
        <div className="mb-4 text-center text-sm text-gray-300">Neural Activity Patterns</div>
        
        {/* Brain region selector */}
        <div className="flex justify-center space-x-2 mb-4">
          {brainRegions.map((region) => (
            <button
              key={region.id}
              className={`px-3 py-1 rounded-md text-xs transition-all ${
                activeRegion === region.id 
                  ? 'bg-[#ffcc00] text-black' 
                  : 'bg-[rgba(255,204,0,0.2)] text-[#ffcc00] hover:bg-[rgba(255,204,0,0.3)]'
              }`}
              onClick={() => setActiveRegion(region.id)}
            >
              {region.name}
            </button>
          ))}
        </div>
        
        {/* Brain visualization */}
        <div className="relative h-32 mb-4 border border-[rgba(255,204,0,0.2)] rounded-lg overflow-hidden bg-[rgba(10,14,36,0.8)]">
          {/* Brain outline */}
          <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 60">
            {/* Simplified brain outline */}
            <path
              d="M25,10 C20,15 15,25 15,30 C15,35 20,45 25,50 C30,55 40,58 50,58 C60,58 70,55 75,50 C80,45 85,35 85,30 C85,25 80,15 75,10 C70,5 60,2 50,2 C40,2 30,5 25,10 Z"
              fill="none"
              stroke="rgba(255,204,0,0.3)"
              strokeWidth="0.5"
            />
            
            {/* Brain regions - highlight active region */}
            <path
              d={activeRegion === "prefrontal" ? 
                "M30,10 C25,15 22,20 25,25 C30,30 40,30 50,30 C60,30 70,30 75,25 C78,20 75,15 70,10 C65,5 55,5 50,5 C45,5 35,5 30,10 Z" :
                "M30,10 C25,15 22,20 25,25 C30,30 40,30 50,30 C60,30 70,30 75,25 C78,20 75,15 70,10 C65,5 55,5 50,5 C45,5 35,5 30,10 Z"}
              fill={activeRegion === "prefrontal" ? "rgba(255,204,0,0.2)" : "none"}
              stroke={activeRegion === "prefrontal" ? "rgba(255,204,0,0.8)" : "rgba(255,204,0,0.1)"}
              strokeWidth="0.5"
            />
            
            <path
              d={activeRegion === "temporal" ? 
                "M20,25 C15,30 15,35 20,40 C25,45 30,45 35,40 C40,35 40,30 35,25 C30,20 25,20 20,25 Z" :
                "M20,25 C15,30 15,35 20,40 C25,45 30,45 35,40 C40,35 40,30 35,25 C30,20 25,20 20,25 Z"}
              fill={activeRegion === "temporal" ? "rgba(255,204,0,0.2)" : "none"}
              stroke={activeRegion === "temporal" ? "rgba(255,204,0,0.8)" : "rgba(255,204,0,0.1)"}
              strokeWidth="0.5"
            />
            
            <path
              d={activeRegion === "parietal" ? 
                "M35,30 C30,35 30,40 35,45 C40,50 50,50 60,50 C65,50 70,45 70,40 C70,35 65,30 60,30 C55,30 40,30 35,30 Z" :
                "M35,30 C30,35 30,40 35,45 C40,50 50,50 60,50 C65,50 70,45 70,40 C70,35 65,30 60,30 C55,30 40,30 35,30 Z"}
              fill={activeRegion === "parietal" ? "rgba(255,204,0,0.2)" : "none"}
              stroke={activeRegion === "parietal" ? "rgba(255,204,0,0.8)" : "rgba(255,204,0,0.1)"}
              strokeWidth="0.5"
            />
            
            <path
              d={activeRegion === "occipital" ? 
                "M35,45 C40,50 50,55 65,55 C75,55 80,50 80,45 C80,40 75,35 70,35 C65,35 40,40 35,45 Z" :
                "M35,45 C40,50 50,55 65,55 C75,55 80,50 80,45 C80,40 75,35 70,35 C65,35 40,40 35,45 Z"}
              fill={activeRegion === "occipital" ? "rgba(255,204,0,0.2)" : "none"}
              stroke={activeRegion === "occipital" ? "rgba(255,204,0,0.8)" : "rgba(255,204,0,0.1)"}
              strokeWidth="0.5"
            />
            
            <path
              d={activeRegion === "limbic" ? 
                "M40,25 C35,30 35,35 40,40 C45,45 55,45 60,40 C65,35 65,30 60,25 C55,20 45,20 40,25 Z" :
                "M40,25 C35,30 35,35 40,40 C45,45 55,45 60,40 C65,35 65,30 60,25 C55,20 45,20 40,25 Z"}
              fill={activeRegion === "limbic" ? "rgba(255,204,0,0.2)" : "none"}
              stroke={activeRegion === "limbic" ? "rgba(255,204,0,0.8)" : "rgba(255,204,0,0.1)"}
              strokeWidth="0.5"
            />
            
            {/* Neural activity points */}
            {Array.from({ length: 30 }).map((_, i) => {
              // Distribute points more densely in active region
              let x, y;
              if (Math.random() < 0.6) {
                // Points in active region
                switch(activeRegion) {
                  case "prefrontal": 
                    x = 30 + Math.random() * 40;
                    y = 10 + Math.random() * 15;
                    break;
                  case "temporal":
                    x = 20 + Math.random() * 15;
                    y = 25 + Math.random() * 15;
                    break;
                  case "parietal":
                    x = 35 + Math.random() * 30;
                    y = 30 + Math.random() * 15;
                    break;
                  case "occipital":
                    x = 50 + Math.random() * 30;
                    y = 45 + Math.random() * 10;
                    break;
                  case "limbic":
                    x = 40 + Math.random() * 20;
                    y = 25 + Math.random() * 15;
                    break;
                  default:
                    x = 20 + Math.random() * 60;
                    y = 10 + Math.random() * 40;
                }
              } else {
                // Random points throughout brain
                x = 20 + Math.random() * 60;
                y = 10 + Math.random() * 40;
              }
              
              return (
                <circle
                  key={i}
                  cx={x}
                  cy={y}
                  r={0.5 + Math.random() * 0.5}
                  fill="#ffcc00"
                  opacity={0.6 + Math.random() * 0.4}
                  className="animate-pulse"
                  style={{ animationDuration: `${1 + Math.random() * 2}s` }}
                />
              );
            })}
          </svg>
          
          {/* Activity level indicator */}
          <div className="absolute bottom-2 left-2 right-2 flex items-center text-xs">
            <span className="text-[#ffcc00] mr-2">Activity:</span>
            <div className="flex-1 h-1.5 bg-[rgba(255,204,0,0.1)] rounded-full overflow-hidden">
              <div 
                className="h-full rounded-full bg-[#ffcc00]" 
                style={{ 
                  width: `${brainRegions.find(r => r.id === activeRegion)?.activity || 0 * 10}%`,
                  transition: 'width 0.5s ease-out'
                }}
              />
            </div>
            <span className="ml-2 text-[#ffcc00]">
              {brainRegions.find(r => r.id === activeRegion)?.activity.toFixed(1)}
            </span>
          </div>
        </div>
        
        {/* EEG waveform visualization */}
        <div className="flex-1 border border-[rgba(255,204,0,0.2)] rounded-lg overflow-hidden p-3 bg-[rgba(10,14,36,0.8)]">
          <div className="text-xs text-[#ffcc00] mb-2">EEG Frequency Bands</div>
          
          {frequencyBands.map((band, index) => (
            <div key={band.name} className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-300">{band.name} ({band.hz})</span>
                <span className="text-[#ffcc00]">{band.amplitude}%</span>
              </div>
              
              <div className="relative h-6">
                {/* Base line */}
                <div className="absolute left-0 right-0 top-1/2 h-px bg-[rgba(255,204,0,0.3)]"></div>
                
                {/* Waveform */}
                <svg className="absolute inset-0 w-full h-full" preserveAspectRatio="none">
                  <path
                    d={generateWaveformPath(band.name, band.amplitude / 100)}
                    fill="none"
                    stroke="#ffcc00"
                    strokeWidth="1"
                    className="animate-pulse"
                    style={{ animationDuration: `${4 - index * 0.5}s` }}
                  />
                </svg>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
    
    // Helper function to generate waveform paths based on frequency band
    function generateWaveformPath(bandType: string, amplitude: number) {
      const width = 100;
      const height = 24;
      const midY = height / 2;
      
      let path = `M 0 ${midY}`;
      let frequency;
      
      switch(bandType) {
        case "Delta": frequency = 2; break;
        case "Theta": frequency = 6; break;
        case "Alpha": frequency = 10; break;
        case "Beta": frequency = 20; break;
        case "Gamma": frequency = 40; break;
        default: frequency = 10;
      }
      
      // Generate sine wave with appropriate frequency
      for (let x = 0; x <= width; x++) {
        const y = midY + Math.sin(x * (frequency / 10) * Math.PI / 10) * midY * amplitude;
        path += ` L ${x} ${y}`;
      }
      
      return path;
    }
  };

  const IdentityMatchVisualization = () => {
    // State for identity match data
    const [matchScore, setMatchScore] = useState(85 + Math.random() * 15);
    const [matchCategories, setMatchCategories] = useState([
      { name: "Communication Style", score: 75 + Math.random() * 25 },
      { name: "Value System", score: 80 + Math.random() * 20 },
      { name: "Emotional Patterns", score: 70 + Math.random() * 30 },
      { name: "Decision Making", score: 65 + Math.random() * 35 },
      { name: "Creative Expression", score: 85 + Math.random() * 15 },
    ]);
    
    return (
      <div className="w-full h-full flex flex-col">
        {/* Overall match score */}
        <div className="mb-6 text-center">
          <div className="text-sm text-gray-400 mb-1">IDENTITY MATCH SCORE</div>
          <div className="text-4xl font-light" style={{ color: "#ff37a6" }}>
            {matchScore.toFixed(1)}%
          </div>
        </div>
        
        {/* Category breakdown */}
        <div className="flex-1 grid grid-cols-1 gap-3">
          {matchCategories.map((category, index) => (
            <div key={index} className="flex items-center">
              <div className="w-32 text-xs text-gray-400">{category.name}</div>
              <div className="flex-1 h-3 bg-[rgba(255,55,166,0.1)] rounded-full overflow-hidden">
                <div 
                  className="h-full rounded-full"
                  style={{ 
                    width: `${category.score}%`, 
                    background: `linear-gradient(90deg, rgba(255,55,166,0.5) 0%, rgba(255,55,166,1) 100%)`,
                    boxShadow: '0 0 8px rgba(255,55,166,0.5)'
                  }}
                />
              </div>
              <div className="ml-2 text-xs" style={{ color: "#ff37a6" }}>
                {category.score.toFixed(1)}%
              </div>
            </div>
          ))}
        </div>
        
        {/* Visual representation of identity match */}
        <div className="mt-4 relative h-24 flex justify-center items-center">
          <div className="absolute w-20 h-20 rounded-full border-2 border-[rgba(255,255,255,0.2)]" />
          <div className="absolute w-20 h-20 rounded-full border-2 border-[#ff37a6] border-dashed animate-spin" style={{ animationDuration: '10s' }} />
          <div className="absolute w-10 h-10 rounded-full bg-[rgba(255,55,166,0.2)]" />
          <div className="absolute w-6 h-6 rounded-full bg-[#ff37a6] animate-pulse" />
        </div>
      </div>
    );
  }

  // Add this function to render a neural network visualization
  const renderNeuralNetworkVisualization = (projectName: string) => {
    const nodeCount = 30; // Number of nodes in the network
    const connectionCount = 50; // Number of connections
    const projectColor = projects.find(p => p.name === projectName)?.color || "#38b6ff";
    
    // Generate random nodes
    const nodes = Array.from({ length: nodeCount }).map((_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 2,
      pulse: Math.random() > 0.7, // Some nodes will pulse
      delay: Math.random() * 2 // Random animation delay
    }));
    
    // Generate random connections between nodes
    const connections = Array.from({ length: connectionCount }).map(() => {
      const source = Math.floor(Math.random() * nodeCount);
      const target = Math.floor(Math.random() * nodeCount);
      return {
        source,
        target,
        strength: Math.random(), // Connection strength
        active: Math.random() > 0.3 // Some connections will be active
      };
    });
    
    return (
      <div className="relative h-40 w-full bg-[rgba(10,14,36,0.8)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
        {/* Neural network nodes */}
        {nodes.map((node) => (
          <div
            key={`node-${node.id}`}
            className={`absolute rounded-full ${node.pulse ? 'animate-pulse' : ''}`}
            style={{
              left: `${node.x}%`,
              top: `${node.y}%`,
              width: `${node.size}px`,
              height: `${node.size}px`,
              backgroundColor: projectColor,
              opacity: 0.6,
              transform: 'translate(-50%, -50%)',
              animationDelay: `${node.delay}s`
            }}
          />
        ))}
        
        {/* Neural connections */}
        {connections.map((conn, i) => {
          const sourceNode = nodes[conn.source];
          const targetNode = nodes[conn.target];
          
          // Calculate line properties
          const dx = targetNode.x - sourceNode.x;
          const dy = targetNode.y - sourceNode.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          const angle = Math.atan2(dy, dx) * (180 / Math.PI);
          
          return (
            <div
              key={`conn-${i}`}
              className="absolute"
              style={{
                left: `${sourceNode.x}%`,
                top: `${sourceNode.y}%`,
                width: `${distance}%`,
                height: '1px',
                backgroundColor: projectColor,
                opacity: conn.strength * 0.4,
                transform: `rotate(${angle}deg)`,
                transformOrigin: 'left center',
                transition: 'opacity 0.5s ease'
              }}
            >
              {conn.active && (
                <div 
                  className="absolute h-1 w-1 rounded-full animate-pulse"
                  style={{
                    backgroundColor: 'white',
                    left: `${Math.random() * 100}%`,
                    animationDuration: `${1 + Math.random() * 2}s`
                  }}
                />
              )}
            </div>
          );
        })}
        
        {/* Data flow animations */}
        {Array.from({ length: 10 }).map((_, i) => {
          const sourceNode = nodes[Math.floor(Math.random() * nodeCount)];
          const targetNode = nodes[Math.floor(Math.random() * nodeCount)];
          
          return (
            <div
              key={`data-${i}`}
              className="absolute h-1 w-1 rounded-full"
              style={{
                backgroundColor: 'white',
                left: `${sourceNode.x}%`,
                top: `${sourceNode.y}%`,
                opacity: 0.8,
                animation: `dataFlow${i} ${2 + Math.random() * 3}s infinite`,
                animationDelay: `${Math.random() * 5}s`
              }}
            />
          );
        })}
        
        {/* Project label */}
        <div className="absolute bottom-2 right-2 text-xs font-mono" style={{ color: projectColor }}>
          {projectName.toUpperCase()} NEURAL MATRIX
        </div>
        
        <style jsx>{`
          @keyframes dataFlow0 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow1 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow2 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow3 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow4 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow5 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow6 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow7 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow8 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
          @keyframes dataFlow9 { to { transform: translate(${Math.random() * 100 - 50}%, ${Math.random() * 100 - 50}%); opacity: 0; } }
        `}</style>
      </div>
    );
  };

  // Enhanced TerminalDisplay component with project-specific customizations
  const TerminalDisplay = ({ projectName, experimentProgress }: { projectName: string, experimentProgress: number }) => {
    // Get project color for styling
    const projectColor = projects.find(p => p.name === projectName)?.color || "#38b6ff";
    
    // Veritas-specific terminal styling and content
    if (projectName === "Veritas") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Veritas terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>veritas-emotional-core@neural:~$</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Veritas-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing emotional coherence analyzer...</div>
            )}
            
            {experimentProgress > 10 && (
              <div>$ loading affective cognition modules...</div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ importing belief consistency matrices...</div>
            )}
            
            {/* Veritas-specific code snippets */}
            {experimentProgress > 20 && Array.from({ length: Math.min(8, experimentProgress / 10) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`EmotionalCoherence.analyze(subject_${Math.floor(Math.random() * 1000)}, {`}
                <span style={{ marginLeft: '10px' }}>
                  {`coherence_index: ${(Math.random() * 100).toFixed(2)}%,`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`affective_state: "${['positive', 'neutral', 'negative', 'ambivalent'][Math.floor(Math.random() * 4)]}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`belief_consistency: ${(Math.random() * 10).toFixed(1)}/10`}
                </span>
                {`});`}
              </div>
            ))}
            
            {/* Veritas-specific metrics and analysis */}
            {experimentProgress > 40 && (
              <div className="mt-1">
                {`> IdentityStability.measure(${(Math.random() * 100).toFixed(1)}%)`}
              </div>
            )}
            
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> DecisionAlignment.calculate(${(Math.random() * 10).toFixed(2)})`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Emotional coherence vectors stabilizing at ${(experimentProgress).toFixed(1)}%`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Self-awareness index calculated: ${(Math.random() * 10).toFixed(1)}/10`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1">
                {`$ finalizing emotional coherence matrix...`}
              </div>
            )}
            
            {/* Scanline effect */}
            <div 
              className="absolute left-0 right-0 h-[1px] opacity-30"
              style={{ 
                top: `${Math.random() * 100}%`,
                background: projectColor,
                boxShadow: `0 0 5px ${projectColor}`,
                animation: 'scanline 2s linear infinite'
              }}
            />
          </div>
        </div>
      );
    }
    
    // Custos-specific terminal styling and content
    if (projectName === "Custos") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Custos terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>custos-security@shield:~#</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Custos-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing quantum encryption protocols...</div>
            )}
            
            {experimentProgress > 10 && (
              <div>$ establishing secure neural channels...</div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ loading privacy protection matrices...</div>
            )}
            
            {/* Custos-specific code snippets */}
            {experimentProgress > 20 && Array.from({ length: Math.min(8, experimentProgress / 10) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`QuantumEncryption.secure(data_packet_${Math.floor(Math.random() * 1000)}, {`}
                <span style={{ marginLeft: '10px' }}>
                  {`encryption_level: ${Math.floor(Math.random() * 4096) + 2048},`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`algorithm: "${['AES-256', 'RSA-4096', 'ECC-521', 'QUANTUM-RESISTANT'][Math.floor(Math.random() * 4)]}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`entropy: ${(Math.random() * 100).toFixed(2)}`}
                </span>
                {`});`}
              </div>
            ))}
            
            {/* Custos-specific metrics and analysis */}
            {experimentProgress > 40 && (
              <div className="mt-1">
                {`> PrivacyShield.measure(${(Math.random() * 100).toFixed(1)}%)`}
              </div>
            )}
            
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> SecurityAudit.scan(vulnerability_count: ${Math.floor(Math.random() * 5)})`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-yellow-400 mt-1">
                {`WARNING: Intrusion attempt detected. Countermeasures deployed.`}
              </div>
            )}
            
            {experimentProgress > 70 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Neural firewall strengthened to level ${Math.floor(experimentProgress / 10)}`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Data encapsulation complete. Privacy score: ${(Math.random() * 10).toFixed(1)}/10`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1 text-red-400">
                {`$ deploying quantum encryption matrix...`}
              </div>
            )}
            
            {/* Security scan effect */}
            <div 
              className="absolute left-0 right-0 h-[1px] opacity-30"
              style={{ 
                top: `${Math.random() * 100}%`,
                background: projectColor,
                boxShadow: `0 0 5px ${projectColor}`,
                animation: 'scanline 1.5s linear infinite'
              }}
            />
          </div>
        </div>
      );
    }
    
    // Vitae-specific terminal styling and content
    if (projectName === "Vitae") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Vitae terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>vitae-simulation@lifeworld:~$</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Vitae-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing life simulation engine...</div>
            )}
            
            {experimentProgress > 10 && (
              <div>$ loading decision tree frameworks...</div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ importing parallel timeline generators...</div>
            )}
            
            {/* Vitae-specific code snippets */}
            {experimentProgress > 20 && Array.from({ length: Math.min(8, experimentProgress / 10) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`SimulationEngine.createWorld({`}
                <span style={{ marginLeft: '10px' }}>
                  {`scenario: "${['career_choice', 'relationship', 'life_decision', 'ethical_dilemma'][Math.floor(Math.random() * 4)]}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`timeline_years: ${Math.floor(Math.random() * 20) + 1},`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`complexity: ${Math.floor(Math.random() * 5) + 1}`}
                </span>
                {`});`}
              </div>
            ))}
            
            {/* Vitae-specific metrics and analysis */}
            {experimentProgress > 40 && (
              <div className="mt-1">
                {`> DecisionPathways.generate(branches: ${Math.floor(Math.random() * 8) + 2})`}
              </div>
            )}
            
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> OutcomeSimulator.predict(satisfaction_score: ${(Math.random() * 10).toFixed(1)})`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Simulating life path variant #${Math.floor(Math.random() * 100)}`}
              </div>
            )}
            
            {experimentProgress > 70 && (
              <div className="text-yellow-400 mt-1">
                {`BRANCH: Timeline divergence detected at decision point ${Math.floor(Math.random() * 10) + 1}`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Life satisfaction optimization complete. Optimal path identified.`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1">
                {`$ finalizing multiverse simulation matrix...`}
              </div>
            )}
            
            {/* Timeline effect - vertical line with branching points */}
            <div 
              className="absolute left-4 top-0 bottom-0 w-[1px]"
              style={{ 
                background: `linear-gradient(to bottom, transparent, ${projectColor}, transparent)`,
                opacity: 0.4
              }}
            />
            
            {/* Branch points on timeline */}
            {Array.from({ length: 3 }).map((_, i) => (
              <div 
                key={`branch-${i}`}
                className="absolute left-4 w-2 h-[1px]"
                style={{ 
                  top: `${20 + i * 25}%`,
                  background: projectColor,
                  transform: 'translateX(1px)',
                  opacity: 0.6
                }}
              />
            ))}
          </div>
        </div>
      );
    }
    
    // Aetheris-specific terminal styling and content
    if (projectName === "Aetheris") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Aetheris terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>aetheris-consciousness@mindscape:~$</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Aetheris-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing consciousness framework...</div>
            )}
            
            {experimentProgress > 10 && (
              <div>$ loading cognitive architecture modules...</div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ importing neural substrate emulators...</div>
            )}
            
            {/* Aetheris-specific code snippets */}
            {experimentProgress > 20 && Array.from({ length: Math.min(8, experimentProgress / 10) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`ConsciousnessEngine.simulate({`}
                <span style={{ marginLeft: '10px' }}>
                  {`awareness_level: ${(Math.random() * 100).toFixed(1)}%,`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`cognitive_model: "${['bayesian', 'emergent', 'hierarchical', 'integrated'][Math.floor(Math.random() * 4)]}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`qualia_index: ${(Math.random() * 10).toFixed(2)}`}
                </span>
                {`});`}
              </div>
            ))}
            
            {/* Aetheris-specific metrics and analysis */}
            {experimentProgress > 40 && (
              <div className="mt-1">
                {`> SelfAwareness.measure(depth: ${Math.floor(Math.random() * 5) + 1})`}
              </div>
            )}
            
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> CognitiveReflection.analyze(recursion_level: ${Math.floor(Math.random() * 3) + 1})`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Consciousness substrate stabilizing at ${experimentProgress.toFixed(1)}% coherence`}
              </div>
            )}
            
            {experimentProgress > 70 && (
              <div className="text-purple-400 mt-1">
                {`INSIGHT: Emergent thought patterns detected in neural substrate`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Consciousness simulation achieved. Turing score: ${(Math.random() * 5 + 5).toFixed(1)}/10`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1">
                {`$ finalizing consciousness integration matrix...`}
              </div>
            )}
            
            {/* Consciousness wave effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {Array.from({ length: 3 }).map((_, i) => (
                <div 
                  key={`wave-${i}`}
                  className="absolute w-full h-[1px] opacity-20"
                  style={{ 
                    top: `${30 + i * 20}%`,
                    background: projectColor,
                    boxShadow: `0 0 8px ${projectColor}`,
                    animation: `wave ${3 + i}s ease-in-out infinite`,
                    animationDelay: `${i * 0.5}s`
                  }}
                />
              ))}
            </div>
            
            {/* Add wave animation */}
            <style jsx>{`
              @keyframes wave {
                0%, 100% { transform: scaleX(0.8); left: -10%; }
                50% { transform: scaleX(1.2); left: 0%; }
              }
            `}</style>
          </div>
        </div>
      );
    }
    
    // Cognita-specific terminal styling and content
    if (projectName === "Cognita") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Cognita terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>cognita-neuroscience@research:~$</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Cognita-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing neuroimaging analysis framework...</div>
            )}
            
            {experimentProgress > 10 && (
              <div>$ loading EEG signal processing modules...</div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ importing brain region mapping libraries...</div>
            )}
            
            {/* Cognita-specific code snippets */}
            {experimentProgress > 20 && Array.from({ length: Math.min(8, experimentProgress / 10) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`NeuroscienceAPI.analyze({`}
                <span style={{ marginLeft: '10px' }}>
                  {`subject: "user_${Math.floor(Math.random() * 1000)}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`brainwave: "${['alpha', 'beta', 'gamma', 'theta', 'delta'][Math.floor(Math.random() * 5)]}",`}
                </span>
                <span style={{ marginLeft: '10px' }}>
                  {`region: "${['prefrontal', 'temporal', 'parietal', 'occipital', 'limbic'][Math.floor(Math.random() * 5)]}"`}
                </span>
                {`});`}
              </div>
            ))}
            
            {/* Cognita-specific metrics and analysis */}
            {experimentProgress > 40 && (
              <div className="mt-1">
                {`> BrainActivity.measure(frequency: ${(Math.random() * 40 + 1).toFixed(1)} Hz)`}
              </div>
            )}
            
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> NeuralCorrelation.calculate(p_value: ${(Math.random() * 0.05).toFixed(4)})`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Significant neural activity detected in ${['prefrontal cortex', 'hippocampus', 'amygdala', 'thalamus'][Math.floor(Math.random() * 4)]}`}
              </div>
            )}
            
            {experimentProgress > 70 && (
              <div className="text-yellow-400 mt-1">
                {`RESEARCH: Collecting data for peer-reviewed publication...`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Neural mapping complete. Confidence interval: ${(95 + Math.random() * 4).toFixed(1)}%`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1">
                {`$ finalizing neurological research data...`}
              </div>
            )}
            
            {/* Brain scan effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {/* Horizontal scan line */}
              <div 
                className="absolute left-0 right-0 h-[2px]"
                style={{ 
                  top: `${(experimentProgress % 100)}%`,
                  background: `linear-gradient(to right, transparent, ${projectColor}, transparent)`,
                  opacity: 0.5,
                  boxShadow: `0 0 8px ${projectColor}`,
                  transition: 'top 0.5s ease-in-out'
                }}
              />
              
              {/* Brain region indicators */}
              {Array.from({ length: 5 }).map((_, i) => (
                <div 
                  key={`region-${i}`}
                  className="absolute h-1 w-1 rounded-full"
                  style={{ 
                    left: `${10 + i * 20}%`,
                    top: `${20 + Math.sin(i * 0.8) * 15}%`,
                    background: projectColor,
                    opacity: Math.random() > 0.5 ? 0.8 : 0.3,
                    boxShadow: `0 0 5px ${projectColor}`,
                    animation: `pulse ${1 + Math.random() * 2}s infinite`
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      );
    }
    
    // Somae-specific terminal styling and content
    if (projectName === "Somae") {
      return (
        <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
          <div className="h-full w-full font-mono text-[10px] p-2 overflow-y-auto" style={{ color: projectColor }}>
            {/* Somae terminal header */}
            <div className="text-gray-400 mb-1 flex justify-between">
              <span>somae-personal-ai@neural:~$</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
            
            {/* Somae-specific loading sequence */}
            {experimentProgress > 5 && (
              <div>$ initializing personal language model...</div>
            )}
            
            {experimentProgress > 10 && (
              <div className="mb-2">
                {`// Training personal LLM model`}
                {`\n// Importing user data...`}
              </div>
            )}
            
            {experimentProgress > 15 && (
              <div>$ loading personality adaptation modules...</div>
            )}
            
            {/* Somae-specific training epochs */}
            {experimentProgress > 20 && Array.from({ length: Math.min(15, (experimentProgress - 20) / 5) }).map((_, i) => (
              <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
                {`[${(experimentProgress / 100).toFixed(2)}] Epoch ${i+1}: `}
                {`loss=${(0.5 - (i * 0.03)).toFixed(4)}, `}
                {`accuracy=${(0.5 + (i * 0.03)).toFixed(4)}, `}
                {`perplexity=${(100 - i * 5).toFixed(2)}`}
              </div>
            ))}
            
            {/* Somae-specific metrics and analysis */}
            {experimentProgress > 50 && (
              <div className="mt-1">
                {`> PersonalityMatrix.calibrate(alignment: ${(Math.random() * 100).toFixed(1)}%)`}
              </div>
            )}
            
            {experimentProgress > 60 && (
              <div className="text-blue-400 mt-1">
                {`INFO: Fine-tuning model on user communication patterns...`}
              </div>
            )}
            
            {experimentProgress > 70 && (
              <div className="mt-1">
                {`> ResponseGenerator.test("${['How are you feeling today?', 'What should I focus on?', 'Tell me about yourself', 'What do you think about this?'][Math.floor(Math.random() * 4)]}")`}
              </div>
            )}
            
            {experimentProgress > 75 && (
              <div className="text-gray-300 mt-1 ml-2">
                {`"${['I sense you might be feeling thoughtful today. Would you like to explore that?', 
                  'Based on your recent patterns, focusing on creative work might be energizing.', 
                  'I\'ve been adapting to better understand your communication style.',
                  'I think this aligns with your previously expressed values.'][Math.floor(Math.random() * 4)]}"`}
              </div>
            )}
            
            {experimentProgress > 80 && (
              <div className="text-green-500 mt-1">
                {`SUCCESS: Personal AI model trained. Personality match: ${(85 + Math.random() * 15).toFixed(1)}%`}
              </div>
            )}
            
            {experimentProgress > 90 && (
              <div className="animate-pulse mt-1">
                {`$ finalizing neural personality integration...`}
              </div>
            )}
            
            {/* Text generation effect */}
            <div className="absolute bottom-3 left-2 right-2 h-5 overflow-hidden opacity-30">
              <div className="whitespace-nowrap font-mono text-[8px]" style={{ color: projectColor, animation: 'textScroll 20s linear infinite' }}>
                {Array.from({ length: 20 }).map((_, i) => (
                  <span key={i} className="mr-2">
                    {['understanding', 'adapting', 'learning', 'personalizing', 'responding', 'analyzing', 'integrating'][Math.floor(Math.random() * 7)]}
                  </span>
                ))}
              </div>
            </div>
            
            {/* Add text scroll animation */}
            <style jsx>{`
              @keyframes textScroll {
                0% { transform: translateX(0); }
                100% { transform: translateX(-50%); }
              }
            `}</style>
          </div>
        </div>
      );
    }
    
    // Return the original terminal for other projects
    return (
      <div className="mt-4 h-40 w-full bg-[rgba(10,14,36,0.9)] rounded-lg overflow-hidden border border-[rgba(56,182,255,0.2)]">
        <div className="h-full w-full font-mono text-[10px] text-green-400 p-2 overflow-y-auto">
          {/* Original terminal content */}
          <div className="text-gray-400 mb-1 flex justify-between">
            <span>neural-terminal@{projectName.toLowerCase()}:~$</span>
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
          
          {experimentProgress > 5 && <div>$ initializing neural experiment module...</div>}
          {experimentProgress > 10 && <div>$ loading dependencies...</div>}
          {experimentProgress > 15 && <div>$ importing neural network libraries...</div>}
          
          {experimentProgress > 20 && Array.from({ length: Math.min(10, experimentProgress / 10) }).map((_, i) => (
            <div key={i} style={{ opacity: Math.random() * 0.5 + 0.5 }}>
              {`> ${getRandomCodeLine(projectName, i)}`}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Enhanced helper function to generate random code lines based on project
  const getRandomCodeLine = (projectName: string, index: number): string => {
    const commonLines = [
      `import tensorflow as tf`,
      `from neural.core import NeuralMatrix`,
      `model = tf.keras.Sequential()`,
      `optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)`,
      `loss = tf.keras.losses.CategoricalCrossentropy()`,
      `accuracy = tf.keras.metrics.Accuracy()`,
      `model.compile(optimizer=optimizer, loss=loss, metrics=[accuracy])`,
      `history = model.fit(x_train, y_train, epochs=10, batch_size=32)`,
      `predictions = model.predict(x_test)`,
      `model.save('/models/${projectName.toLowerCase()}_v1.h5')`,
    ];
    
    const projectSpecificLines: Record<string, string[]> = {
      "Veritas": [
        `emotional_data = EmotionalDataset.load("emotions_v3.csv")`,
        `coherence_matrix = NeuralMatrix.create_coherence(dimensions=512)`,
        `belief_system = BeliefNetwork(nodes=128, edges=512)`,
        `emotional_model.train(emotional_data, epochs=100)`,
        `truth_score = veritas_engine.evaluate(input_text, threshold=0.85)`,
        `identity_stability = EmotionalCoherence.calculate(user_data)`,
        `decision_alignment = DecisionMatrix.analyze(choice_history)`,
        `belief_consistency = BeliefSystem.check_contradictions(belief_set)`,
        `emotional_vectors = EmotionalVectorSpace.embed(emotional_states)`,
        `self_awareness_index = SelfAwareness.calculate(reflection_data)`,
      ],
      "Custos": [
        `encryption_layer = QuantumEncryption(bits=4096)`,
        `secure_channel = SecureChannel.establish(encryption_layer)`,
        `vulnerability_scan = SecurityScanner.deep_scan(system_architecture)`,
        `firewall = NeuralFirewall(layers=7, nodes_per_layer=256)`,
        `intrusion_detection = IntrusionDetectionSystem(sensitivity=0.95)`,
        `privacy_score = PrivacyAnalyzer.calculate(user_data)`,
        `protection_layers = SecurityLayers.initialize(security_config)`,
        `encryption_level = EncryptionStrength.measure(encryption_algorithm)`,
        `quantum_key = QuantumKeyDistribution.generate(entropy_source)`,
        `neural_security = NeuralSecurity.train(attack_patterns)`,
      ],
      "Vitae": [
        `simulation = SimulationEngine.create(parameters=sim_params)`,
        `timeline = TimelineGenerator.create(years=10, branching_factor=3)`,
        `decision_tree = DecisionTree(max_depth=8, min_samples_split=20)`,
        `life_path = LifePathSimulator(starting_age=25, variables=life_vars)`,
        `outcome_probabilities = simulation.calculate_probabilities(decision_point)`,
        `scenario = ScenarioGenerator.create("${['career_choice', 'relationship', 'life_decision', 'ethical_dilemma'][Math.floor(Math.random() * 4)]}")`,
        `timeline_years = TimelineProjection.calculate(current_age, life_expectancy)`,
        `decision_complexity = DecisionComplexity.measure(decision_variables)`,
        `life_satisfaction = SatisfactionPredictor.estimate(life_choices)`,
        `alternative_futures = FuturePathways.generate(current_state, decision_points)`,
      ],
      "Aetheris": [
        `network_graph = NetworkGraph.initialize`,
        `resonance_strength = NetworkGraph.calculate_resonance(user_profile)`,
        `connection_strength = NetworkGraph.calculate_connection(user_id, target_id)`,
        `archetypal_pattern = ArchetypalAnalyzer.analyze(user_profile)`,
        `connection_density = NetworkGraph.calculate_density(user_id)`,
        `meaningful_connection = NetworkGraph.filter_connections(user_id, threshold=0.8)`,
      ],
      "Cognita": [
        `brain_scan = BrainScanAnalyzer.process(raw_scan_data)`,
        `neural_activity = NeuralActivityTracker.track(eeg_data, duration=60)`,
        `brain_region_map = BrainMapper.create_map(fmri_data)`,
        `cognitive_load = CognitiveLoadEstimator.estimate(task_data)`,
        `memory_formation = MemoryFormationSimulator.simulate(stimulus_data)`,
      ],
      "Somae": [
        `personality_model = PersonalityModel.load(user_id)`,
        `llm = LargeLanguageModel(parameters=1.3e9, layers=24)`,
        `fine_tuning = FineTuner.create_session(base_model=llm, training_data=user_data)`,
        `embedding = SentenceEmbedder.embed(input_text)`,
        `response = llm.generate(prompt, temperature=0.7, max_tokens=100)`,
      ],
    };
    
    // Use project-specific lines if available, otherwise use common lines
    const availableLines = projectName in projectSpecificLines 
      ? [...commonLines, ...projectSpecificLines[projectName]] 
      : commonLines;
    
    // Return a random line, but try to make it somewhat sequential
    const position = (index + Math.floor(Math.random() * 3)) % availableLines.length;
    return availableLines[position];
  };

  return (
    <div className="min-h-screen bg-[rgb(10,17,40)] text-white">
      <BackgroundParticles />
      <Navbar />
      
      {/* Back Button */}
      <div className="absolute top-20 left-6 z-40">
        <button 
          onClick={() => router.push("/dashboard")}
          className="flex items-center gap-2 px-4 py-2 rounded-full bg-[rgba(56,182,255,0.1)] text-white hover:bg-[rgba(56,182,255,0.2)] transition-colors"
        >
          <ArrowLeft className="w-4 h-4 text-[#38b6ff]" />
          <span>Back to Dashboard</span>
        </button>
      </div>
      
      <div className="container mx-auto px-4 py-16 pt-24">
        <div 
          ref={headerRef}
          className="max-w-3xl mx-auto mb-16 text-center relative"
        >
          <motion.h1 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl md:text-5xl font-light mb-4 text-white flex items-center justify-center"
          >
            Yubi <span className="text-[#38b6ff] mx-2">Research</span> Lab
            <FlaskConical className="w-8 h-8 ml-3 text-[#37fff1]" />
          </motion.h1>
          <motion.p 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-gray-400 max-w-2xl mx-auto"
          >
            Access the interconnected neural research modules. Each node represents a phase in our advanced cognitive intelligence development pipeline.
          </motion.p>
          
          {/* Neural activity indicators */}
          <div className="absolute left-0 top-1/2 -translate-y-1/2">
            <div className="relative h-20 w-20">
              {Array.from({ length: 5 }).map((_, i) => (
                <div 
                  key={i}
                  className="absolute rounded-full animate-ping"
                  style={{
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: `${(i+1) * 10}px`,
                    height: `${(i+1) * 10}px`,
                    border: '1px solid #38b6ff',
                    opacity: 0.3,
                    animationDuration: `${3 + i}s`,
                    animationDelay: `${i * 0.5}s`
                  }}
                />
              ))}
            </div>
          </div>
          
          <div className="absolute right-0 top-1/2 -translate-y-1/2">
            <div className="relative h-20 w-20">
              {Array.from({ length: 5 }).map((_, i) => (
                <div 
                  key={i}
                  className="absolute rounded-full animate-ping"
                  style={{
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: `${(i+1) * 10}px`,
                    height: `${(i+1) * 10}px`,
                    border: '1px solid #a137ff',
                    opacity: 0.3,
                    animationDuration: `${3 + i}s`,
                    animationDelay: `${i * 0.5}s`
                  }}
                />
              ))}
            </div>
          </div>
        </div>
        
        <AnimatePresence mode="wait">
          {isExperimentMode ? renderExperimentView() : renderProjectGrid()}
        </AnimatePresence>
        
        {/* Add the modal here */}
        <AnimatePresence>
          {showInsightsModal && renderInsightsModal()}
        </AnimatePresence>
        
        {!isExperimentMode && hasMoreProjects && (
          <div className="flex justify-center mt-12">
            <button 
              onClick={loadMoreProjects}
              className="px-6 py-3 rounded-md bg-[rgba(56,182,255,0.1)] text-[#38b6ff] hover:bg-[rgba(56,182,255,0.2)] transition-colors relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[rgba(56,182,255,0.1)] to-transparent shimmer-animation"></div>
              <span className="font-mono tracking-wider flex items-center">
                <Network className="w-4 h-4 mr-2" />
                EXPAND NEURAL NETWORK
                <ChevronDown className="w-4 h-4 ml-2" />
              </span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
