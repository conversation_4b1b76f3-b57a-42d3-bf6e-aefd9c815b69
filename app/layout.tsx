import type { Metadata, Viewport } from 'next/types'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'
import { montserrat, inter, robotoMono } from '@/components/theme'

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#020419',
}

export const metadata: Metadata = {
  title: 'Yubi - AI Companion for Self-Discovery',
  description: 'An emotionally intelligent AI that helps you explore your identity through guided reflection and meaningful conversation.',
  generator: 'Next.js',
  applicationName: 'Yubi',
  keywords: [
    'AI Companion',
    'Mental Health',
    'Self-Discovery',
    'Identity Exploration',
    'Emotional Intelligence'
  ],
  authors: [{ name: 'Yubi Team' }],
  creator: 'Yubi Development Team',
  metadataBase: new URL('https://yubi.app'),
  openGraph: {
    title: 'Yubi - The Mirror That Speaks',
    description: 'Begin your journey of self-discovery with an AI companion that asks the right questions and stays long enough to matter.',
    images: '/og-image.jpg',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Yubi - AI-Powered Self-Reflection',
    description: 'Explore who you are before the world tells you who to be',
    images: ['https://yubi.app/twitter-image.jpg'],
  },
  manifest: '/manifest.json'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${montserrat.variable} ${inter.variable} ${robotoMono.variable}`}>
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
