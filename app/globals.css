@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
  --dark-blue: 2, 4, 25;
  --darker-blue: 0, 1, 18;
  --deepest-blue: 0, 0, 15;
  --accent-blue: 56, 182, 255;
  --accent-purple: 161, 55, 255;
  --deep-purple: 30, 0, 60;

  /* Futuristic theme colors */
  --neon-blue: 56, 182, 255;
  --neon-purple: 161, 55, 255;
  --accent-cyan: 0, 225, 255;
  --text-glow: 0 0 10px rgba(56, 182, 255, 0.7);
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
    135deg,
    rgb(var(--deepest-blue)) 0%,
    rgb(var(--darker-blue)) 25%,
    rgb(var(--deep-purple)) 50%,
    rgb(var(--darker-blue)) 75%,
    rgb(var(--dark-blue)) 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 30s ease-in-out infinite;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  min-height: 100vh;
  overflow-x: hidden;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 150%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add a subtle overlay */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, 
    rgba(var(--accent-blue), 0.05) 0%,
    rgba(var(--accent-purple), 0.05) 100%);
  pointer-events: none;
  animation: pulseOverlay 8s ease-in-out infinite;
}

@keyframes pulseOverlay {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.text-glow {
  text-shadow: 0 0 20px rgba(56, 182, 255, 0.7),
               0 0 40px rgba(56, 182, 255, 0.4);
}

.neon-border {
  border: 1px solid rgba(var(--neon-blue), 0.7);
  box-shadow: 0 0 20px rgba(var(--neon-blue), 0.5);
}

.neon-purple-border {
  border: 1px solid rgba(var(--neon-purple), 0.5);
  box-shadow: 0 0 15px rgba(var(--neon-purple), 0.3);
}

.futuristic-panel {
  background: rgba(10, 17, 40, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(56, 182, 255, 0.2);
  box-shadow: 0 0 20px rgba(56, 182, 255, 0.1);
}

.cinematic-text {
  letter-spacing: 0.3px;
  line-height: 1.4;
  font-size: 0.95em;
  color: white;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: inherit;
  background: radial-gradient(circle at center, rgba(var(--neon-blue), 0.4) 0%, transparent 70%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
  opacity: 1;
}

/* Exploding character animation */
.exploding-char {
  display: inline-block;
  position: relative;
  transform-origin: center;
  color: white;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Cinematic animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.98);
  }
}

@keyframes scanline {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

.scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(var(--neon-blue), 0.5), transparent);
  opacity: 0.5;
  animation: scanline 8s linear infinite;
  pointer-events: none;
  z-index: 100;
}

.cinematic-fade-in {
  animation: fadeInUp 1s ease forwards;
}

.cinematic-pulse {
  animation: pulse 4s ease-in-out infinite;
}

/* Typing indicator animation */
.typing-indicator span {
  animation: blink 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes blink {
  0% {
    opacity: 0.1;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}

/* Particle animation */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@import url('https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&display=swap');

.font-space-mono {
  font-family: 'Space Mono', monospace;
}

.cinematic-text {
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  letter-spacing: 0.05em;
  line-height: 1.6;
}

@keyframes pulse-glow {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer-animation {
  animation: shimmer 2s infinite linear;
  opacity: 0.5;
}
