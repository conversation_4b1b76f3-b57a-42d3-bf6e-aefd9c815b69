'use client';

import { useState } from 'react';
import { GmailIntegration } from '@/components/gmail-integration';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';

export default function TestGmailPage() {
  const [userId, setUserId] = useState('test-user-123'); // In real app, get from auth
  const [testCommand, setTestCommand] = useState('');
  const [testRecipient, setTestRecipient] = useState('');
  const [testMessage, setTestMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const { toast } = useToast();

  const testVoiceCommand = async () => {
    if (!testCommand.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a voice command to test',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/gmail/voice-handler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          voiceCommand: testCommand
        })
      });

      const data = await response.json();
      setResults(data);

      if (data.success) {
        toast({
          title: 'Success',
          description: data.message
        });
      } else {
        toast({
          title: 'Command Result',
          description: data.message,
          variant: data.needsAuth ? 'destructive' : 'default'
        });
      }
    } catch (error) {
      console.error('Error testing voice command:', error);
      toast({
        title: 'Error',
        description: 'Failed to test voice command',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const testEmailSend = async () => {
    if (!testRecipient.trim() || !testMessage.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter both recipient and message',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/gmail/send-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          recipientEmail: testRecipient,
          message: testMessage
        })
      });

      const data = await response.json();
      setResults(data);

      if (data.success) {
        toast({
          title: 'Email Sent',
          description: `Email sent to ${data.recipient}`
        });
      } else {
        toast({
          title: 'Send Failed',
          description: data.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: 'Error',
        description: 'Failed to send test email',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Gmail Integration Test</h1>
        <p className="text-gray-600 mt-2">Test your Gmail integration with Yubi</p>
      </div>

      {/* Gmail Integration Component */}
      <GmailIntegration userId={userId} />

      {/* Voice Command Test */}
      <Card>
        <CardHeader>
          <CardTitle>Test Voice Commands</CardTitle>
          <CardDescription>
            Test how Yubi interprets email-related voice commands
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Voice Command:</label>
            <Input
              value={testCommand}
              onChange={(e) => setTestCommand(e.target.value)}
              placeholder="e.g., 'summarize my emails' or 'send <NAME_EMAIL> saying hello'"
              className="mt-1"
            />
          </div>
          <Button onClick={testVoiceCommand} disabled={loading}>
            {loading ? 'Testing...' : 'Test Voice Command'}
          </Button>
        </CardContent>
      </Card>

      {/* Direct Email Send Test */}
      <Card>
        <CardHeader>
          <CardTitle>Test Email Sending</CardTitle>
          <CardDescription>
            Directly test email sending functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Recipient Email:</label>
            <Input
              value={testRecipient}
              onChange={(e) => setTestRecipient(e.target.value)}
              placeholder="<EMAIL>"
              className="mt-1"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Message:</label>
            <Textarea
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Your message here..."
              className="mt-1"
            />
          </div>
          <Button onClick={testEmailSend} disabled={loading}>
            {loading ? 'Sending...' : 'Send Test Email'}
          </Button>
        </CardContent>
      </Card>

      {/* Results Display */}
      {results && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(results, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Sample Voice Commands */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Voice Commands</CardTitle>
          <CardDescription>
            Try these example commands to test the integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Email Summary:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• "summarize my emails"</li>
                <li>• "what are my emails today"</li>
                <li>• "check my inbox"</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Send Emails:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• "send email to john saying hello"</li>
                <li>• "email sarah about the meeting"</li>
                <li>• "reply to mike with thanks"</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
