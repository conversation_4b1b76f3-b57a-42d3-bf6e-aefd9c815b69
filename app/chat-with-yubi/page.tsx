"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { BackgroundParticles } from "@/components/background-particles";
import { supabase } from "@/lib/supabase";
import { audioAnalyzer } from "@/lib/audio-analyzer";
import { getEmotionCategory } from "@/lib/emotional-keywords";
import { EmotionalMeter } from "@/components/emotional-meter";

// Define SpeechRecognition type
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface Message {
  role: "user" | "assistant";
  content: string;
}

interface SpeechRecognitionResult {
  isFinal: boolean;
  [index: number]: {
    transcript: string;
  };
}

interface SpeechRecognitionEvent {
  resultIndex: number;
  results: {
    length: number;
    [index: number]: SpeechRecognitionResult;
  };
}

interface PastConversation {
  id: string;
  summary: string;
  emotional_markers: string[];
  topics: string[];
  full_conversation: Message[];
  created_at: string;
  user_id?: string; // Add this field in case it's needed
  conversation_type?: string; // Add this field in case it's needed
}

export default function ChatWithYubi() {
  const [messages, setMessages] = useState<Message[]>([{
    role: "assistant",
    content: "Hey I am Yubi! How are you doing today?"
  }]);
  const [isLoading, setIsLoading] = useState(false);
  const [callActive, setCallActive] = useState(false);
  const [isYubiSpeaking, setIsYubiSpeaking] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const router = useRouter();

  // Add state for user ID
  const [userId, setUserId] = useState<string | null>(null);

  // Add to existing state
  const [conversationTranscript, setConversationTranscript] = useState<Message[]>([{
    role: "assistant",
    content: "Hey I am Yubi! How are you doing today?"
  }]);

  // Add to existing state
  const [pastConversations, setPastConversations] = useState<PastConversation[]>([]);

  // Voice input states
  const [isListening, setIsListening] = useState(false);
  const [speechRecognition, setSpeechRecognition] = useState<any | null>(null);
  const [isVoiceSupported, setIsVoiceSupported] = useState(false);

  // Add these to your existing state variables
  const [emotionalState, setEmotionalState] = useState<any>(null);
  const [emotionalPatterns, setEmotionalPatterns] = useState<Record<string, number>>({});
  const [emotionalDataInterval, setEmotionalDataInterval] = useState<NodeJS.Timeout | null>(null);
  const [conversationStartTime, setConversationStartTime] = useState<number>(0);
  const [emotionalTriggers, setEmotionalTriggers] = useState<string[]>([]);
  const [emotionalThreshold, setEmotionalThreshold] = useState<number>(7);
  const [lastReflectionTime, setLastReflectionTime] = useState<number>(0);
  const [isReflecting, setIsReflecting] = useState<boolean>(false);
  const [pendingMessages, setPendingMessages] = useState<string[]>([]);
  const [callStartTime, setCallStartTime] = useState<number>(0);
  const [callRefreshInterval, setCallRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Check if voice is supported
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      setIsVoiceSupported(true);
    }

    // Create audio element for Yubi's voice
    const audio = new Audio();
    audioRef.current = audio;
    
    // Play initial greeting when call is started
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, []);

  // Add to useEffect to get user ID
  useEffect(() => {
    async function getUserId() {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        console.log("User authenticated:", user.id);
        setUserId(user.id);
        // Fetch past conversations after getting userId
        fetchPastConversations();
      } else {
        console.log("No authenticated user found");
      }
    }
    
    getUserId();
  }, []);

  // Play initial greeting when call is started
  useEffect(() => {
    if (callActive && messages.length === 1) {
      playYubiVoice(messages[0].content);
    }
  }, [callActive, messages]);

  const handleEndConversation = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
    
    // Stop speech recognition if it's active
    if (isListening) {
      stopListening();
    }
    
    // Stop audio analyzer
    audioAnalyzer.stopAnalysis();
    
    // Clear emotional data interval
    if (emotionalDataInterval) {
      clearInterval(emotionalDataInterval);
      setEmotionalDataInterval(null);
    }
    
    // Only save if there's an actual conversation (more than just the initial greeting)
    if (conversationTranscript.length > 1) {
      console.log("Ending conversation with user ID:", userId);
      saveConversation();
    } else {
      console.log("Not saving conversation - too short");
    }
    
    setCallActive(false);
    setMessages([{
      role: "assistant",
      content: "Hey I am Yubi! How are you doing today?"
    }]);
    setConversationTranscript([{
      role: "assistant",
      content: "Hey I am Yubi! How are you doing today?"
    }]);
    
    // Reset emotional states
    setEmotionalState(null);
    setEmotionalPatterns({});
    setEmotionalTriggers([]);
    
    // Clear call refresh interval
    if (callRefreshInterval) {
      clearInterval(callRefreshInterval);
      setCallRefreshInterval(null);
    }
    
    router.push("/dashboard");
  };

  // Function to start voice recognition
  function startListening() {
    if (!isVoiceSupported) return;

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.lang = "en-US";
    recognition.interimResults = true;
    recognition.continuous = true;

    let finalTranscript = '';
    let silenceTimer: NodeJS.Timeout;
    const SILENCE_DURATION = 2000;

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      let interimTranscript = '';
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript = transcript;
        } else {
          interimTranscript += transcript;
        }
      }
      
      // Feed words to audio analyzer for emotional analysis
      audioAnalyzer.addWords(interimTranscript || finalTranscript);

      clearTimeout(silenceTimer);
      silenceTimer = setTimeout(() => {
        if (finalTranscript) {
          handleSendMessage(finalTranscript);
          stopListening();
        }
      }, SILENCE_DURATION);
    };

    recognition.onerror = (event: { error: any }) => {
      console.error("Speech recognition error", event.error);
      setIsListening(false);
    };

    recognition.onend = () => {
      if (isListening) {
        recognition.start();
      }
    };

    recognition.start();
    setSpeechRecognition(recognition);
  }

  // Function to stop voice recognition
  function stopListening() {
    if (speechRecognition) {
      speechRecognition.stop();
      speechRecognition.onend = null; // Remove the onend handler to prevent auto-restart
      setSpeechRecognition(null); // Clear the recognition object
      setIsListening(false);
    }
  }

  // Function to play Yubi's voice response
  const playYubiVoice = async (text: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      try {
        setIsYubiSpeaking(true);
        
        fetch("/api/elevenlabs-tts", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text }),
        })
        .then(response => {
          if (!response.ok) {
            throw new Error("Failed to generate speech");
          }
          return response.blob();
        })
        .then(audioBlob => {
          const audioUrl = URL.createObjectURL(audioBlob);
          
          if (audioRef.current) {
            audioRef.current.src = audioUrl;
            audioRef.current.onended = () => {
              setIsYubiSpeaking(false);
              URL.revokeObjectURL(audioUrl);
              resolve();
            };
            audioRef.current.onerror = (e) => {
              setIsYubiSpeaking(false);
              URL.revokeObjectURL(audioUrl);
              reject(e);
            };
            audioRef.current.play();
          } else {
            setIsYubiSpeaking(false);
            reject(new Error("Audio element not available"));
          }
        })
        .catch(error => {
          console.error("Failed to play voice:", error);
          setIsYubiSpeaking(false);
          reject(error);
        });
      } catch (error) {
        console.error("Failed to play voice:", error);
        setIsYubiSpeaking(false);
        reject(error);
      }
    });
  };

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    setIsLoading(true);
    
    // Add user message to transcript
    const userMessage = { role: "user", content: message.trim() };
    setMessages(prev => [...prev, userMessage as Message]);
    setConversationTranscript(prev => [...prev, userMessage as Message]);

    try {
      // Check if we should respond to emotional triggers or thresholds
      let enhancedMessage = message.trim();
      let shouldAddEmotionalContext = false;
      let shouldTriggerReflection = false;
      
      // Calculate current emotional intensity
      const currentIntensity = emotionalState ? calculateEmotionalIntensity(emotionalState) : 0;
      
      // Check for emotional triggers from pattern detection
      if (emotionalTriggers.length > 0) {
        console.log("Emotional triggers detected:", emotionalTriggers);
        shouldAddEmotionalContext = true;
        
        // Reset triggers after using them
        setEmotionalTriggers([]);
      }
      
      // Check for emotional intensity threshold crossing
      if (currentIntensity > emotionalThreshold) {
        console.log(`Emotional intensity threshold crossed: ${currentIntensity} > ${emotionalThreshold}`);
        shouldAddEmotionalContext = true;
        
        // Only trigger reflection if enough time has passed since last one
        const timeSinceLastReflection = Date.now() - lastReflectionTime;
        if (timeSinceLastReflection > 60000) { // 1 minute minimum between reflections
          shouldTriggerReflection = true;
          setLastReflectionTime(Date.now());
        }
      }
      
      // If we need to trigger a reflection, do it before normal response
      if (shouldTriggerReflection) {
        await triggerEmotionalReflection();
      }
      
      // Log the exact payload being sent
      const payload = { 
        message: enhancedMessage,
        pastConversations: pastConversations,
        emotionalState: shouldAddEmotionalContext ? emotionalState : undefined,
        userId: userId
      };
      console.log("Sending payload to Yubi API:", JSON.stringify(payload, null, 2));
      
      const response = await fetch("/api/chat-with-yubi", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const data = await response.json();
      console.log("Received response from Yubi API:", data);

      if (data.error) {
        throw new Error(data.error);
      }

      const yubiResponse = { role: "assistant", content: data.message };
      
      // If we have pending messages from a reflection, queue this response
      if (isReflecting) {
        setPendingMessages(prev => [...prev, data.message]);
      } else {
        setMessages((prev: Message[]) => [...prev, yubiResponse as Message]);
        setConversationTranscript(prev => [...prev, yubiResponse as Message]);
        
        // Play Yubi's voice response
        playYubiVoice(data.message);
      }

    } catch (error) {
      console.error("Failed to send message:", error);
      const errorMessage = "I'm having trouble responding right now. Could you try again?";
      setMessages(prev => [...prev, { 
        role: "assistant", 
        content: errorMessage
      }]);
      
      if (!isReflecting) {
        playYubiVoice(errorMessage);
      } else {
        setPendingMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const startCall = async () => {
    console.log("startCall called, initializing audio analyzer");
    setCallActive(true);
    setConversationStartTime(Date.now());
    setCallStartTime(Date.now());
    
    // Initialize audio analyzer
    console.log("Attempting to initialize audio analyzer");
    const initialized = await audioAnalyzer.initialize();
    console.log("Audio analyzer initialization result:", initialized);
    
    if (initialized) {
      console.log("Starting audio analysis");
      audioAnalyzer.startAnalysis();
      
      // Set up interval for emotional data collection
      console.log("Setting up emotional data collection interval");
      const interval = setInterval(() => {
        console.log("Emotional data collection interval triggered");
        trackEmotionalData();
      }, 12000); // Every 12 seconds
      setEmotionalDataInterval(interval);
    } else {
      console.error("Failed to initialize audio analyzer");
    }
    
    // Set up keepalive to refresh connection every 4 minutes
    const refreshInterval = setInterval(() => {
      refreshCallConnection();
    }, 240000); // 4 minutes (240,000ms)
    setCallRefreshInterval(refreshInterval);
  };

  // Add this new function to refresh the call connection
  const refreshCallConnection = async () => {
    if (!callActive) return;
    
    console.log("Refreshing call connection to prevent timeout");
    try {
      // Send a minimal request to keep the connection alive
      await fetch("/api/elevenlabs-heartbeat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId }),
      });
    } catch (error) {
      console.error("Failed to refresh call connection:", error);
    }
  };

  // Add this function to track emotional data
  const trackEmotionalData = async () => {
    console.log("trackEmotionalData called, userId:", userId, "callActive:", callActive);
    if (!userId || !callActive) {
      console.log("Skipping emotional tracking: userId or callActive is falsy");
      return;
    }
    
    // Get current audio analysis
    const analysis = audioAnalyzer.analyzeAudio();
    console.log("Audio analysis result:", analysis);
    if (!analysis) {
      console.log("Skipping emotional tracking: No audio analysis available");
      return;
    }
    
    try {
      // Determine primary emotion from keywords using getEmotionCategory
      const primaryEmotion = analysis.keywords.length > 0 
        ? getEmotionCategory(analysis.keywords[0]) || "neutral" 
        : "neutral";
      
      // Calculate intensity (1-10 scale) based on volume and tone
      const intensity = Math.min(10, Math.max(1, Math.round((analysis.volume / 10) + Math.abs(analysis.tone) * 5)));
      
      // Create timestamp
      const timestamp = new Date().toISOString();
      
      // Convert milliseconds to seconds for in_conversation_timestamp to fit in integer range
      const inConversationTimestampSeconds = Math.floor(new Date(timestamp).getTime() / 1000);
      
      console.log("Sending emotional data to API:", {
        userId,
        conversationId: Date.now().toString(),
        emotion: primaryEmotion,
        intensity,
        speechPace: analysis.pace,
        speechVolume: analysis.volume,
        speechTone: analysis.tone,
        emotionalKeywords: analysis.keywords,
        inConversationTimestamp: inConversationTimestampSeconds
      });
      
      // Send emotional data to API
      const response = await fetch("/api/emotional-tracking", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId,
          conversationId: Date.now().toString(),
          emotion: primaryEmotion,
          intensity,
          speechPace: analysis.pace,
          speechVolume: analysis.volume,
          speechTone: analysis.tone,
          emotionalKeywords: analysis.keywords || [],
          in_conversation_timestamp: inConversationTimestampSeconds // Use the seconds value here
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error from emotional tracking API:", errorData);
        return;
      }
      
      const data = await response.json();
      console.log("Emotional tracking response:", data);
      
      // Update emotional state if available
      if (data.emotionalState) {
        setEmotionalState(data.emotionalState);
        
        // Check for emotional patterns
        detectEmotionalPatterns(data.emotionalState, analysis);
      }
    } catch (error) {
      console.error("Error tracking emotional data:", error instanceof Error ? error.message : error);
    }
  };

  // Add this function to detect patterns that should trigger a response
  const detectEmotionalPatterns = (state: any, currentAnalysis: any) => {
    const newTriggers: string[] = [];
    
    // Check for repeated emotional keywords (e.g., "tired" mentioned 3+ times)
    Object.entries(state.keywordCounts || {}).forEach(([keyword, count]) => {
      if ((count as number) >= 3 && !emotionalTriggers.includes(`keyword_${keyword}`)) {
        newTriggers.push(`keyword_${keyword}`);
      }
    });
    
    // Check for significant tone shifts
    if (state.averageTone && Math.abs(parseFloat(state.averageTone) - currentAnalysis.tone) > 0.5) {
      newTriggers.push('tone_shift');
    }
    
    // Check for unusually fast or slow speech
    if (state.averagePace && (currentAnalysis.pace > state.averagePace * 1.5 || 
        currentAnalysis.pace < state.averagePace * 0.5)) {
      newTriggers.push('pace_change');
    }
    
    // If we have new triggers, update state
    if (newTriggers.length > 0) {
      setEmotionalTriggers(prev => [...prev, ...newTriggers]);
    }
  };

  // Add this function to fetch past conversations
  const fetchPastConversations = async () => {
    if (!userId) {
      console.log("Cannot fetch past conversations: userId is null");
      return;
    }
    
    try {
      console.log("Fetching past conversations for user:", userId);
      
      // Log the exact query we're using
      console.log(`Executing query: supabase.from("conversations").select("*").eq("user_id", "${userId}")`);
      
      // Now fetch the conversations with the same query structure as the dashboard
      const { data, error } = await supabase
        .from("conversations")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });
      
      if (error) {
        console.error("Error fetching past conversations:", error);
        throw error;
      }
      
      console.log("Raw response from Supabase:", data);
      
      if (data && data.length > 0) {
        console.log("First conversation details:", {
          id: data[0].id,
          user_id: data[0].user_id,
          created_at: data[0].created_at,
          summary: data[0].summary?.substring(0, 50) + "..."
        });
        
        // Make sure we're setting the state with the correct structure
        setPastConversations(data.map(conv => ({
          id: conv.id,
          summary: conv.summary,
          emotional_markers: conv.emotional_markers || [],
          topics: conv.topics || [],
          full_conversation: conv.full_conversation || [],
          created_at: conv.created_at
        })));
      } else {
        console.log("No conversations found for user:", userId);
        setPastConversations([]);
      }
    } catch (error) {
      console.error("Error in fetchPastConversations:", error);
    }
  };

  // Add this useEffect to monitor pastConversations changes
  useEffect(() => {
    console.log("pastConversations state updated:", pastConversations);
  }, [pastConversations]);

  // Add a useEffect that runs when userId changes
  useEffect(() => {
    if (userId) {
      console.log("userId changed, fetching conversations for:", userId);
      fetchPastConversations();
    }
  }, [userId]);

  // Add this function to directly check what's in the database
  const debugCheckDatabase = async () => {
    if (!userId) return;
    
    console.log("DEBUG: Directly checking database for user:", userId);
    
    try {
      // First check all conversations in the table
      const { data: allConvs, error: allError } = await supabase
        .from("conversations")
        .select("id, user_id, created_at")
        .limit(10);
        
      console.log("DEBUG: All recent conversations in database:", allConvs);
      
      if (allError) {
        console.error("DEBUG: Error fetching all conversations:", allError);
      }
      
      // Then check specifically for this user
      const { data: userConvs, error: userError } = await supabase
        .from("conversations")
        .select("id, user_id, created_at")
        .eq("user_id", userId);
        
      console.log(`DEBUG: Conversations for user ${userId}:`, userConvs);
      
      if (userError) {
        console.error(`DEBUG: Error fetching conversations for user ${userId}:`, userError);
      }
    } catch (error) {
      console.error("DEBUG: Error in debugCheckDatabase:", error);
    }
  };

  // Call this function after getting the userId
  useEffect(() => {
    if (userId) {
      debugCheckDatabase();
    }
  }, [userId]);

  // Add cleanup for speech recognition when component unmounts
  useEffect(() => {
    return () => {
      if (speechRecognition) {
        speechRecognition.stop();
      }
      if (audioRef.current) {
        audioRef.current.pause();
      }
      audioAnalyzer.stopAnalysis();
      if (emotionalDataInterval) {
        clearInterval(emotionalDataInterval);
      }
    };
  }, [speechRecognition, emotionalDataInterval]);

  const saveConversation = async () => {
    try {
      console.log("Saving conversation to database");
      const response = await fetch("/api/summarize-conversation", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          conversation: conversationTranscript,
          userId: userId
        }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to save conversation");
      }
      
      const data = await response.json();
      console.log("Conversation saved successfully:", data);
    } catch (error) {
      console.error("Error saving conversation:", error);
    }
  };

  // Helper function to calculate emotional intensity from state
  const calculateEmotionalIntensity = (state: any): number => {
    if (!state) return 0;
    
    let intensity = 0;
    
    // Factor in dominant emotion intensity
    if (state.dominantEmotion && state.emotionCounts) {
      const emotionCount = state.emotionCounts[state.dominantEmotion] || 0;
      intensity += Math.min(5, emotionCount);
    }
    
    // Factor in speech volume (0-100 scale)
    if (typeof state.averageVolume === 'number') {
      intensity += (state.averageVolume / 20); // Convert to 0-5 scale
    }
    
    // Factor in emotional keywords
    if (state.keywordCounts) {
      const keywordIntensity = Object.values(state.keywordCounts).reduce(
        (sum: number, count: any) => sum + (count as number), 0
      );
      intensity += Math.min(3, keywordIntensity / 2); // Cap at 3
    }
    
    // Factor in tone (from -1 to 1)
    if (typeof state.averageTone === 'string') {
      const tone = parseFloat(state.averageTone);
      intensity += Math.abs(tone) * 2; // Convert to 0-2 scale
    }
    
    // Normalize to 0-10 scale
    return Math.min(10, Math.max(0, intensity));
  };

  // Function to trigger an emotional reflection
  const triggerEmotionalReflection = async () => {
    if (!emotionalState) return;
    
    setIsReflecting(true);
    
    try {
      // Generate a reflection based on emotional state
      const response = await fetch("/api/generate-reflection", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          emotionalState,
          conversationHistory: conversationTranscript.slice(-5), // Last 5 messages
          userId
        }),
      });

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }
      
      // Add reflection to messages
      const reflectionMessage = { role: "assistant", content: data.reflection };
      setMessages(prev => [...prev, reflectionMessage as Message]);
      setConversationTranscript(prev => [...prev, reflectionMessage as Message]);
      
      // Play the reflection
      await playYubiVoice(data.reflection);
      
      // Process any pending messages after reflection
      if (pendingMessages.length > 0) {
        const nextMessage = pendingMessages[0];
        setPendingMessages(prev => prev.slice(1));
        
        // Add to conversation
        setMessages(prev => [...prev, { role: "assistant", content: nextMessage }]);
        setConversationTranscript(prev => [...prev, { role: "assistant", content: nextMessage }]);
        
        // Play the message
        await playYubiVoice(nextMessage);
      }
    } catch (error) {
      console.error("Failed to generate emotional reflection:", error);
    } finally {
      setIsReflecting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A1140] via-[#1A1F35] to-[#0D1225] text-white">
      <BackgroundParticles />
      
      {/* Navigation Buttons */}
      <div className="absolute top-6 left-6 flex items-center gap-4">
        <button 
          onClick={() => router.push("/dashboard")}
          className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Dashboard</span>
        </button>
        {callActive && (
          <button 
            onClick={handleEndConversation}
            className="flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white hover:opacity-90 transition-opacity"
          >
            <X className="w-4 h-4" />
            <span>End Call</span>
          </button>
        )}
      </div>

      {/* Call Container */}
      <div className="flex justify-center items-center min-h-screen p-4">
        <div className="w-full max-w-4xl bg-[rgba(10,17,40,0.95)] backdrop-blur-xl border border-[rgba(56,182,255,0.2)] rounded-lg shadow-2xl overflow-hidden">
          {/* Call Header */}
          <div className="p-4 border-b border-gray-800 bg-[rgba(56,182,255,0.03)]">
            <h1 className="text-xl font-light bg-gradient-to-r from-[#38b6ff] to-[#a137ff] bg-clip-text text-transparent">
              Call with Yubi
            </h1>
          </div>

          {/* Call Interface */}
          <div className="h-[60vh] flex flex-col items-center justify-center p-4">
            {!callActive ? (
              <div className="text-center">
                <div className="mb-8">
                  <h2 className="text-2xl font-light text-white mb-2">Ready to talk with Yubi?</h2>
                  <p className="text-gray-400">Click the button below to start a voice call</p>
                </div>
                <button
                  onClick={startCall}
                  className="p-6 rounded-full bg-gradient-to-r from-[#38b6ff] to-[#a137ff] text-white hover:opacity-90 transition-all duration-300"
                >
                  <Phone className="w-8 h-8" />
                </button>
              </div>
            ) : (
              <div className="text-center">
                <div className="w-32 h-32 rounded-full bg-gradient-to-r from-[#38b6ff] to-[#a137ff] flex items-center justify-center mb-8">
                  <span className="text-4xl">Y</span>
                </div>
                
                <div className="mb-8">
                  {isYubiSpeaking ? (
                    <div className="text-center">
                      <p className="text-xl font-light text-white mb-2">Yubi is speaking...</p>
                      <div className="flex justify-center space-x-1">
                        <div className="w-2 h-8 bg-[#38b6ff] rounded-full animate-pulse" style={{ animationDelay: "0ms" }}></div>
                        <div className="w-2 h-8 bg-[#38b6ff] rounded-full animate-pulse" style={{ animationDelay: "300ms" }}></div>
                        <div className="w-2 h-8 bg-[#38b6ff] rounded-full animate-pulse" style={{ animationDelay: "600ms" }}></div>
                      </div>
                    </div>
                  ) : isListening ? (
                    <p className="text-xl font-light text-white">Listening to you...</p>
                  ) : isLoading ? (
                    <p className="text-xl font-light text-white">Processing...</p>
                  ) : (
                    <p className="text-xl font-light text-white">Ready for your voice</p>
                  )}
                </div>

                {/* Voice Input Button */}
                {isVoiceSupported && !isYubiSpeaking && (
                  <button
                    onClick={isListening ? stopListening : startListening}
                    disabled={isLoading}
                    className={`p-6 rounded-full ${
                      isListening
                        ? "bg-[#a137ff] text-white animate-pulse"
                        : "bg-gradient-to-r from-[#38b6ff] to-[#a137ff] text-white hover:opacity-90"
                    } disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
                    aria-label={isListening ? "Stop recording" : "Start recording"}
                  >
                    {isListening ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
