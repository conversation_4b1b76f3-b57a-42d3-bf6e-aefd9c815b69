"use client"

import React, { useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { BackgroundParticles } from "@/components/background-particles"
import gsap from "gsap"
import { Home, GamepadIcon, Users, Sparkles, FlaskConical } from "lucide-react"
import Link from 'next/link'

function Navbar() {
  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border-b border-[rgba(56,182,255,0.2)]">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-xl font-light text-[#38b6ff]">Yubi</div>
          <div className="flex space-x-6">
            <Link 
              href="/dashboard" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Home className="w-4 h-4 text-[#38b6ff]" />
              <span className="text-white">Dashboard</span>
            </Link>
            <Link 
              href="/game" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <GamepadIcon className="w-4 h-4 text-[#a137ff]" />
              <span className="text-white">Game of Life</span>
            </Link>
            <Link 
              href="/social" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Users className="w-4 h-4 text-[#ff6b37]" />
              <span className="text-white">Resonance Web</span>
            </Link>
            <Link 
              href="/lab" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <FlaskConical className="w-4 h-4 text-[#37fff1]" />
              <span className="text-white">Yubi Lab</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function GamePage() {
  const comingSoonRef = useRef<HTMLDivElement>(null)
  const sparklesRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    if (comingSoonRef.current && sparklesRef.current) {
      // Create a timeline for animations
      const tl = gsap.timeline({
        defaults: {
          duration: 1,
          ease: "power3.out"
        }
      })
      
      // Set initial states
      gsap.set(comingSoonRef.current, {
        opacity: 0,
        y: 30
      })
      
      gsap.set(sparklesRef.current, {
        opacity: 0,
        scale: 0.8
      })
      
      // Animate the coming soon text
      tl.to(comingSoonRef.current, {
        opacity: 1,
        y: 0,
        duration: 1.2
      })
      
      // Animate the sparkles
      tl.to(sparklesRef.current, {
        opacity: 1,
        scale: 1,
        duration: 0.8
      }, "-=0.5")
      
      // Add floating animation to the sparkles
      gsap.to(sparklesRef.current, {
        y: -15,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      })
    }
  }, [])
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A1140] via-[#1A1F35] to-[#0D1225] text-white">
      <BackgroundParticles />
      <Navbar />
      
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div ref={sparklesRef} className="mb-8">
          <motion.div
            animate={{
              rotate: [0, 10, -10, 0],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <GamepadIcon className="w-24 h-24 text-[#a137ff]" />
          </motion.div>
        </div>
        
        <div ref={comingSoonRef} className="text-center max-w-2xl">
          <h1 className="text-5xl font-light mb-6 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] bg-clip-text text-transparent">
            Game of Life Coming Soon
          </h1>
          
          <p className="text-xl text-gray-300 mb-8">
            Yubi is developing an interactive experience to help you explore your life choices and potential futures.
          </p>
          
          <div className="relative px-8 py-4 rounded-lg bg-[rgba(56,182,255,0.05)] border border-[rgba(56,182,255,0.2)]">
            <div className="absolute -top-3 -right-3">
              <Sparkles className="w-6 h-6 text-[#38b6ff] animate-pulse" />
            </div>
            <p className="text-[#38b6ff]">
              "Play through different life scenarios and see how your choices shape your future self."
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
