"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { CinematicIntro } from "@/components/cinematic-intro"
import { HearYubiButton } from "@/components/hear-yubi-button"
import { CreateYubiButton } from "@/components/create-yubi-button"
import { supabase } from "@/lib/supabase"
import { BackgroundParticles } from "@/components/background-particles"
import { gsap } from "gsap"

interface AudioData {
  message: string
  audio: string | null
}

export default function Home() {
  const [audioData, setAudioData] = useState<AudioData | null>(null)
  const [introCompleted, setIntroCompleted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [audioFinished, setAudioFinished] = useState(false)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const hearYubiButtonRef = useRef<HTMLButtonElement>(null)
  const mainContainerRef = useRef<HTMLDivElement>(null)

  // Handle Supabase auth state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "SIGNED_IN" && session) {
        console.log("User signed in:", session.user)
      }
    })

    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [])

  // Animate the Hear Yubi button when intro completes
  useEffect(() => {
    if (introCompleted && hearYubiButtonRef.current) {
      // Create a dramatic entrance animation
      const tl = gsap.timeline()

      // Set initial state
      gsap.set(hearYubiButtonRef.current, {
        opacity: 0,
        scale: 0.8,
        y: 20,
      })

      // Powerful entrance animation
      tl.to(hearYubiButtonRef.current, {
        opacity: 1,
        scale: 1.2,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
      })

      // Scale back with bounce
      tl.to(
        hearYubiButtonRef.current,
        {
          scale: 1,
          duration: 0.7,
          ease: "elastic.out(1, 0.3)",
        },
        "-=0.2",
      )

      // Add a subtle pulse animation
      tl.to(
        hearYubiButtonRef.current,
        {
          boxShadow: "0 0 30px rgba(56, 182, 255, 0.7)",
          repeat: 2,
          yoyo: true,
          duration: 0.8,
        },
        "-=0.2",
      )
    }
  }, [introCompleted])

  const handleHearYubi = async () => {
    setIsLoading(true)
    setError(null)
    setAudioFinished(false)

    try {
      const response = await fetch("/api/hear-yubi")
      const data = await response.json()

      if (data.error) {
        setError(data.error)
        return
      }

      // Create audio URL from base64 data
      const audioUrl = data.audio ? `data:audio/mpeg;base64,${data.audio}` : null

      if (audioUrl) {
        // Create and play audio element
        const audio = new Audio(audioUrl)
        setAudioElement(audio)

        audio.onended = () => {
          setAudioFinished(true)
          
          // Create a timeline for the transition
          const tl = gsap.timeline()
          
          // Fade out the "Hear Yubi" button
          tl.to(hearYubiButtonRef.current, {
            opacity: 0,
            scale: 0.8,
            y: -20,
            duration: 0.5,
            ease: "power2.in"
          })

          // Fade in the "Create Yubi" button with a dramatic entrance
          tl.fromTo(".create-yubi-button",
            {
              opacity: 0,
              y: 30,
              scale: 0.9,
            },
            {
              opacity: 1,
              y: 0,
              scale: 1,
              duration: 0.8,
              ease: "power3.out",
              onComplete: () => {
                // Add a subtle pulse animation
                gsap.to(".create-yubi-button", {
                  boxShadow: "0 0 30px rgba(190,75,255,0.6)",
                  repeat: 2,
                  yoyo: true,
                  duration: 1,
                })
              }
            },
            "-=0.2" // Overlap with previous animation
          )
        }

        audio.onerror = () => {
          console.error("Audio playback failed")
          setError("Audio playback failed. Please try again.")
          setAudioFinished(true) // Still show the button on error
        }

        audio.play().catch((err) => {
          console.error("Failed to play audio:", err)
          setError("Audio playback failed. Please try again.")
          setAudioFinished(true) // Still show the button on error
        })
      }

      setAudioData({
        message: data.message,
        audio: audioUrl
      })
    } catch (error) {
      console.error("Failed to fetch Yubi's response:", error)
      setError("Failed to connect to Yubi. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateYubi = async () => {
    try {
      await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        },
      })
    } catch (error) {
      console.error("Authentication error:", error)
    }
  }

  const handleIntroComplete = () => {
    setIntroCompleted(true)
  }

  return (
    <main className="relative min-h-screen overflow-hidden">
      <BackgroundParticles />
      {/* Scanline effect */}
      <div className="scanline"></div>

      {/* Cinematic Intro */}
      <div className="max-w-2xl w-full z-10">
        <CinematicIntro onComplete={handleIntroComplete} />
      </div>

      {/* Centered Button Container - Using flex and margin auto for perfect centering */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-50">
        {introCompleted && !audioFinished && (
          <HearYubiButton
            ref={hearYubiButtonRef}
            onClick={handleHearYubi}
            isLoading={isLoading}
            className="pointer-events-auto"
          />
        )}

        {audioFinished && (
          <div className="create-yubi-button pointer-events-auto">
            <CreateYubiButton onClick={handleCreateYubi} />
          </div>
        )}
      </div>

      {/* Error Message */}
      {introCompleted && error && (
        <div className="fixed bottom-1/4 left-0 right-0 flex justify-center z-50">
          <motion.p initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-red-400 text-center">
            {error}
          </motion.p>
        </div>
      )}
    </main>
  )
}
