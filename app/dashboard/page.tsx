"use client"

import React, { useEffect, useState, useRef } from "react"
import { motion } from "framer-motion"
import { supabase } from "@/lib/supabase"
import { BackgroundParticles } from "@/components/background-particles"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import gsap from "gsap"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  ReferenceArea,
  ReferenceLine,
  Legend,
  CartesianGrid,
} from "recharts"
import {
  Calendar,
  MessageCircle,
  Brain,
  Timer,
  Award,
  TrendingUp,
  Sparkles,
  Loader2,
  Phone,
  X,
  Home,
  GamepadIcon,
  Heart,
  Users,
  FlaskConical,
  Check,
  HeartPulse,
  RefreshCw
} from "lucide-react"
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useToast } from "@/hooks/use-toast"
// Add Input component import
import { Input } from "@/components/ui/input"

// Add Navbar component
function Navbar() {
  const router = useRouter()
  
  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border-b border-[rgba(56,182,255,0.2)]">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-xl font-light text-[#38b6ff]">Yubi</div>
          <div className="flex space-x-6">
            <Link 
              href="/dashboard" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Home className="w-4 h-4 text-[#38b6ff]" />
              <span className="text-white">Dashboard</span>
            </Link>
            <Link 
              href="/game" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <GamepadIcon className="w-4 h-4 text-[#a137ff]" />
              <span className="text-white">Game of Life</span>
            </Link>
            <Link 
              href="/social" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <Users className="w-4 h-4 text-[#ff6b37]" />
              <span className="text-white">Resonance Web</span>
            </Link>
            <Link 
              href="/lab" 
              className="flex items-center gap-2 px-4 py-2 rounded-full hover:bg-[rgba(56,182,255,0.1)] transition-colors"
            >
              <FlaskConical className="w-4 h-4 text-[#37fff1]" />
              <span className="text-white">Yubi Lab</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

// Import social media icons
import InstagramIcon from "@/components/icons/instagram-icon"
import TikTokIcon from "@/components/icons/tiktok-icon"
import SpotifyIcon from "@/components/icons/spotify-icon"
import TwitterIcon from "@/components/icons/twitter-icon"
import { NeuralProfileCard } from "@/components/neural-profile-card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { EmotionalGrowthAnalyzer } from "@/lib/emotional-growth-analyzer"

function ShimmeringMessage() {
  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="relative px-6 py-3 rounded-full bg-[rgba(10,17,40,0.8)] backdrop-blur-lg border border-[rgba(56,182,255,0.2)]">
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-[rgba(56,182,255,0.2)] to-transparent shimmer-animation"></div>
        <div className="flex items-center gap-2 text-sm text-[#38b6ff]">
          <Sparkles className="w-4 h-4 animate-pulse" />
          <span>Your dashboard will evolve as your Yubi learns more about you</span>
        </div>
      </div>
    </div>
  )
}

interface Conversation {
  id: string;
  created_at: string;
  summary: string;
  emotional_markers: string[];
  topics: string[];
  conversation_type: string;
}

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null)
  const [recentResponses, setRecentResponses] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalConversations: 0,
    streakDays: 0,
    reflectionScore: 0,
    lastActive: "",
    userName: ""
  })
  const [isCallingYubi, setIsCallingYubi] = useState(false)
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [showConversations, setShowConversations] = useState(false)
  const [conversations, setConversations] = useState<Conversation[]>([])
  // Update phone number state
  const [showPhoneModal, setShowPhoneModal] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [isSubmittingPhone, setIsSubmittingPhone] = useState(false)
  const [verificationCode, setVerificationCode] = useState("")
  const [verificationSent, setVerificationSent] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  // Refs
  const welcomeRef = useRef<HTMLDivElement>(null)
  const statsGridRef = useRef<HTMLDivElement>(null)
  const cardsRef = useRef<HTMLDivElement>(null)
  const statsCardsRef = useRef<(HTMLDivElement | null)[]>([])
  const achievementsRef = useRef<HTMLDivElement>(null)
  const mounted = useRef(false)
  const reflectionsCardRef = useRef<HTMLDivElement>(null)
  const growthTrendsCardRef = useRef<HTMLDivElement>(null)
  const activityOverviewRef = useRef<HTMLDivElement>(null)
  const socialMediaRef = useRef<HTMLDivElement>(null)
  const emotionalJourneyCardRef = useRef<HTMLDivElement>(null)
  const neuralProfileRef = useRef<HTMLDivElement>(null)

  const handleCallYubi = () => {
    router.push("/chat-with-yubi");
  };

  // Add handler for phone number submission
  const handlePhoneSubmit = async () => {
    try {
      setIsSubmittingPhone(true);
      
      // Basic validation
      if (!phoneNumber || phoneNumber.length < 10) {
        toast({
          variant: "destructive",
          description: "Please enter a valid phone number"
        });
        setIsSubmittingPhone(false);
        return;
      }
      
      // If verification code hasn't been sent yet, send it
      if (!verificationSent) {
        const response = await fetch("/api/verify-phone", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            phoneNumber,
            action: "send"
          }),
        });
        
        const data = await response.json();
        
        if (data.success) {
          setVerificationSent(true);
          toast({
            description: "Verification code sent to your phone"
          });
        } else {
          toast({
            variant: "destructive",
            description: data.error || "Failed to send verification code"
          });
        }
      } 
      // If verification code has been sent, verify it
      else {
        const response = await fetch("/api/verify-phone", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            phoneNumber,
            code: verificationCode,
            userId: user?.id,
            action: "verify"
          }),
        });
        
        const data = await response.json();
        
        if (data.success && data.status === "approved") {
          toast({
            description: "Phone number verified successfully!"
          });
          setShowPhoneModal(false);
          setPhoneNumber("");
          setVerificationCode("");
          setVerificationSent(false);
        } else {
          toast({
            variant: "destructive",
            description: "Invalid verification code. Please try again."
          });
        }
      }
    } catch (error) {
      console.error("Error verifying phone number:", error);
      toast({
        variant: "destructive",
        description: "Failed to verify phone number. Please try again."
      });
    } finally {
      setIsSubmittingPhone(false);
    }
  };

  // First, load the data
  useEffect(() => {
    loadDashboardData()
    mounted.current = true
    
    return () => {
      mounted.current = false
    }
  }, [])

  // Then, handle animations after data is loaded
  useEffect(() => {
    if (isLoading || !mounted.current) return

    // Kill any existing GSAP animations
    gsap.killTweensOf([
      welcomeRef.current,
      statsCardsRef.current,
      cardsRef.current?.children,
      achievementsRef.current
    ])

    const tl = gsap.timeline({
      defaults: {
        duration: 1,
        ease: "power3.out"
      }
    })

    // Set initial states
    gsap.set([
      welcomeRef.current, 
      statsCardsRef.current,
      reflectionsCardRef.current,
      growthTrendsCardRef.current,
      socialMediaRef.current,
      achievementsRef.current
    ], {
      opacity: 0,
      y: 30
    })

    // Welcome section animation
    tl.to(welcomeRef.current, {
      opacity: 1,
      y: 0,
    })

    // Stats cards stagger animation
    tl.to(statsCardsRef.current, {
      opacity: 1,
      y: 0,
      stagger: 0.1,
      ease: "back.out(1.7)",
    }, "-=0.5")

    // Reflections card animation
    tl.to(reflectionsCardRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.3")

    // Growth trends card animation
    tl.to(growthTrendsCardRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.5")

    // Emotional journey card animation
    tl.to(emotionalJourneyCardRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.5")

    // Neural profile card animation
    tl.to(neuralProfileRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.5")

    // Social media card animation
    tl.to(socialMediaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.5")

    // Activity overview card animation
    tl.to(activityOverviewRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
    }, "-=0.3")

    // Achievements section animation
    tl.to(achievementsRef.current, {
      opacity: 1,
      y: 0,
    }, "-=0.3")

    // Add hover animations for cards
    const cleanupHoverEffects = () => {
      statsCardsRef.current.forEach(card => {
        if (card) {
          const enterHandler = () => {
            gsap.to(card, {
              scale: 1.02,
              duration: 0.3,
              ease: "power2.out",
              boxShadow: "0 0 30px rgba(56, 182, 255, 0.3)",
            })
          }

          const leaveHandler = () => {
            gsap.to(card, {
              scale: 1,
              duration: 0.3,
              ease: "power2.out",
              boxShadow: "none",
            })
          }

          card.addEventListener("mouseenter", enterHandler)
          card.addEventListener("mouseleave", leaveHandler)

          return () => {
            card.removeEventListener("mouseenter", enterHandler)
            card.removeEventListener("mouseleave", leaveHandler)
          }
        }
      })
    }

    cleanupHoverEffects()

    // Cleanup function
    return () => {
      if (mounted.current) {
        gsap.killTweensOf([
          welcomeRef.current,
          statsCardsRef.current,
          cardsRef.current?.children,
          achievementsRef.current
        ])
        cleanupHoverEffects()
      }
    }
  }, [isLoading])

  useEffect(() => {
    console.log("Conversations state updated:", conversations);
  }, [conversations]);

  async function loadDashboardData() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return

      setUser(session.user)

      // Fetch the user's name from yubi_responses
      const { data: nameResponse } = await supabase
        .from("yubi_responses")
        .select("response")
        .eq("user_id", session.user.id)
        .eq("question_id", "name")
        .single()

      // Get conversation count
      const { count: conversationCount } = await supabase
        .from("conversations")
        .select("id", { count: "exact", head: true })
        .eq("user_id", session.user.id)

      const { data: responses } = await supabase
        .from("yubi_responses")
        .select("*")
        .eq("user_id", session.user.id)
        .order("created_at", { ascending: false })
        .limit(5)

      setRecentResponses(responses || [])
      setStats({
        totalConversations: conversationCount || 0,
        streakDays: 3,
        reflectionScore: 85,
        lastActive: "Today",
        userName: nameResponse?.response || "there"
      })

      // Fetch conversations
      const { data: conversationsData, error } = await supabase
        .from("conversations")
        .select("*")
        .eq("user_id", session.user.id)
        .order("created_at", { ascending: false })
        .limit(5);      
      if (error) {
        console.error("Error fetching conversations:", error);
      } else {
        console.log("Fetched conversations:", conversationsData);
        setConversations(conversationsData || []);
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0A1140] via-[#1A1F35] to-[#0D1225] text-white flex items-center justify-center">
        <div className="text-[#38b6ff]">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0A1140] via-[#1A1F35] to-[#0D1225] text-white p-6">
      <BackgroundParticles />
      <Navbar />
      <div className="max-w-7xl mx-auto space-y-8 pt-20">
        {/* Welcome Section */}
        <div
          ref={welcomeRef}
          className="mb-8 relative opacity-0"
        >
          <div className="absolute -top-10 -left-10 w-40 h-40 bg-[#38b6ff] rounded-full filter blur-[100px] opacity-20" />
          <h1 className="text-4xl font-light mb-3 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] bg-clip-text text-transparent">
            Welcome back, {stats.userName}
          </h1>
          <p className="text-gray-400 text-lg">
            Your journey of self-discovery continues
          </p>
        </div>

        {/* Stats Grid */}
        <div ref={statsGridRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              title: "Total Conversations",
              value: stats.totalConversations,
              icon: <MessageCircle className="w-5 h-5" />,
              color: "#38b6ff",
              onClick: () => {
                console.log("Opening conversations modal. Available conversations:", conversations);
                setShowConversations(true);
              }
            },
            {
              title: "Reflection Streak",
              value: `${stats.streakDays} days`,
              icon: <Timer className="w-5 h-5" />,
              color: "#a137ff"
            },
            {
              title: "Reflection Score",
              value: `${stats.reflectionScore}%`,
              icon: <Brain className="w-5 h-5" />,
              color: "#ff37a6"
            },
            {
              title: "Last Active",
              value: stats.lastActive,
              icon: <Calendar className="w-5 h-5" />,
              color: "#37fff1"
            }
          ].map((stat, index) => (
            <StatsCard
              key={stat.title}
              {...stat}
              ref={(el: HTMLDivElement | null) => {
                if (statsCardsRef.current) {
                  statsCardsRef.current[index] = el;
                }
              }}
            />
          ))}
        </div>

        {/* Main Content Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div ref={reflectionsCardRef} className="opacity-0">
            <ReflectionsCard responses={recentResponses} />
          </div>
          <div ref={growthTrendsCardRef} className="opacity-0">
            <GrowthTrendsCard />
          </div>
        </div>

        {/* Emotional Journey Section */}
        <div ref={emotionalJourneyCardRef} className="opacity-0">
          <EmotionalJourneyCard />
        </div>

        {/* Neural Profile Section */}
        <div ref={neuralProfileRef} className="opacity-0">
          <NeuralProfileCard />
        </div>

        {/* Social Media Integration Section */}
        <div className="opacity-0" ref={socialMediaRef}>
          <SocialMediaCard />
        </div>

        {/* Activity Overview Section */}
        <div ref={activityOverviewRef} className="opacity-0">
          <ActivityOverviewCard />
        </div>

        {/* Achievements Section */}
        <div ref={achievementsRef} className="opacity-0">
          <AchievementsCard />
        </div>
      </div>
      <ShimmeringMessage />
      <div className="fixed bottom-20 right-6 z-50 flex flex-col gap-3">
        <button
          onClick={() => setShowPhoneModal(true)}
          className="px-6 py-3 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full 
                     flex items-center gap-2 text-white font-medium
                     hover:scale-105 transition-transform"
        >
          <Phone className="w-5 h-5" />
          <span>Connect Phone Number</span>
        </button>
        <button
          onClick={handleCallYubi}
          disabled={isCallingYubi}
          className="px-6 py-3 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full 
                     flex items-center gap-2 text-white font-medium
                     hover:scale-105 transition-transform disabled:opacity-70"
        >
          {isCallingYubi ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              <span>Still incorporating...</span>
            </>
          ) : (
            <>
              <Phone className="w-5 h-5" />
              <span>Call Yubi</span>
            </>
          )}
        </button>
      </div>
      {/* Conversations Modal */}
      {showConversations && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-[rgba(10,17,40,0.95)] backdrop-blur-xl border border-[rgba(56,182,255,0.2)] 
                         rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-light text-[#38b6ff] flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Conversation History
              </h2>
              <button 
                onClick={() => setShowConversations(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              {conversations.length > 0 ? (
                conversations.map((conv: Conversation) => (
                  <div
                    key={conv.id}
                    onClick={() => {
                      console.log("Selected conversation:", conv);
                      setSelectedConversation(conv);
                    }}
                    className="p-4 rounded-lg bg-[rgba(56,182,255,0.03)] border-l-2 border-[#38b6ff] 
                             cursor-pointer hover:bg-[rgba(56,182,255,0.08)] transition-all duration-300"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <span className="text-[#38b6ff] text-sm">
                        {new Date(conv.created_at).toLocaleDateString()} {new Date(conv.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </span>
                      <span className="text-xs px-2 py-1 rounded-full bg-[rgba(56,182,255,0.1)]">
                        {conv.conversation_type}
                      </span>
                    </div>
                    <p className="text-gray-300 mb-3">{conv.summary}</p>
                    <div className="flex flex-wrap gap-2">
                      {conv.emotional_markers && conv.emotional_markers.map((emotion: string, index: number) => (
                        <span 
                          key={`${conv.id}-emotion-${index}`}
                          className="text-xs px-2 py-1 rounded-full bg-[rgba(161,55,255,0.1)] text-[#a137ff]"
                        >
                          {emotion}
                        </span>
                      ))}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <MessageCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No conversations yet</p>
                  <p className="text-sm mt-2">Start a call with Yubi to see your history here</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Phone Number Modal */}
      {showPhoneModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-[rgba(10,17,40,0.95)] backdrop-blur-xl border border-[rgba(56,182,255,0.2)] 
                         rounded-lg p-6 max-w-md w-full relative">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-light text-[#38b6ff] flex items-center gap-2">
                <Phone className="w-5 h-5" />
                {verificationSent ? "Verify Your Phone" : "Connect Your Phone"}
              </h2>
              <button 
                onClick={() => {
                  setShowPhoneModal(false);
                  setVerificationSent(false);
                  setVerificationCode("");
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <p className="text-gray-300 mb-6">
              {verificationSent 
                ? "Enter the verification code sent to your phone."
                : "Connect your phone number to receive notifications and continue conversations with Yubi on the go."}
            </p>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="phone-number" className="block text-sm text-gray-400 mb-2">
                  Phone Number
                </label>
                <Input
                  id="phone-number"
                  type="tel"
                  placeholder="+****************"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  disabled={verificationSent}
                  className="w-full p-4 bg-[rgba(10,17,40,0.4)] border border-[rgba(56,182,255,0.3)]
                           rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#38b6ff]"
                />
              </div>
              
              {verificationSent && (
                <div>
                  <label htmlFor="verification-code" className="block text-sm text-gray-400 mb-2">
                    Verification Code
                  </label>
                  <Input
                    id="verification-code"
                    type="text"
                    placeholder="123456"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="w-full p-4 bg-[rgba(10,17,40,0.4)] border border-[rgba(56,182,255,0.3)]
                             rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#38b6ff]"
                  />
                </div>
              )}
              
              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => {
                    setShowPhoneModal(false);
                    setVerificationSent(false);
                    setVerificationCode("");
                  }}
                  className="px-4 py-2 rounded-lg border border-[rgba(56,182,255,0.3)]
                           text-gray-300 hover:bg-[rgba(56,182,255,0.1)] transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePhoneSubmit}
                  disabled={isSubmittingPhone || (verificationSent && !verificationCode)}
                  className="px-4 py-2 bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-lg
                           text-white hover:opacity-90 transition-opacity disabled:opacity-70
                           flex items-center gap-2"
                >
                  {isSubmittingPhone ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>{verificationSent ? "Verifying..." : "Sending..."}</span>
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4" />
                      <span>{verificationSent ? "Verify" : "Send Code"}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface StatsCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  color: string
  onClick?: () => void
}

const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ title, value, icon, color, onClick }, ref) => {
    return (
      <Card
        ref={ref}
        onClick={onClick}
        className={`p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[1px] border-[rgba(56,182,255,0.1)] 
                   hover:border-[rgba(56,182,255,0.3)] transition-all duration-300
                   ${onClick ? 'cursor-pointer' : ''}`}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-gray-400">{title}</h3>
          <div style={{ color }} className="opacity-80">
            {icon}
          </div>
        </div>
        <p className="text-2xl font-light" style={{ color }}>
          {value}
        </p>
      </Card>
    )
  }
)

// Add display name for the forwarded ref component
StatsCard.displayName = 'StatsCard'

function ReflectionsCard({ responses }: { responses: any[] }) {
  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#38b6ff]">
        <Brain className="w-5 h-5" />
        Your Yubi
      </h2>
      <div className="space-y-4">
        {responses.map((response) => (
          <motion.div
            key={response.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="p-4 rounded-lg bg-[rgba(56,182,255,0.03)] border-l-2 border-[#38b6ff]"
          >
            <p className="text-[#38b6ff] text-sm mb-2">
              {formatQuestionId(response.question_id)}
            </p>
            <p className="text-gray-300">{response.response}</p>
          </motion.div>
        ))}
      </div>
    </Card>
  )
}

function GrowthTrendsCard() {
  const chartData = [
    { day: "Mon", score: 65, engagement: 45 },
    { day: "Tue", score: 70, engagement: 52 },
    { day: "Wed", score: 75, engagement: 58 },
    { day: "Thu", score: 72, engagement: 63 },
    { day: "Fri", score: 85, engagement: 75 },
    { day: "Sat", score: 82, engagement: 71 },
    { day: "Sun", score: 90, engagement: 85 },
  ]

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#a137ff]">
        <TrendingUp className="w-5 h-5" />
        Growth Trends
      </h2>
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="colorScore" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#38b6ff" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#38b6ff" stopOpacity={0}/>
              </linearGradient>
              <linearGradient id="colorEngagement" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#a137ff" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#a137ff" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <XAxis dataKey="day" stroke="#38b6ff" />
            <YAxis stroke="#38b6ff" />
            <Tooltip
              contentStyle={{
                backgroundColor: "rgba(10,17,40,0.9)",
                border: "1px solid rgba(56,182,255,0.2)",
                borderRadius: "8px",
              }}
            />
            <Area
              type="monotone"
              dataKey="score"
              stroke="#38b6ff"
              fillOpacity={1}
              fill="url(#colorScore)"
            />
            <Area
              type="monotone"
              dataKey="engagement"
              stroke="#a137ff"
              fillOpacity={1}
              fill="url(#colorEngagement)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </Card>
  )
}

// Define emotion colors mapping as a type outside the component
type EmotionColorMap = Record<string, string>;

// Define emotion colors mapping as a constant outside the component
const emotionColors: EmotionColorMap = {
  "joy": "#FFD700",        // Gold
  "sadness": "#4169E1",    // Royal Blue
  "anger": "#FF4500",      // Red Orange
  "fear": "#800080",       // Purple
  "surprise": "#00FFFF",   // Cyan
  "trust": "#32CD32",      // Lime Green
  "anticipation": "#FFA500", // Orange
  "disgust": "#8B4513",    // Saddle Brown
  "neutral": "#CCCCCC",    // Gray
  "confusion": "#9370DB",  // Medium Purple
  "fatigue": "#708090",    // Slate Gray
  // Add colors for the custom emotions from Gemini
  "openness": "#3CB371",   // Medium Sea Green
  "freedom": "#9370DB",    // Medium Purple
  "good": "#32CD32",       // Lime Green
  "sad": "#4169E1",        // Royal Blue (same as sadness)
  // Add more vibrant colors for any other emotions Gemini might generate
  "happiness": "#FFD700",  // Same as joy
  "excited": "#FF1493",    // Deep Pink
  "calm": "#40E0D0",       // Turquoise
  "curious": "#DA70D6",    // Orchid
  "hopeful": "#98FB98",    // Pale Green
  "grateful": "#DDA0DD",   // Plum
  "love": "#FF69B4",       // Hot Pink
  "content": "#87CEEB",    // Sky Blue
  "anxious": "#9932CC",    // Dark Orchid
  "worried": "#6A5ACD",    // Slate Blue
  // Handle capitalization variations
  "Openness": "#3CB371",
  "Good": "#32CD32",
  "Freedom": "#9370DB",
  "Sad": "#4169E1",
  "Joy": "#FFD700",
  "Sadness": "#4169E1",
  "Anger": "#FF4500",
  "Fear": "#800080",
  "Surprise": "#00FFFF",
  "Trust": "#32CD32",
  "Anticipation": "#FFA500",
  "Disgust": "#8B4513",
  "Neutral": "#CCCCCC",
  "Confusion": "#9370DB",
  "Fatigue": "#708090",
  "Happiness": "#FFD700",
  "Excited": "#FF1493",
  "Calm": "#40E0D0",
  "Curious": "#DA70D6",
  "Hopeful": "#98FB98",
  "Grateful": "#DDA0DD",
  "Love": "#FF69B4",
  "Content": "#87CEEB",
  "Anxious": "#9932CC",
  "Worried": "#6A5ACD"
};

// Add a default color function to handle unknown emotions
const getEmotionColor = (emotion: string): string => {
  if (!emotion) return "#ff37a6"; // Default pink for null/undefined
  
  // Check if we have this emotion in our mapping
  const normalizedEmotion = emotion.toLowerCase();
  if (emotionColors[normalizedEmotion]) return emotionColors[normalizedEmotion];
  if (emotionColors[emotion]) return emotionColors[emotion];
  
  // Try to find similar emotions using word roots
  // For example, "joyful" should match with "joy"
  const emotionRoots = {
    "joy": ["happy", "happi", "cheer", "delight", "excit", "ecsta", "glad", "pleas", "content"],
    "sadness": ["sad", "unhappy", "depress", "gloomy", "miser", "down", "blue", "upset", "disappoint"],
    "anger": ["ang", "mad", "furi", "enrag", "irritat", "annoy", "frustrat", "rage"],
    "fear": ["fear", "afraid", "scare", "fright", "terr", "anxi", "nerv", "worr", "panic"],
    "surprise": ["surpris", "shock", "amaz", "astonish", "stun", "startl"],
    "trust": ["trust", "believ", "faith", "confid", "sure", "certain", "rely"],
    "anticipation": ["anticipat", "expect", "hope", "look forward"],
    "disgust": ["disgust", "revolt", "repuls", "sicken", "loath", "hate"],
    "love": ["love", "adore", "fond", "affection"],
    "calm": ["calm", "relax", "tranquil", "peace", "serene"],
    "curious": ["curio", "inquisit", "wonder", "interest"],
    "grateful": ["grate", "thank", "appreciat"]
  };
  
  // Check if the emotion contains any root words
  for (const [baseEmotion, roots] of Object.entries(emotionRoots)) {
    if (roots.some(root => normalizedEmotion.includes(root))) {
      // Use the color of the base emotion
      return emotionColors[baseEmotion] || emotionColors[baseEmotion.charAt(0).toUpperCase() + baseEmotion.slice(1)];
    }
  }
  
  // Group emotions by semantic categories for consistent coloring
  const emotionCategories = {
    "positive": ["good", "great", "excellent", "wonderful", "fantastic", "amazing", "awesome", 
                "brilliant", "fabulous", "splendid", "marvelous", "superb", "terrific", 
                "outstanding", "remarkable", "phenomenal", "impressive", "exceptional"],
    "negative": ["bad", "terrible", "awful", "horrible", "dreadful", "poor", "unpleasant", 
                "disappointing", "unfortunate", "unfavorable", "disagreeable", "unsatisfactory"],
    "energy": ["energetic", "vigorous", "dynamic", "lively", "vibrant", "active", "spirited", 
              "enthusiastic", "passionate", "fervent", "intense", "powerful", "strong"],
    "calmness": ["peaceful", "quiet", "still", "gentle", "mild", "soft", "soothing", 
                "relaxing", "comforting", "restful", "tranquil", "serene"]
  };
  
  // Check if the emotion belongs to a category
  for (const [category, words] of Object.entries(emotionCategories)) {
    if (words.some(word => normalizedEmotion.includes(word))) {
      // Use a consistent color for the category
      switch(category) {
        case "positive": return "#32CD32"; // Lime Green
        case "negative": return "#FF4500"; // Red Orange
        case "energy": return "#FF1493";   // Deep Pink
        case "calmness": return "#40E0D0";  // Turquoise
      }
    }
  }
  
  // If not found, generate a deterministic color based on the emotion string
  // This ensures the same emotion always gets the same color
  let hash = 0;
  for (let i = 0; i < emotion.length; i++) {
    hash = emotion.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // Get all existing colors from the emotionColors object
  const existingColors = new Set(Object.values(emotionColors));
  
  // Create a list of vibrant colors that aren't in our predefined list
  const uniqueColorHues = [
    15, 45, 75, 105, 135, 165, 195, 225, 255, 285, 315, 345 // Hues spread across color wheel
  ];
  
  // Generate a color based on the hash
  let newColor;
  let attempts = 0;
  const maxAttempts = 20; // Prevent infinite loop
  
  do {
    // Pick a hue based on the hash
    const hueIndex = (Math.abs(hash) + attempts) % uniqueColorHues.length;
    const h = uniqueColorHues[hueIndex];
    
    // Vary saturation and lightness slightly based on the hash
    const s = 75 + ((Math.abs(hash) + attempts) % 15); // 75-90% saturation
    const l = 55 + ((Math.abs(hash) + attempts) % 15); // 55-70% lightness
    
    newColor = `hsl(${h}, ${s}%, ${l}%)`;
    attempts++;
    
    // Convert HSL to hex for comparison with existing colors
    // This is a simplified conversion and might not be perfect
    const hslToHex = (h: number, s: number, l: number): string => {
      l /= 100;
      const a = s * Math.min(l, 1 - l) / 100;
      const f = (n: number) => {
        const k = (n + h / 30) % 12;
        const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
        return Math.round(255 * color).toString(16).padStart(2, '0');
      };
      return `#${f(0)}${f(8)}${f(4)}`;
    };
    
    const hexColor = hslToHex(h, s, l).toUpperCase();
    
    // If we've tried too many times or found a unique color, break the loop
    if (attempts >= maxAttempts || !existingColors.has(hexColor)) {
      break;
    }
  } while (true);
  
  return newColor;
};

// Create an interface for your emotion data
interface EmotionalDataPoint {
  date: string;
  [key: string]: number | string; // Allow dynamic emotion keys
}

function EmotionalJourneyCard() {
  const [metricType, setMetricType] = useState<string>('intensity');
  const [selectedEmotions, setSelectedEmotions] = useState<string[]>([]);
  const [emotionData, setEmotionData] = useState<EmotionalDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [insightHighlight, setInsightHighlight] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [availableEmotions, setAvailableEmotions] = useState<string[]>([]);
  const { toast } = useToast();
  
  useEffect(() => {
    // Fetch real emotional data from Supabase
    const fetchEmotionalData = async () => {
      try {
        setIsLoading(true);
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          setIsLoading(false);
          return;
        }
        
        // Fetch emotional data with a larger limit and ensure we're getting all fields
        const { data, error } = await supabase
          .from("emotional_tracking")
          .select("*") // Select all fields to ensure we get everything
          .eq("user_id", session.user.id)
          .order("timestamp", { ascending: true })
          .limit(50); // Increased limit to get more data points
        
        if (error) {
          console.error("Error fetching emotional data:", error);
          setIsLoading(false);
          return;
        }
        
        console.log("Fetched emotional data:", data?.length || 0, "records");
        console.log("Sample data point:", data && data.length > 0 ? data[0] : "No data");
        
        let formattedData: EmotionalDataPoint[];
        if (data && data.length > 0) {
          // Use the analyzer's visualization method instead
          formattedData = EmotionalGrowthAnalyzer.getEmotionalDataForVisualization(data);
          console.log("Formatted data for chart:", formattedData.length, "points");
          
          // Extract available emotions from the formatted data
          const availableEmotions = new Set<string>();
          formattedData.forEach(point => {
            Object.keys(point).forEach(key => {
              if (key.endsWith('_intensity')) {
                const emotion = key.split('_')[0];
                availableEmotions.add(emotion);
              }
            });
          });
          
          setAvailableEmotions(Array.from(availableEmotions));
          setSelectedEmotions(Array.from(availableEmotions).slice(0, 4));
        } else {
          // Fallback to mock data
          console.log("No emotional data found, using mock data");
          formattedData = generateMockEmotionalData();
          
          // Extract available emotions from mock data
          const mockEmotions = new Set<string>();
          formattedData.forEach(point => {
            Object.keys(point).forEach(key => {
              if (key !== 'date') {
                const emotion = key.split('_')[0];
                mockEmotions.add(emotion);
              }
            });
          });
          
          setAvailableEmotions(Array.from(mockEmotions));
          setSelectedEmotions(Array.from(mockEmotions).slice(0, 4));
        }
        
        setEmotionData(formattedData);
        
        // Generate insight based on real data or mock data
        const insight = generateInsightHighlight(formattedData);
        setInsightHighlight(insight || null);
        
        setIsLoading(false);
      } catch (error) {
        console.error("Error in fetchEmotionalData:", error);
        const mockData = generateMockEmotionalData();
        setEmotionData(mockData);
        setIsLoading(false);
      }
    };
    
    fetchEmotionalData();
  }, []);

  // Function to manually generate emotional journey data
  const handleGenerateEmotionalJourney = async () => {
    try {
      setIsGenerating(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          variant: "destructive",
          description: "Please sign in to generate emotional journey data"
        });
        setIsGenerating(false);
        return;
      }
      
      // Fetch recent conversations
      const { data: conversations, error: convError } = await supabase
        .from("conversations")
        .select("id, summary, emotional_markers, created_at")
        .eq("user_id", session.user.id)
        .order("created_at", { ascending: false })
        .limit(10);
      
      if (convError) {
        console.error("Error fetching conversations:", convError);
        toast({
          variant: "destructive",
          description: "Failed to fetch conversations"
        });
        setIsGenerating(false);
        return;
      }
      
      if (!conversations || conversations.length === 0) {
        toast({ description: "No conversations found to analyze" });
        setIsGenerating(false);
        return;
      }
      
      // Process each conversation to generate emotional data
      for (const conv of conversations) {
        // Extract emotions from emotional_markers
        if (conv.emotional_markers && conv.emotional_markers.length > 0) {
          for (const emotion of conv.emotional_markers) {
            try {
              // Generate a random intensity between 5-9 for each emotion
              const intensity = 5 + Math.floor(Math.random() * 5);
              
              // Create timestamp if not available
              const timestamp = conv.created_at || new Date().toISOString();
              
              // Convert milliseconds to seconds for in_conversation_timestamp to fit in integer range
              const inConversationTimestampSeconds = Math.floor(new Date(timestamp).getTime() / 1000);
              
              // Insert emotional tracking data with proper error handling
              const { error: insertError } = await supabase
                .from("emotional_tracking")
                .insert({
                  user_id: session.user.id,
                  conversation_id: conv.id,
                  emotion: emotion.toLowerCase(),
                  intensity: intensity,
                  speech_pace: 120 + Math.floor(Math.random() * 60),
                  speech_volume: 50 + Math.floor(Math.random() * 50),
                  speech_tone: -0.5 + Math.random(),
                  emotional_keywords: [], 
                  in_conversation_timestamp: inConversationTimestampSeconds, // Store as seconds
                  timestamp: timestamp
                });
              
              if (insertError) {
                console.error("Error inserting emotional data:", insertError.message || insertError);
              }
            } catch (err) {
              console.error("Exception during emotional data insert:", err);
            }
          }
        }
      }
      
      // Refresh the data
      const { data, error } = await supabase
        .from("emotional_tracking")
        .select("emotion, intensity, speech_pace, speech_volume, speech_tone, timestamp")
        .eq("user_id", session.user.id)
        .order("timestamp", { ascending: true })
        .limit(14);
      
      if (error) {
        console.error("Error fetching updated emotional data:", error);
      } else if (data && data.length > 0) {
        const formattedData = formatEmotionalData(data);
        setEmotionData(formattedData);
        
        // Generate insight based on real data
        const insight = generateInsightHighlight(formattedData);
        setInsightHighlight(insight || null);
        
        toast({ description: "Emotional journey data generated successfully!" });
      } else {
        toast({
          description: "No emotional data was generated",
          variant: "destructive"
        });
      }
      
      setIsGenerating(false);
    } catch (error) {
      console.error("Error generating emotional journey:", error);
      toast({
        variant: "destructive",
        description: "Failed to generate emotional journey data"
      });
      setIsGenerating(false);
    }
  };
  
  // Get metric label
  const getMetricLabel = () => {
    switch (metricType) {
      case 'intensity': return 'Emotional Intensity (1-10)';
      case 'pace': return 'Speech Pace (words/min)';
      case 'volume': return 'Speech Volume (0-100)';
      case 'tone': return 'Emotional Tone (-1 to 1)';
      default: return '';
    }
  };
  
  // Get domain for Y axis based on metric
  const getYDomain = () => {
    switch (metricType) {
      case 'intensity': return [0, 10];
      case 'pace': return [0, 200];
      case 'volume': return [0, 100];
      case 'tone': return [-1, 1];
      default: return [0, 10];
    }
  };

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-light flex items-center gap-2 text-[#ff37a6]">
          <HeartPulse className="w-5 h-5" />
          Emotional Journey
        </h2>
        <div className="flex items-center gap-3">
          {/* Metric selector */}
          <Select value={metricType} onValueChange={setMetricType}>
            <SelectTrigger className="w-[180px] bg-[rgba(255,55,166,0.1)] border-[rgba(255,55,166,0.2)] text-sm">
              <SelectValue placeholder="Select metric" />
            </SelectTrigger>
            <SelectContent className="bg-[#0A1140] border-[rgba(255,55,166,0.2)]">
              <SelectItem value="intensity">Emotional Intensity</SelectItem>
              <SelectItem value="pace">Speech Pace</SelectItem>
              <SelectItem value="volume">Speech Volume</SelectItem>
              <SelectItem value="tone">Emotional Tone</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Insight highlight banner */}
      {insightHighlight && !isLoading && (
        <div className="mb-6 p-3 rounded-lg bg-gradient-to-r from-[rgba(56,182,255,0.1)] to-[rgba(255,55,166,0.1)] border-l-2 border-[#ff37a6]">
          <div className="flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-[#ff37a6]" />
            <p className="text-sm text-gray-300">{insightHighlight}</p>
          </div>
        </div>
      )}
      
      {isLoading ? (
        <div className="h-[300px] flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="w-8 h-8 text-[#ff37a6] animate-spin mb-2" />
            <p className="text-sm text-gray-400">Analyzing your emotional patterns...</p>
          </div>
        </div>
      ) : emotionData.length > 0 ? (
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={emotionData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(56,182,255,0.1)" />
              <XAxis 
                dataKey="date" 
                stroke="#38b6ff"
                tickFormatter={(value, index) => {
                  // Only show some dates to avoid overcrowding
                  if (index === 0 || index === emotionData.length - 1 || index % Math.ceil(emotionData.length / 5) === 0) {
                    return value;
                  }
                  return '';
                }}
              />
              <YAxis stroke="#38b6ff" domain={[0, 10]} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(10,17,40,0.9)', 
                  border: '1px solid rgba(56,182,255,0.3)',
                  borderRadius: '8px',
                  color: 'white'
                }}
                formatter={(value, name) => {
                  // Format the tooltip to show emotion name without "_intensity" suffix
                  const emotion = typeof name === 'string' ? name.split('_')[0] : String(name);
                  return [Number(value).toFixed(1), emotion.charAt(0).toUpperCase() + emotion.slice(1)];
                }}
              />
              <Legend 
                formatter={(value) => {
                  // Format legend to show emotion name without "_intensity" suffix
                  const emotion = typeof value === 'string' ? value.split('_')[0] : String(value);
                  return emotion.charAt(0).toUpperCase() + emotion.slice(1);
                }}
              />
              {selectedEmotions.map((emotion, index) => (
                <Line
                  key={emotion}
                  type="monotone"
                  dataKey={`${emotion}_${metricType}`}
                  stroke={getEmotionColor(emotion)}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-400">
          <Heart className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No emotional data yet</p>
          <p className="text-sm mt-2">Your emotional journey will appear here as you talk with Yubi</p>
        </div>
      )}
      
      {/* Insights section */}
      {emotionData.length > 0 && (
        <div className="mt-6 pt-4 border-t border-[rgba(56,182,255,0.1)]">
          <h3 className="text-sm font-medium text-gray-300 mb-2">Emotional Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="p-3 rounded-lg bg-[rgba(56,182,255,0.05)] border-l-2 border-[#38b6ff]">
              <p className="text-sm text-gray-300">
                {generateInsight(emotionData, selectedEmotions, metricType)}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-[rgba(56,182,255,0.05)] border-l-2 border-[#ff37a6]">
              <p className="text-sm text-gray-300">
                {generateTrend(emotionData, selectedEmotions, metricType)}
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Emotion selection chips */}
      {emotionData.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {availableEmotions.map(emotion => {
            // Get the color for this emotion
            const color = getEmotionColor(emotion);
            
            return (
              <button
                key={emotion}
                onClick={() => {
                  if (selectedEmotions.includes(emotion)) {
                    setSelectedEmotions(selectedEmotions.filter(e => e !== emotion));
                  } else {
                    setSelectedEmotions([...selectedEmotions, emotion]);
                  }
                }}
                className={`text-xs px-3 py-1.5 rounded-full transition-all duration-300 flex items-center gap-1
                  ${selectedEmotions.includes(emotion) 
                    ? `bg-opacity-20 border border-opacity-40` 
                    : 'bg-[rgba(56,182,255,0.05)] text-gray-400 border border-transparent'}`}
                style={{
                  backgroundColor: selectedEmotions.includes(emotion) ? `${color}20` : undefined,
                  borderColor: selectedEmotions.includes(emotion) ? `${color}40` : undefined,
                  color: selectedEmotions.includes(emotion) ? color : undefined
                }}
              >
                {selectedEmotions.includes(emotion) && <Check className="w-3 h-3" />}
                {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
              </button>
            );
          })}
        </div>
      )}
      
      {/* Key emotional moments */}
      {emotionData.length > 0 && (
        <div className="mt-6 pt-4 border-t border-[rgba(56,182,255,0.1)]">
          <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-[#ff37a6]" />
            Key Emotional Moments
          </h3>
          <div className="space-y-3">
            {[
              // Generate some key moments based on the data
              ...selectedEmotions.map(emotion => {
                // Find the highest intensity for this emotion
                const highestPoint = [...emotionData]
                  .sort((a, b) => {
                    const aVal = a[`${emotion}_${metricType}`] as number || 0;
                    const bVal = b[`${emotion}_${metricType}`] as number || 0;
                    return bVal - aVal;
                  })[0];
                
                if (!highestPoint || !highestPoint[`${emotion}_${metricType}`]) return null;
                
                return {
                  emotion: emotion,
                  date: highestPoint.date,
                  intensity: highestPoint[`${emotion}_${metricType}`] as number,
                  note: `Peak ${emotion} moment with ${metricType === 'intensity' ? 'high emotional intensity' : 
                    metricType === 'pace' ? 'rapid speech pace' : 
                    metricType === 'volume' ? 'elevated voice volume' : 'distinctive emotional tone'}.`
                };
              }).filter(Boolean)
            ].map((moment, index) => {
              if (!moment) return null;
              const color = getEmotionColor(moment.emotion);
              
              return (
                <div 
                  key={index}
                  className="p-3 rounded-lg bg-[rgba(56,182,255,0.03)] border-l-2"
                  style={{ borderColor: color }}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-sm" style={{ color }}>
                        {moment.emotion.charAt(0).toUpperCase() + moment.emotion.slice(1)}
                        <span className="text-xs ml-1 text-gray-400">
                          (Intensity: {moment.intensity.toFixed(1)}/10)
                        </span>
                      </span>
                    </div>
                    <span className="text-xs text-gray-400">{moment.date}</span>
                  </div>
                  <p className="text-sm text-gray-300 mt-1">{moment.note}</p>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </Card>
  );
}

function AchievementsCard() {
  const achievements = [
    { title: "3 Day Streak", icon: <Timer />, color: "#38b6ff" },
    { title: "Deep Reflection", icon: <Brain />, color: "#a137ff" },
    { title: "Regular Explorer", icon: <Sparkles />, color: "#ff37a6" },
  ]

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#ff37a6]">
        <Award className="w-5 h-5" />
        Recent Achievements
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {achievements.map((achievement) => (
          <motion.div
            key={achievement.title}
            whileHover={{ scale: 1.05 }}
            className="p-6 rounded-lg bg-[rgba(56,182,255,0.03)] border border-[rgba(56,182,255,0.1)]
                       hover:border-[rgba(56,182,255,0.3)] transition-all duration-300"
            style={{ borderColor: achievement.color + "40" }}
          >
            <div className="mb-3" style={{ color: achievement.color }}>
              {achievement.icon}
            </div>
            <p className="text-sm" style={{ color: achievement.color }}>
              {achievement.title}
            </p>
          </motion.div>
        ))}
      </div>
    </Card>
  )
}

function ActivityOverviewCard() {
  const activities = [
    {
      title: "Deep Reflection Session",
      time: "2 hours ago",
      score: 98,
      icon: <Sparkles className="w-5 h-5 text-[#38b6ff]" />,
    },
    {
      title: "Weekly Goal Achieved",
      time: "Yesterday",
      score: 85,
      icon: <Award className="w-5 h-5 text-[#a137ff]" />,
    },
    {
      title: "Breakthrough Moment",
      time: "2 days ago",
      score: 92,
      icon: <Brain className="w-5 h-5 text-[#ff37a6]" />,
    },
  ]

  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                   hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#38b6ff]">
        <Timer className="w-5 h-5" />
        Activity Overview
      </h2>
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-4 rounded-lg bg-[rgba(56,182,255,0.05)] 
                       hover:bg-[rgba(56,182,255,0.1)] transition-all duration-300"
          >
            <div className="flex items-center gap-4">
              <div className="p-2 rounded-lg bg-[rgba(56,182,255,0.1)]">
                {activity.icon}
              </div>
              <div>
                <h3 className="text-white font-light">{activity.title}</h3>
                <p className="text-sm text-gray-400">{activity.time}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-light text-[#38b6ff]">
                {activity.score}
              </div>
              <div className="text-sm text-gray-400">points</div>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-6 pt-6 border-t border-[rgba(56,182,255,0.1)]">
        <div className="flex justify-between items-center">
          <div className="text-gray-400">Weekly Progress</div>
          <div className="text-[#38b6ff]">275/300</div>
        </div>
        <div className="mt-2 h-2 bg-[rgba(56,182,255,0.1)] rounded-full overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-[#38b6ff] to-[#a137ff] rounded-full"
            style={{ width: '92%' }}
          />
        </div>
      </div>
    </Card>
  )
}

// Helper function remains the same
function formatQuestionId(id: string): string {
  const questionMap: Record<string, string> = {
    name: "What's your name?",
    feeling: "How are you really feeling?",
    wish: "What do you wish people asked you more?",
    dream: "What's one dream you've had?",
    fear: "What scares you but matters anyway?",
    remember: "What should Yubi remember about you?",
  }

  return questionMap[id] || id
}

function ConversationHistoryCard({ conversations, setSelectedConversation }: { 
  conversations: Conversation[];
  setSelectedConversation: (conv: Conversation) => void;
}) {
  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#38b6ff]">
        <MessageCircle className="w-5 h-5" />
        Conversation History
      </h2>
      <div className="space-y-4">
        {conversations.length > 0 ? (
          conversations.map((conv) => (
            <div
              key={conv.id}
              onClick={() => setSelectedConversation(conv)}
              className="p-4 rounded-lg bg-[rgba(56,182,255,0.03)] border-l-2 border-[#38b6ff] 
                         cursor-pointer hover:bg-[rgba(56,182,255,0.08)] transition-all duration-300"
            >
              <div className="flex justify-between items-start mb-2">
                <span className="text-[#38b6ff] text-sm">
                  {new Date(conv.created_at).toLocaleDateString()}
                </span>
                <Badge variant="outline" className="bg-[rgba(56,182,255,0.1)] text-[#38b6ff]">
                  {conv.conversation_type}
                </Badge>
              </div>
              <p className="text-white text-sm mb-2">{conv.summary}</p>
              <div className="flex flex-wrap gap-2 mt-2">
                {conv.emotional_markers.slice(0, 3).map((emotion, i) => (
                  <span key={i} className="text-xs px-2 py-1 rounded-full bg-[rgba(161,55,255,0.1)] text-[#a137ff]">
                    {emotion}
                  </span>
                ))}
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-400">
            <MessageCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
            <p>No conversations yet</p>
            <p className="text-sm mt-2">Start a call with Yubi to see your history here</p>
          </div>
        )}
      </div>
    </Card>
  );
}

function SocialMediaCard() {
  const [integrating, setIntegrating] = useState<string | null>(null);
  
  const handleSocialClick = (platform: string) => {
    setIntegrating(platform);
    
    // Show integration message for 2 seconds
    setTimeout(() => {
      setIntegrating(null);
    }, 2000);
  };
  
  return (
    <Card className="p-6 bg-[rgba(10,17,40,0.4)] backdrop-blur-xl border-[rgba(56,182,255,0.1)]
                     hover:border-[rgba(56,182,255,0.2)] transition-all duration-300">
      <h2 className="text-xl font-light mb-6 flex items-center gap-2 text-[#38b6ff]">
        <MessageCircle className="w-5 h-5" />
        Connect Your Social Media
      </h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[
          { name: "Instagram", icon: <InstagramIcon className="w-6 h-6" />, color: "#E1306C" },
          { name: "TikTok", icon: <TikTokIcon className="w-6 h-6" />, color: "#000000" },
          { name: "Twitter", icon: <TwitterIcon className="w-6 h-6" />, color: "#1DA1F2" },
          { name: "Spotify", icon: <SpotifyIcon className="w-6 h-6" />, color: "#1DB954" }
        ].map((platform) => (
          <motion.div
            key={platform.name}
            whileHover={{ scale: 1.05 }}
            onClick={() => handleSocialClick(platform.name)}
            className="p-4 rounded-lg bg-[rgba(56,182,255,0.03)] border border-[rgba(56,182,255,0.1)]
                      hover:border-[rgba(56,182,255,0.3)] transition-all duration-300 flex flex-col items-center gap-3
                      cursor-pointer relative"
          >
            {integrating === platform.name ? (
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-[rgba(10,17,40,0.9)] rounded-lg">
                <Loader2 className="w-5 h-5 animate-spin mb-2" style={{ color: platform.color }} />
                <p className="text-xs text-center text-gray-300">Still integrating...</p>
              </div>
            ) : null}
            <div style={{ color: platform.color }}>{platform.icon}</div>
            <p className="text-sm text-gray-300">{platform.name}</p>
          </motion.div>
        ))}
      </div>
    </Card>
  )
}

// Helper function to generate insights based on emotional data
function generateInsight(data: EmotionalDataPoint[], emotions: string[], metricType: string): string {
  if (data.length === 0 || emotions.length === 0) {
    return "Not enough data to generate insights yet.";
  }
  
  // Find the most common emotion
  const emotionCounts: Record<string, number> = {};
  emotions.forEach(emotion => {
    emotionCounts[emotion] = data.filter((d: EmotionalDataPoint) => d[`${emotion}_${metricType}`] !== undefined).length;
  });
  
  const mostCommonEmotion = Object.entries(emotionCounts)
    .sort((a, b) => b[1] - a[1])[0][0];
  
  // Calculate average value for the most common emotion
  const emotionValues = data
    .filter((d: EmotionalDataPoint) => d[`${mostCommonEmotion}_${metricType}`] !== undefined)
    .map((d: EmotionalDataPoint) => Number(d[`${mostCommonEmotion}_${metricType}`]));
  
  const avgValue = emotionValues.reduce((sum: number, val: number) => sum + val, 0) / emotionValues.length;
  
  // Generate insight based on metric type
  switch (metricType) {
    case 'intensity':
      return `Your ${mostCommonEmotion} experiences tend to have an average intensity of ${avgValue.toFixed(1)}/10, making it your most consistently tracked emotion.`;
    case 'pace':
      return `When experiencing ${mostCommonEmotion}, your speech pace averages ${avgValue.toFixed(0)} words per minute, which is ${avgValue > 150 ? 'faster than' : avgValue < 100 ? 'slower than' : 'close to'} typical conversation.`;
    case 'volume':
      return `During moments of ${mostCommonEmotion}, your voice volume averages ${avgValue.toFixed(0)}/100, suggesting ${avgValue > 70 ? 'higher energy' : avgValue < 40 ? 'more subdued' : 'balanced'} expression.`;
    case 'tone':
      return `Your emotional tone during ${mostCommonEmotion} averages ${avgValue.toFixed(2)} on a scale from -1 to 1, indicating a ${avgValue > 0.3 ? 'positive' : avgValue < -0.3 ? 'negative' : 'neutral'} expression pattern.`;
    default:
      return "Explore your emotional patterns by selecting different metrics and time ranges.";
  }
}

// Helper function to generate trend analysis
function generateTrend(data: EmotionalDataPoint[], emotions: string[], metricType: string): string {
  if (data.length < 3 || emotions.length === 0) {
    return "Continue conversations with Yubi to reveal your emotional trends over time.";
  }
  
  // Get the first selected emotion with enough data points
  let targetEmotion = '';
  for (const emotion of emotions) {
    const points = data.filter(d => d[`${emotion}_${metricType}`] !== undefined);
    if (points.length >= 3) {
      targetEmotion = emotion;
      break;
    }
  }
  
  if (!targetEmotion) {
    return "Select emotions with more data points to see trend analysis.";
  }
  
  // Get values for the target emotion
  const values = data
    .filter(d => d[`${targetEmotion}_${metricType}`] !== undefined)
    .map(d => ({ 
      value: Number(d[`${targetEmotion}_${metricType}`]), 
      date: d.date 
    }));
  
  // Calculate trend (simple linear regression)
  const n = values.length;
  const indices = Array.from({ length: n }, (_, i) => i);
  const sumX = indices.reduce((sum, i) => sum + i, 0);
  const sumY = values.reduce((sum, v) => sum + v.value, 0);
  const sumXY = indices.reduce((sum, i) => sum + i * values[i].value, 0);
  const sumXX = indices.reduce((sum, i) => sum + i * i, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  
  // Calculate volatility (standard deviation)
  const mean = sumY / n;
  const variance = values.reduce((sum, v) => sum + Math.pow(v.value - mean, 2), 0) / n;
  const volatility = Math.sqrt(variance);
  
  // Generate trend message based on metric type and trend direction
  let trendMessage = "";
  
  if (Math.abs(slope) < 0.05) {
    trendMessage = `Your ${targetEmotion} levels have remained relatively stable`;
  } else if (slope > 0) {
    trendMessage = `Your ${targetEmotion} levels have been increasing`;
  } else {
    trendMessage = `Your ${targetEmotion} levels have been decreasing`;
  }
  
  // Add volatility insight
  if (volatility > 2 && metricType === 'intensity') {
    trendMessage += ` with significant fluctuations (volatility: ${volatility.toFixed(1)})`;
  } else if (volatility > 20 && metricType === 'pace') {
    trendMessage += ` with notable variations in speech pace`;
  } else if (volatility > 15 && metricType === 'volume') {
    trendMessage += ` with varying levels of vocal expression`;
  } else if (volatility > 0.3 && metricType === 'tone') {
    trendMessage += ` with shifts between positive and negative expressions`;
  } else {
    trendMessage += ` in a consistent pattern`;
  }
  
  // Add time context
  const firstDate = new Date(values[0].date);
  const lastDate = new Date(values[values.length - 1].date);
  const daysDiff = Math.round((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24));
  
  if (daysDiff <= 7) {
    trendMessage += ` over the past week.`;
  } else if (daysDiff <= 30) {
    trendMessage += ` over the past month.`;
  } else {
    trendMessage += ` over the past ${daysDiff} days.`;
  }
  
  return trendMessage;
}

// Generate mock emotional data for testing
function generateMockEmotionalData(): EmotionalDataPoint[] {
  // Include the custom emotions we've seen in the database
  const emotions = ['joy', 'sadness', 'anger', 'fear', 'openness', 'freedom', 'good', 'trust'];
  const days = 14; // Two weeks of data
  
  // Create smoother, more realistic patterns
  const joyTrend = Array.from({ length: days }, (_, i) => 5 + 3 * Math.sin(i / 3) + Math.random() * 2);
  const sadnessTrend = Array.from({ length: days }, (_, i) => 4 + 2 * Math.cos(i / 2.5) + Math.random() * 1.5);
  const angerTrend = Array.from({ length: days }, (_, i) => 3 + Math.sin(i / 2) * Math.cos(i) + Math.random() * 2);
  const fearTrend = Array.from({ length: days }, (_, i) => 2 + 1.5 * Math.sin(i / 4 + 2) + Math.random());
  
  return Array.from({ length: days }).map((_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (days - i - 1));
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    
    const dataPoint: EmotionalDataPoint = { date: formattedDate };
    
    // Add values with realistic patterns for each emotion
    emotions.forEach(emotion => {
      // Only include some emotions for each day (more realistic)
      if (Math.random() > 0.2) {
        // Use the trend arrays for main emotions, random for others
        let intensity = 0;
        switch(emotion) {
          case 'joy': intensity = joyTrend[i]; break;
          case 'sadness': intensity = sadnessTrend[i]; break;
          case 'anger': intensity = angerTrend[i]; break;
          case 'fear': intensity = fearTrend[i]; break;
          default: intensity = Math.floor(Math.random() * 6) + 2; // 2-8 range
        }
        
        // Ensure intensity is within bounds
        intensity = Math.min(10, Math.max(1, intensity));
        
        dataPoint[`${emotion}_intensity`] = intensity;
        dataPoint[`${emotion}_pace`] = 100 + intensity * 10 + Math.floor(Math.random() * 20);
        dataPoint[`${emotion}_volume`] = 40 + intensity * 5 + Math.floor(Math.random() * 10);
        dataPoint[`${emotion}_tone`] = (emotion === 'joy' || emotion === 'trust' || emotion === 'openness' || emotion === 'good') 
          ? (0.2 + Math.random() * 0.6) // Positive emotions have positive tone
          : (emotion === 'sadness' || emotion === 'fear') 
            ? (-0.7 + Math.random() * 0.4) // Negative emotions have negative tone
            : (Math.random() * 1.6 - 0.8); // Others vary
      }
    });
    
    return dataPoint;
  });
}

// Format emotional data from Supabase to chart format
function formatEmotionalData(data: Array<any>): EmotionalDataPoint[] {
  // Normalize emotion names (lowercase everything)
  const normalizedData = data.map(item => ({
    ...item,
    emotion: item.emotion?.toLowerCase() // Normalize to lowercase
  }));
  
  // Group data by date
  const groupedByDate = normalizedData.reduce<Record<string, Array<any>>>((acc, item) => {
    // Format date to match chart format
    const date = new Date(item.timestamp);
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    
    if (!acc[formattedDate]) {
      acc[formattedDate] = [];
    }
    
    acc[formattedDate].push(item);
    return acc;
  }, {});
  
  // Convert grouped data to chart format
  return Object.entries(groupedByDate).map(([date, items]) => {
    // Create base data point with date
    const dataPoint: EmotionalDataPoint = { date };
    
    // Calculate average intensity for each emotion on this date
    const emotionIntensities: Record<string, number[]> = {};
    
    items.forEach(item => {
      if (item.emotion) {
        const key = `${item.emotion}_intensity`;
        if (!emotionIntensities[item.emotion]) {
          emotionIntensities[item.emotion] = [];
        }
        emotionIntensities[item.emotion].push(item.intensity || 0);
      }
    });
    
    // Add average intensity for each emotion to data point
    Object.entries(emotionIntensities).forEach(([emotion, intensities]) => {
      const avgIntensity = intensities.reduce((sum, val) => sum + val, 0) / intensities.length;
      dataPoint[`${emotion}_intensity`] = avgIntensity;
    });
    
    return dataPoint;
  }).sort((a, b) => {
    // Ensure data is sorted by date
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    return dateA.getTime() - dateB.getTime();
  });
}

// Generate insight highlight based on data
function generateInsightHighlight(data: EmotionalDataPoint[]): string | null {
  if (data.length === 0) return null;
  
  // Create a default set of emotions to analyze
  const defaultEmotions = ['joy', 'sadness', 'anger', 'fear'];
  
  // Generate a basic insight using the intensity metric
  return generateInsight(data, defaultEmotions, 'intensity');
}
